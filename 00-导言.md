# 导言

## 为什么要写这本书

如果你正在阅读这本书，说明你已经听说过 LangGraph，或者正在寻找一个能够构建复杂 AI Agent 的工具。

在 AI 快速发展的今天，我们已经从简单的"问答机器人"进化到了需要"会思考、会使用工具、能协作"的智能体。传统的 LLM 调用方式——发送一个 prompt，得到一个回复——已经无法满足复杂业务场景的需求。

我们需要的是：
- 能够记住对话历史的机器人
- 能够调用外部工具（搜索、计算、API）的助手  
- 能够在关键时刻"举手求助"的智能体
- 多个 AI 角色协作完成复杂任务的系统

LangGraph 正是为了解决这些问题而生。但是，官方文档虽然详细，却往往让初学者望而却步。概念抽象、示例分散、缺乏系统性的学习路径。

这就是我写这本书的原因：**用最直白的语言，最实用的例子，带你从零开始掌握 LangGraph**。

## 适合谁读

这本书适合以下读者：

**如果你是...**
- **Python 开发者**：有基本的 Python 编程经验，想要构建 AI 应用
- **AI 应用开发者**：已经使用过 OpenAI API 或其他 LLM，想要构建更复杂的智能体
- **产品经理/技术负责人**：想要了解 AI Agent 的技术实现，评估技术方案
- **学生/研究者**：对 AI Agent 架构感兴趣，想要深入理解其工作原理

**你需要具备：**
- 基础的 Python 编程能力（会写函数、类，理解异步编程更佳）
- 对大语言模型有基本了解（知道什么是 prompt、token）
- 有使用过 API 的经验（REST API、JSON 数据格式）

**你不需要：**
- 深厚的机器学习背景
- 复杂的数学知识
- 分布式系统经验（虽然有更好）

## 如何一边阅读一边动手

这本书的最大特色是**边学边做**。每个概念都配有可运行的代码示例，每个章节都有动手练习。

**推荐的学习方式：**

1. **准备环境**：按照第2章的指引搭建开发环境
2. **跟着敲代码**：不要只看，一定要亲自运行每个示例
3. **修改参数试试**：改改 prompt、换换模型，看看效果如何变化
4. **做章节练习**：每章末尾的练习题帮你巩固知识
5. **完成综合项目**：第11章的项目从简单到复杂，循序渐进



**章节难度：**
- 第1-4章：**基础必读**，所有读者都应该掌握
- 第5-7章：**核心进阶**，构建实用应用的关键
- 第8-10章：**高级应用**，生产环境部署必备
- 第11章：**综合实战**，检验学习成果
- 第12章：**问题锦囊**，遇到问题时的救命稻草

## 学习建议

**对于初学者：**
按顺序阅读，不要跳章。遇到不理解的概念，先记下来，往往在后续章节会有详细解释。

**对于有经验的开发者：**
可以快速浏览第1-2章，重点关注第3章的核心概念，然后根据需要选择性阅读后续章节。

**对于急于上手的读者：**
直接从第2章开始，跑通第一个例子后，再回头补充理论知识。

**最重要的建议：**
不要只是看，一定要动手！LangGraph 的精髓在于实践，只有亲自构建过 Agent，你才能真正理解其设计哲学。

---

准备好了吗？让我们开始这段 LangGraph 的学习之旅！

在下一章中，我们将正式认识 LangGraph，了解它在 AI 开发生态中的独特定位。