# 第9章 流式输出与实时反馈

想象一下，当你问 AI 一个复杂问题时，不是等待很久后突然得到一个完整答案，而是能够实时看到 AI 的"思考过程"——就像看到一个人边思考边说话一样。这就是流式输出的魅力。

在这一章中，我们将学习如何在 LangGraph 中实现流式输出和实时反馈，让用户体验更加流畅和透明。

## 9.1 逐 token 流式返回：提升用户体验

### 什么是流式输出？

传统的 AI 交互模式：
```
用户提问 → 等待... → 完整答案出现
```

流式输出模式：
```
用户提问 → AI开始回答 → 逐字显示 → 完整答案
```

流式输出的优势：
- **更好的用户体验**：减少等待焦虑
- **实时反馈**：用户可以提前看到回答方向
- **可中断性**：用户可以随时打断不满意的回答
- **透明度**：展示 AI 的思考过程

### LangGraph 中的流式输出

```python
import os
import time
import asyncio
from typing import AsyncGenerator
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict
from typing import Annotated
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage

class StreamingState(TypedDict):
    messages: Annotated[list, add_messages]
    streaming_content: str
    current_step: str
    progress: float

def create_streaming_chatbot():
    """创建流式输出聊天机器人"""
    
    def streaming_response_node(state: StreamingState):
        """流式响应节点"""
        user_message = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if not api_key:
            # 模拟流式输出
            mock_response = "我来为您解答这个问题。首先，我需要分析问题的核心要点。然后，我会基于相关知识提供详细的解答。"
            streaming_content = ""
            
            print("\n🤖 AI回答: ", end="", flush=True)
            for char in mock_response:
                streaming_content += char
                print(char, end="", flush=True)
                time.sleep(0.05)  # 模拟打字效果
            print()  # 换行
            
            return {
                "messages": [AIMessage(content=streaming_content)],
                "streaming_content": streaming_content,
                "current_step": "response_completed"
            }
        
        try:
            from langchain_community.chat_models import ChatZhipuAI
            
            # 使用支持流式输出的 LLM
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.7,
                api_key=api_key,
                timeout=35,
                streaming=True  # 启用流式输出
            )
            
            system_prompt = """
你是一个友好的AI助手，请详细回答用户的问题。
在回答时，请分步骤思考并逐步展开你的回答。
"""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_message)
            ]
            
            # 收集流式输出
            streaming_content = ""
            print("\n🤖 AI回答: ", end="", flush=True)
            
            for chunk in llm.stream(messages):
                if hasattr(chunk, 'content') and chunk.content:
                    streaming_content += chunk.content
                    # 实时输出到控制台
                    print(chunk.content, end='', flush=True)
            
            print()  # 换行
            
            return {
                "messages": [AIMessage(content=streaming_content)],
                "streaming_content": streaming_content,
                "current_step": "response_completed"
            }
            
        except Exception as e:
            print(f"\n⚠️ 流式输出失败: {e}")
            fallback_response = "抱歉，网络繁忙，请稍后重试。"
            return {
                "messages": [AIMessage(content=fallback_response)],
                "streaming_content": fallback_response,
                "current_step": "error"
            }
    
    # 构建图
    graph = StateGraph(StreamingState)
    graph.add_node("streaming_response", streaming_response_node)
    graph.add_edge(START, "streaming_response")
    graph.add_edge("streaming_response", END)
    
    return graph.compile()

async def create_async_streaming_chatbot():
    """创建异步流式聊天机器人"""
    
    async def async_streaming_node(state: StreamingState):
        """异步流式节点"""
        user_message = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if not api_key:
            # 模拟异步流式输出
            mock_chunks = [
                "我来", "为您", "解答", "这个", "问题。",
                "首先，", "我需要", "分析", "问题的", "核心", "要点。",
                "然后，", "我会", "基于", "相关", "知识", "提供", "详细的", "解答。"
            ]
            
            streaming_content = ""
            for chunk in mock_chunks:
                streaming_content += chunk
                await asyncio.sleep(0.1)  # 模拟异步处理
                
                yield {
                    "partial_content": chunk,
                    "total_content": streaming_content,
                    "is_complete": False
                }
            
            # 最终结果
            yield {
                "messages": [AIMessage(content=streaming_content)],
                "streaming_content": streaming_content,
                "current_step": "completed",
                "is_complete": True
            }
            return
        
        try:
            from langchain_community.chat_models import ChatZhipuAI
            
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.7,
                api_key=api_key,
                timeout=35,
                streaming=True
            )
            
            messages = [
                SystemMessage(content="你是一个AI助手，请详细回答问题。"),
                HumanMessage(content=user_message)
            ]
            
            streaming_content = ""
            async for chunk in llm.astream(messages):
                if hasattr(chunk, 'content') and chunk.content:
                    streaming_content += chunk.content
                    # 异步处理每个 token
                    await asyncio.sleep(0.01)  # 模拟处理时间
                    
                    yield {
                        "partial_content": chunk.content,
                        "total_content": streaming_content,
                        "is_complete": False
                    }
            
            # 最终结果
            yield {
                "messages": [AIMessage(content=streaming_content)],
                "streaming_content": streaming_content,
                "current_step": "completed",
                "is_complete": True
            }
            
        except Exception as e:
            yield {
                "messages": [AIMessage(content=f"异步流式处理失败: {str(e)[:50]}")],
                "streaming_content": f"处理失败: {str(e)[:50]}",
                "current_step": "error",
                "is_complete": True
            }
    
    return async_streaming_node
```

### 实时进度显示

```python
class ProgressTrackingState(TypedDict):
    messages: Annotated[list, add_messages]
    current_task: str
    progress_steps: list
    completed_steps: int
    total_steps: int
    estimated_time: float

def create_progress_tracking_chatbot():
    """创建带进度跟踪的聊天机器人"""
    
    def task_analyzer_node(state: ProgressTrackingState):
        """任务分析节点"""
        user_request = state["messages"][-1].content
        
        # 分析任务复杂度并估算步骤
        if any(keyword in user_request for keyword in ["复杂", "详细", "深入", "全面"]):
            steps = ["分析问题", "收集信息", "整理思路", "生成回答", "检查完善"]
            estimated_time = 15.0
        elif any(keyword in user_request for keyword in ["简单", "快速", "简要", "概括"]):
            steps = ["理解问题", "生成回答"]
            estimated_time = 5.0
        else:
            steps = ["分析问题", "生成回答", "完善答案"]
            estimated_time = 10.0
        
        return {
            "current_task": "任务分析完成",
            "progress_steps": steps,
            "total_steps": len(steps),
            "completed_steps": 1,
            "estimated_time": estimated_time
        }
    
    def progressive_response_node(state: ProgressTrackingState):
        """渐进式响应节点"""
        steps = state.get("progress_steps", [])
        user_message = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        # 模拟分步骤处理
        step_responses = []
        
        for i, step in enumerate(steps):
            print(f"\n🔄 正在执行: {step} ({i+1}/{len(steps)})")
            
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    
                    llm = ChatZhipuAI(
                        model="glm-4.5", 
                        temperature=0.6, 
                        api_key=api_key, 
                        timeout=35
                    )
                    
                    step_prompt = f"""
当前步骤：{step}
用户问题：{user_message}
之前的处理结果：{' '.join(step_responses)}

请完成当前步骤的处理，提供简洁而有用的内容。
"""
                    
                    response = llm.invoke([HumanMessage(content=step_prompt)])
                    step_response = response.content
                    
                except Exception as e:
                    print(f"⚠️ 步骤处理失败: {e}")
                    step_response = f"完成{step}..."
            else:
                # 模拟处理结果
                mock_responses = {
                    "分析问题": f"已分析问题：{user_message[:20]}...",
                    "收集信息": "已收集相关背景信息和知识点",
                    "整理思路": "已整理逻辑结构和回答要点",
                    "生成回答": "正在生成详细的回答内容",
                    "检查完善": "已检查回答的完整性和准确性",
                    "理解问题": f"已理解问题要点：{user_message[:30]}...",
                    "完善答案": "已完善回答内容和格式"
                }
                step_response = mock_responses.get(step, f"完成{step}")
            
            step_responses.append(step_response)
            
            # 更新进度
            progress = (i + 1) / len(steps) * 100
            print(f"✅ 进度: {progress:.1f}% - {step_response[:50]}...")
            
            # 模拟处理时间
            time.sleep(0.8)
        
        # 整合最终回答
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.5, api_key=api_key, timeout=35)
                
                integration_prompt = f"""
基于以下分步处理结果，生成一个完整、连贯的最终回答：

用户问题：{user_message}

处理步骤和结果：
{chr(10).join([f"{i+1}. {steps[i]}: {resp}" for i, resp in enumerate(step_responses)])}

请整合以上内容，提供一个清晰、完整的最终答案。
"""
                
                final_response = llm.invoke([HumanMessage(content=integration_prompt)])
                final_content = final_response.content
                
            except Exception as e:
                print(f"⚠️ 整合回答失败: {e}")
                final_content = f"基于{len(steps)}个步骤的分析，为您提供以下回答：\n\n" + "\n".join(step_responses)
        else:
            final_content = f"经过{len(steps)}个步骤的细致分析，为您提供完整的解答。"
        
        return {
            "messages": [AIMessage(content=final_content)],
            "current_task": "任务完成",
            "completed_steps": len(steps)
        }
    
    # 构建图
    graph = StateGraph(ProgressTrackingState)
    graph.add_node("task_analyzer", task_analyzer_node)
    graph.add_node("progressive_response", progressive_response_node)
    
    graph.add_edge(START, "task_analyzer")
    graph.add_edge("task_analyzer", "progressive_response")
    graph.add_edge("progressive_response", END)
    
    return graph.compile()
```

## 9.2 中间步骤可视化：洞察 Agent 思考过程

### 思考过程可视化

让用户看到 AI 的"思考过程"能够增加信任度和透明度：

```python
class ThinkingState(TypedDict):
    messages: Annotated[list, add_messages]
    thinking_steps: list
    current_thought: str
    reasoning_chain: list
    confidence_scores: list

def create_thinking_process_chatbot():
    """创建思考过程可视化聊天机器人"""
    
    def reasoning_node(state: ThinkingState):
        """推理节点"""
        user_question = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        # 分步骤思考
        thinking_steps = [
            "🤔 理解问题",
            "📚 回忆相关知识", 
            "🔍 分析关键要素",
            "💡 形成初步想法",
            "🛠️ 完善解决方案",
            "✅ 验证答案合理性"
        ]
        
        reasoning_chain = []
        confidence_scores = []
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.4, api_key=api_key, timeout=35)
                
                for step in thinking_steps:
                    print(f"\n{step}")
                    
                    step_prompt = f"""
当前思考步骤：{step}
用户问题：{user_question}
之前的思考过程：{reasoning_chain}

请完成这一步的思考，并在最后一行给出你的置信度(0.1-1.0)。
格式：
思考内容...
置信度: 0.85
"""
                    
                    response = llm.invoke([HumanMessage(content=step_prompt)])
                    content = response.content
                    
                    # 解析置信度
                    try:
                        lines = content.strip().split('\n')
                        confidence_line = [line for line in lines if '置信度' in line or 'confidence' in line.lower()]
                        if confidence_line:
                            confidence_text = confidence_line[0]
                            import re
                            confidence_match = re.search(r'[\d.]+', confidence_text)
                            confidence_score = float(confidence_match.group()) if confidence_match else 0.7
                        else:
                            confidence_score = 0.7
                        
                        # 移除置信度行，保留思考内容
                        thought_content = '\n'.join([line for line in lines if '置信度' not in line and 'confidence' not in line.lower()]).strip()
                        
                    except:
                        thought_content = content.strip()
                        confidence_score = 0.7
                    
                    reasoning_chain.append({
                        "step": step,
                        "thought": thought_content,
                        "confidence": confidence_score
                    })
                    confidence_scores.append(confidence_score)
                    
                    # 实时显示思考过程
                    print(f"💭 {thought_content[:100]}...")
                    print(f"📊 置信度: {confidence_score:.2f}")
                    time.sleep(0.5)  # 模拟思考时间
                
                # 生成最终答案
                final_prompt = f"""
基于以下深入的思考过程，给出最终答案：

用户问题：{user_question}

思考过程：
{chr(10).join([f"{i+1}. {step['step']}: {step['thought']}" for i, step in enumerate(reasoning_chain)])}

请给出清晰、完整、有条理的最终答案。
"""
                
                final_response = llm.invoke([HumanMessage(content=final_prompt)])
                final_content = final_response.content
                
            except Exception as e:
                print(f"⚠️ 思考过程分析失败: {e}")
                # 模拟思考过程
                for step in thinking_steps:
                    print(f"\n{step}")
                    mock_thoughts = {
                        "🤔 理解问题": f"分析问题的核心要素：{user_question[:30]}...",
                        "📚 回忆相关知识": "检索相关背景知识和经验",
                        "🔍 分析关键要素": "识别问题的关键要素和约束条件",
                        "💡 形成初步想法": "基于分析形成初步解决思路",
                        "🛠️ 完善解决方案": "细化和完善解决方案的具体步骤",
                        "✅ 验证答案合理性": "检验方案的可行性和合理性"
                    }
                    
                    thought = mock_thoughts.get(step, "思考中...")
                    confidence = 0.75 + (len(reasoning_chain) * 0.05)  # 递增置信度
                    
                    reasoning_chain.append({
                        "step": step,
                        "thought": thought,
                        "confidence": confidence
                    })
                    confidence_scores.append(confidence)
                    
                    print(f"💭 {thought}")
                    print(f"📊 置信度: {confidence:.2f}")
                    time.sleep(0.3)
                
                final_content = f"经过6个维度的深入思考，为您提供完整的解答。"
        else:
            # 完全模拟模式
            for step in thinking_steps:
                print(f"\n{step}")
                mock_thoughts = {
                    "🤔 理解问题": f"正在分析问题：{user_question[:30]}...",
                    "📚 回忆相关知识": "检索相关知识库和经验数据",
                    "🔍 分析关键要素": "识别核心要素和影响因素",
                    "💡 形成初步想法": "构建初步解决方案框架",
                    "🛠️ 完善解决方案": "优化和细化具体实施步骤",
                    "✅ 验证答案合理性": "验证方案的可行性和有效性"
                }
                
                thought = mock_thoughts.get(step, "深度思考中...")
                confidence = 0.7 + (len(reasoning_chain) * 0.04)
                
                reasoning_chain.append({
                    "step": step,
                    "thought": thought,
                    "confidence": min(confidence, 0.95)
                })
                confidence_scores.append(min(confidence, 0.95))
                
                print(f"💭 {thought}")
                print(f"📊 置信度: {min(confidence, 0.95):.2f}")
                time.sleep(0.3)
            
            final_content = "基于系统性的6步思考过程，为您提供经过深度分析的完整解答。"
        
        return {
            "messages": [AIMessage(content=final_content)],
            "thinking_steps": thinking_steps,
            "reasoning_chain": reasoning_chain,
            "confidence_scores": confidence_scores
        }
    
    # 构建图
    graph = StateGraph(ThinkingState)
    graph.add_node("reasoning", reasoning_node)
    graph.add_edge(START, "reasoning")
    graph.add_edge("reasoning", END)
    
    return graph.compile()
```

### 决策树可视化

```python
class DecisionState(TypedDict):
    messages: Annotated[list, add_messages]
    decision_tree: dict
    current_path: list
    alternatives: list
    final_decision: str

def create_decision_tree_chatbot():
    """创建决策树可视化聊天机器人"""
    
    def decision_tree_node(state: DecisionState):
        """决策树节点"""
        user_question = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        # 构建决策树
        decision_tree = {
            "root": {
                "question": user_question,
                "options": []
            }
        }
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.5, api_key=api_key, timeout=35)
                
                # 生成决策选项
                options_prompt = f"""
用户问题：{user_question}

请分析这个问题，提供3-5个可能的解决方向或思考角度。
每个选项请按以下格式输出（每行一个选项）：
选项名称|简短描述|优先级(1-5)

例如：
技术方案A|使用现有技术栈快速实现|4
技术方案B|采用新技术提供更好性能|3
"""
                
                response = llm.invoke([HumanMessage(content=options_prompt)])
                
                options = []
                for line in response.content.strip().split('\n'):
                    if '|' in line:
                        try:
                            parts = line.split('|')
                            if len(parts) >= 3:
                                name = parts[0].strip()
                                desc = parts[1].strip()
                                priority = int(parts[2].strip())
                                options.append({
                                    "name": name,
                                    "description": desc,
                                    "priority": priority,
                                    "explored": False
                                })
                        except:
                            continue
                
                # 如果解析失败，使用默认选项
                if not options:
                    options = [
                        {"name": "方案A", "description": "第一种解决思路", "priority": 4, "explored": False},
                        {"name": "方案B", "description": "第二种解决思路", "priority": 3, "explored": False},
                        {"name": "方案C", "description": "第三种解决思路", "priority": 3, "explored": False}
                    ]
                
            except Exception as e:
                print(f"⚠️ 选项生成失败: {e}")
                # 使用默认选项
                options = [
                    {"name": "直接方案", "description": "最直接的解决方法", "priority": 4, "explored": False},
                    {"name": "深入方案", "description": "深入分析的解决方法", "priority": 3, "explored": False},
                    {"name": "创新方案", "description": "创新思路的解决方法", "priority": 3, "explored": False}
                ]
        else:
            # 模拟决策选项
            options = [
                {"name": "标准方案", "description": "采用标准化的解决方法", "priority": 4, "explored": False},
                {"name": "优化方案", "description": "在标准基础上进行优化", "priority": 4, "explored": False},
                {"name": "创新方案", "description": "尝试创新的解决思路", "priority": 3, "explored": False},
                {"name": "保守方案", "description": "风险较低的稳妥方法", "priority": 3, "explored": False}
            ]
        
        # 按优先级排序
        options.sort(key=lambda x: x["priority"], reverse=True)
        decision_tree["root"]["options"] = options
        
        # 探索每个选项
        explored_paths = []
        print(f"\n🌳 决策树分析：{user_question}")
        print(f"📋 发现 {len(options)} 个可能方向:")
        
        for i, option in enumerate(options[:3]):  # 探索前3个选项
            print(f"\n🔍 探索选项 {i+1}: {option['name']}")
            print(f"📝 描述: {option['description']}")
            print(f"⭐ 优先级: {option['priority']}/5")
            
            if api_key:
                try:
                    explore_prompt = f"""
用户问题：{user_question}
当前探索方向：{option['name']} - {option['description']}

请深入分析这个方向，包括：
1. 具体的解决步骤
2. 可能的挑战和风险
3. 预期效果和优势
4. 推荐程度(1-10)，并简要说明原因

请简洁而有条理地回答。
"""
                    
                    exploration = llm.invoke([HumanMessage(content=explore_prompt)])
                    analysis_content = exploration.content
                    
                except Exception as e:
                    print(f"⚠️ 选项分析失败: {e}")
                    analysis_content = f"对{option['name']}的分析：这是一个可行的解决方向，具有{option['priority']}/5的优先级。"
            else:
                # 模拟分析结果
                analysis_content = f"""
**{option['name']}分析结果：**
1. 实施步骤：分析需求 → 设计方案 → 实施执行 → 验证效果
2. 主要优势：符合当前条件，风险可控，实施难度适中
3. 潜在挑战：需要一定的资源投入和时间规划
4. 推荐程度：{option['priority'] + 3}/10 - 基于优先级和可行性评估
"""
            
            explored_paths.append({
                "option": option,
                "analysis": analysis_content,
                "timestamp": time.time()
            })
            
            print(f"📊 分析结果: {analysis_content[:80]}...")
            time.sleep(0.5)
        
        # 选择最佳路径
        if api_key:
            try:
                best_path_prompt = f"""
基于以下分析，选择最佳解决方案并给出最终建议：

用户问题：{user_question}

分析的方案：
{chr(10).join([f"方案{i+1}: {path['option']['name']}\n分析: {path['analysis']}\n" for i, path in enumerate(explored_paths)])}

请：
1. 选择最推荐的方案
2. 说明选择理由
3. 给出具体的实施建议
"""
                
                final_decision_response = llm.invoke([HumanMessage(content=best_path_prompt)])
                final_decision = final_decision_response.content
                
            except Exception as e:
                print(f"⚠️ 最终决策失败: {e}")
                best_option = max(explored_paths, key=lambda x: x['option']['priority'])
                final_decision = f"推荐选择：{best_option['option']['name']}。这个方案具有最高的优先级和可行性。"
        else:
            # 模拟最终决策
            best_option = max(explored_paths, key=lambda x: x['option']['priority'])
            final_decision = f"""
**最终推荐：{best_option['option']['name']}**

选择理由：
- 优先级最高（{best_option['option']['priority']}/5）
- 风险可控，实施难度适中
- 符合当前的资源和时间约束

实施建议：
1. 制定详细的执行计划
2. 确保必要的资源配置
3. 设定阶段性检查点
4. 保持方案的灵活性，根据实际情况调整
"""
        
        print(f"\n🎯 最终决策: {final_decision[:100]}...")
        
        return {
            "messages": [AIMessage(content=final_decision)],
            "decision_tree": decision_tree,
            "current_path": [path["option"]["name"] for path in explored_paths],
            "alternatives": options,
            "final_decision": final_decision
        }
    
    # 构建图
    graph = StateGraph(DecisionState)
    graph.add_node("decision_tree", decision_tree_node)
    graph.add_edge(START, "decision_tree")
    graph.add_edge("decision_tree", END)
    
    return graph.compile()
```

## 9.3 前端接入示例（React / Streamlit）

### Streamlit 实时界面

```python
import streamlit as st
from datetime import datetime

def create_streamlit_interface():
    """创建 Streamlit 实时界面"""
    
    st.title("🌊 LangGraph 流式聊天机器人")
    st.markdown("实时查看 AI 的思考过程")
    
    # 初始化会话状态
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "thinking_process" not in st.session_state:
        st.session_state.thinking_process = []
    
    # 显示聊天历史
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # 用户输入
    if prompt := st.chat_input("请输入您的问题"):
        # 添加用户消息
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # AI 响应区域
        with st.chat_message("assistant"):
            # 创建占位符用于实时更新
            thinking_placeholder = st.empty()
            response_placeholder = st.empty()
            progress_bar = st.progress(0)
            
            # 模拟思考过程
            thinking_steps = [
                "🤔 分析问题...",
                "📚 检索相关信息...",
                "💡 生成解决方案...",
                "✅ 完善答案..."
            ]
            
            full_response = ""
            for i, step in enumerate(thinking_steps):
                thinking_placeholder.markdown(f"**{step}**")
                progress_bar.progress((i + 1) / len(thinking_steps))
                time.sleep(1)
                
                # 最后一步生成完整回答
                if i == len(thinking_steps) - 1:
                    try:
                        chatbot = create_streaming_chatbot()
                        result = chatbot.invoke({
                            "messages": [HumanMessage(content=prompt)],
                            "streaming_content": "",
                            "current_step": "start",
                            "progress": 0.0
                        })
                        full_response = result["messages"][-1].content
                    except Exception as e:
                        full_response = f"生成回答时出现问题：{str(e)[:100]}..."
                    
                    # 模拟打字效果
                    displayed_response = ""
                    for char in full_response:
                        displayed_response += char
                        response_placeholder.markdown(displayed_response + "▌")
                        time.sleep(0.02)
                    
                    response_placeholder.markdown(displayed_response)
            
            # 清理临时元素
            thinking_placeholder.empty()
            progress_bar.empty()
            
            # 添加 AI 响应到历史
            st.session_state.messages.append({
                "role": "assistant",
                "content": full_response
            })

def create_advanced_streamlit_interface():
    """创建高级 Streamlit 界面"""
    
    st.set_page_config(
        page_title="LangGraph 智能助手",
        page_icon="🤖",
        layout="wide"
    )
    
    # 侧边栏配置
    with st.sidebar:
        st.header("⚙️ 配置")
        
        # 模型选择
        model_choice = st.selectbox(
            "选择模型",
            ["glm-4.5", "glm-4", "glm-4-plus"]
        )
        
        # 温度设置
        temperature = st.slider(
            "创造性 (Temperature)",
            min_value=0.0,
            max_value=1.0,
            value=0.7,
            step=0.1
        )
        
        # 显示模式
        show_thinking = st.checkbox("显示思考过程", value=True)
        show_progress = st.checkbox("显示进度条", value=True)
        show_decision_tree = st.checkbox("显示决策树", value=False)
        
        # API 配置
        api_key = st.text_input("智谱API密钥", type="password")
        if api_key:
            os.environ["ZHIPUAI_API_KEY"] = api_key
            st.success("✅ API密钥已设置")
        
        # 清除历史
        if st.button("🗑️ 清除对话历史"):
            st.session_state.messages = []
            st.session_state.thinking_process = []
            st.rerun()
    
    # 主界面
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("💬 对话区域")
        
        # 聊天界面（复用之前的逻辑）
        create_streamlit_interface()
    
    with col2:
        st.header("🧠 思考过程")
        
        if show_thinking and st.session_state.thinking_process:
            for step in st.session_state.thinking_process:
                with st.expander(f"步骤: {step['name']}"):
                    st.write(f"**时间**: {step['timestamp']}")
                    st.write(f"**内容**: {step['content']}")
                    st.write(f"**置信度**: {step.get('confidence', 'N/A')}")
        
        # 实时统计
        st.header("📊 对话统计")
        if st.session_state.messages:
            total_messages = len(st.session_state.messages)
            user_messages = len([m for m in st.session_state.messages if m["role"] == "user"])
            ai_messages = len([m for m in st.session_state.messages if m["role"] == "assistant"])
            
            st.metric("总消息数", total_messages)
            st.metric("用户消息", user_messages)
            st.metric("AI 回复", ai_messages)

# React 前端示例（JavaScript）
react_example = '''
// React 流式聊天组件示例
import React, { useState, useEffect } from 'react';

const StreamingChat = () => {
    const [messages, setMessages] = useState([]);
    const [currentMessage, setCurrentMessage] = useState('');
    const [isStreaming, setIsStreaming] = useState(false);
    const [thinkingSteps, setThinkingSteps] = useState([]);

    const sendMessage = async (userMessage) => {
        setMessages(prev => [...prev, { role: 'user', content: userMessage }]);
        setIsStreaming(true);
        setThinkingSteps([]);

        try {
            const response = await fetch('/api/chat/stream', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: userMessage })
            });

            const reader = response.body.getReader();
            let aiMessage = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = new TextDecoder().decode(value);
                const lines = chunk.split('\\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            
                            if (data.type === 'thinking') {
                                setThinkingSteps(prev => [...prev, data.step]);
                            } else if (data.type === 'token') {
                                aiMessage += data.content;
                                setCurrentMessage(aiMessage);
                            } else if (data.type === 'complete') {
                                setMessages(prev => [...prev, { 
                                    role: 'assistant', 
                                    content: aiMessage 
                                }]);
                                setCurrentMessage('');
                                setIsStreaming(false);
                            }
                        } catch (e) {
                            console.error('解析错误:', e);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('请求错误:', error);
            setIsStreaming(false);
        }
    };

    return (
        <div className="chat-container">
            <div className="messages">
                {messages.map((msg, index) => (
                    <div key={index} className={`message ${msg.role}`}>
                        {msg.content}
                    </div>
                ))}
                
                {isStreaming && (
                    <div className="message assistant streaming">
                        <div className="thinking-steps">
                            {thinkingSteps.map((step, index) => (
                                <div key={index} className="thinking-step">
                                    🤔 {step}
                                </div>
                            ))}
                        </div>
                        <div className="current-response">
                            {currentMessage}
                            <span className="cursor">▌</span>
                        </div>
                    </div>
                )}
            </div>
            <ChatInput onSend={sendMessage} disabled={isStreaming} />
        </div>
    );
};

export default StreamingChat;
'''

# CSS 样式示例
css_styles = '''
/* 流式聊天界面样式 */
.chat-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
}

.messages {
    height: 500px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
}

.message {
    margin: 10px 0;
    padding: 10px;
    border-radius: 8px;
    max-width: 80%;
}

.message.user {
    background-color: #007bff;
    color: white;
    margin-left: auto;
    text-align: right;
}

.message.assistant {
    background-color: #e9ecef;
    color: #333;
}

.message.streaming {
    border-left: 3px solid #28a745;
    animation: pulse 1.5s infinite;
}

.thinking-steps {
    font-style: italic;
    color: #666;
    margin-bottom: 10px;
}

.thinking-step {
    padding: 2px 0;
    font-size: 0.9em;
}

.current-response {
    line-height: 1.5;
}

.cursor {
    animation: blink 1s infinite;
    font-weight: bold;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 进度条样式 */
.progress-container {
    margin: 10px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #28a745);
    transition: width 0.3s ease;
}

/* 决策树样式 */
.decision-tree {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
}

.decision-option {
    margin: 10px 0;
    padding: 10px;
    border-left: 3px solid #007bff;
    background-color: #f8f9fa;
}

.option-priority {
    font-weight: bold;
    color: #007bff;
}
'''
```

## 🔧 环境准备

在运行本章的示例代码之前，请确保已安装必要的依赖：

```bash
# 使用uv安装依赖
uv add langchain-community langgraph langchain-core streamlit
```

并设置智谱AI API密钥：

```bash
export ZHIPUAI_API_KEY="your_zhipu_api_key_here"
```

## 🚀 运行示例

```python
def demo_streaming_interfaces():
    """演示流式输出与实时反馈"""
    print("🌊 流式输出与实时反馈演示")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置")
    else:
        print("⚠️ 未配置智谱API密钥，演示将使用模拟模式")
    
    # 1. 基础流式输出
    print("\n1. 🚀 基础流式输出")
    streaming_bot = create_streaming_chatbot()
    test_question = "请解释什么是机器学习，并给出一些实际应用例子"
    print(f"❓ 用户问题: {test_question}")
    
    try:
        result = streaming_bot.invoke({
            "messages": [HumanMessage(content=test_question)],
            "streaming_content": "",
            "current_step": "start",
            "progress": 0.0
        })
        print(f"\n✅ 流式输出完成，状态: {result.get('current_step', 'unknown')}")
    except Exception as e:
        print(f"❌ 流式输出失败: {e}")
    
    # 2. 进度跟踪演示
    print(f"\n{'='*60}")
    print("2. 📊 进度跟踪演示")
    progress_bot = create_progress_tracking_chatbot()
    complex_question = "请详细分析云计算的优势和挑战"
    print(f"❓ 复杂问题: {complex_question}")
    
    try:
        result = progress_bot.invoke({
            "messages": [HumanMessage(content=complex_question)],
            "current_task": "",
            "progress_steps": [],
            "completed_steps": 0,
            "total_steps": 0,
            "estimated_time": 0.0
        })
        print(f"\n✅ 进度跟踪完成")
        print(f"📋 总步骤: {result.get('total_steps', 0)}")
        print(f"✅ 完成步骤: {result.get('completed_steps', 0)}")
    except Exception as e:
        print(f"❌ 进度跟踪失败: {e}")
    
    # 3. 思考过程可视化
    print(f"\n{'='*60}")
    print("3. 🧠 思考过程可视化")
    thinking_bot = create_thinking_process_chatbot()
    thinking_question = "如何设计一个高效的推荐系统？"
    print(f"❓ 思考问题: {thinking_question}")
    
    try:
        result = thinking_bot.invoke({
            "messages": [HumanMessage(content=thinking_question)],
            "thinking_steps": [],
            "current_thought": "",
            "reasoning_chain": [],
            "confidence_scores": []
        })
        print(f"\n🎯 思考步骤数: {len(result.get('reasoning_chain', []))}")
        confidence_scores = result.get('confidence_scores', [])
        if confidence_scores:
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
            print(f"📊 平均置信度: {avg_confidence:.2f}")
    except Exception as e:
        print(f"❌ 思考过程失败: {e}")
    
    # 4. 决策树可视化
    print(f"\n{'='*60}")
    print("4. 🌳 决策树可视化")
    decision_bot = create_decision_tree_chatbot()
    decision_question = "我应该选择哪种编程语言来开发Web应用？"
    print(f"❓ 决策问题: {decision_question}")
    
    try:
        result = decision_bot.invoke({
            "messages": [HumanMessage(content=decision_question)],
            "decision_tree": {},
            "current_path": [],
            "alternatives": [],
            "final_decision": ""
        })
        print(f"\n🛣️ 探索路径: {', '.join(result.get('current_path', []))}")
        print(f"🎯 最终决策长度: {len(result.get('final_decision', ''))}")
        print(f"📋 备选方案数: {len(result.get('alternatives', []))}")
    except Exception as e:
        print(f"❌ 决策树失败: {e}")
    
    print(f"\n{'='*60}")
    print("🎉 流式输出演示完成！")
    
    # 前端集成说明
    print(f"\n💡 前端集成说明:")
    print("1. 🌊 Streamlit: 运行 `streamlit run your_app.py`")
    print("2. ⚛️ React: 参考提供的组件代码")  
    print("3. 🔗 关键技术: Server-Sent Events (SSE) 或 WebSocket")
    print("4. 🎨 样式: 使用提供的CSS样式文件")

if __name__ == "__main__":
    demo_streaming_interfaces()
```

---

## 本章小结

在这一章中，我们学习了如何实现流式输出和实时反馈：

1. **逐 token 流式返回**：让用户实时看到 AI 的回答过程，显著提升用户体验
2. **进度跟踪系统**：通过分步骤执行展示任务进展，减少等待焦虑
3. **思考过程可视化**：展示 AI 的推理链条，增加透明度和信任度
4. **决策树可视化**：呈现多种解决方案的分析和选择过程
5. **前端集成方案**：提供了 Streamlit 和 React 的完整实现示例

## 核心特性

- **实时反馈** - 逐token显示，消除等待焦虑
- **过程透明** - 展示AI思考和决策过程
- **进度可视** - 清晰的任务进度和步骤展示
- **交互友好** - 支持中断和实时调整

流式输出不仅改善了用户体验，还让 AI 系统变得更加透明和可信。用户可以实时了解 AI 的工作状态，甚至在不满意时及时中断。

## 下一步预告

在第10章中，我们将学习如何将 LangGraph 应用**部署到生产环境**，包括本地部署、云端部署和监控运维等关键技能。准备好让你的 AI 助手走向生产环境了吗？