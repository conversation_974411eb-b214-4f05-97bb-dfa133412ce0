#!/usr/bin/env python3
"""
修复结构化输出问题
使用更简单、更兼容的JSON输出方式
"""
import os
import json
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_community.chat_models import ChatZhipuAI

def test_simple_json_output():
    """测试简单的JSON输出"""
    print("🔧 测试简化的结构化输出")
    print("=" * 50)
    
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ 未找到API密钥")
        return False
    
    try:
        llm = ChatZhipuAI(
            model="glm-4.5", 
            temperature=0.3, 
            api_key=api_key,
            timeout=20
        )
        
        # 使用更简单的JSON提示，避免复杂的Pydantic格式
        simple_prompt = """
请将用户需求分解为任务列表，严格按照以下JSON格式返回，不要包含任何其他内容：

{
  "project_name": "项目名称",
  "total_estimated_hours": 总工时数字,
  "tasks": [
    {
      "title": "任务标题",
      "description": "任务描述",
      "priority": "high|medium|low",
      "estimated_hours": 工时数字,
      "tags": ["标签1", "标签2"]
    }
  ]
}

用户需求：我需要开发一个在线商城，包括用户注册、商品展示、购物车和支付功能

请直接返回JSON，不要有其他解释文字：
"""
        
        response = llm.invoke([HumanMessage(content=simple_prompt)])
        
        print("📝 GLM-4.5原始输出:")
        print(response.content)
        
        # 尝试解析JSON
        try:
            # 清理输出，移除可能的markdown格式
            json_text = response.content.strip()
            if json_text.startswith("```json"):
                json_text = json_text.replace("```json", "").replace("```", "").strip()
            elif json_text.startswith("```"):
                json_text = json_text.replace("```", "").strip()
            
            parsed_json = json.loads(json_text)
            print("\n✅ JSON解析成功！")
            print(f"📋 项目: {parsed_json['project_name']}")
            print(f"⏱️ 总工时: {parsed_json['total_estimated_hours']}小时")
            print(f"📝 任务数: {len(parsed_json['tasks'])}")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"\n⚠️ JSON解析失败: {e}")
            print("但GLM-4.5输出是正常的，只需要调整解析方式")
            return False
            
    except Exception as e:
        print(f"❌ GLM-4.5调用失败: {e}")
        return False

def create_improved_structured_system():
    """创建改进的结构化输出系统"""
    print("\n🛠️ 改进的结构化输出系统")
    print("=" * 50)
    
    def extract_json_from_response(response_text: str) -> dict:
        """从响应中提取JSON"""
        # 移除markdown标记
        text = response_text.strip()
        if text.startswith("```json"):
            text = text.replace("```json", "").replace("```", "").strip()
        elif text.startswith("```"):
            text = text.replace("```", "").strip()
        
        # 查找JSON内容
        try:
            # 尝试直接解析
            return json.loads(text)
        except:
            # 如果失败，尝试找到JSON部分
            start = text.find("{")
            end = text.rfind("}") + 1
            if start != -1 and end != 0:
                try:
                    return json.loads(text[start:end])
                except:
                    pass
        
        raise ValueError("无法解析JSON内容")
    
    # 测试改进的系统
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ 未找到API密钥")
        return
    
    try:
        llm = ChatZhipuAI(
            model="glm-4.5", 
            temperature=0.1,  # 降低温度提高一致性
            api_key=api_key,
            timeout=20
        )
        
        prompt = """
用户需求：开发一个博客系统，包括文章发布、评论功能、用户管理

请返回JSON格式的任务分解，格式如下：
{
  "project_name": "博客系统开发",
  "total_estimated_hours": 80,
  "tasks": [
    {
      "title": "数据库设计",
      "description": "设计文章、用户、评论表结构",
      "priority": "high",
      "estimated_hours": 16,
      "tags": ["数据库", "架构"]
    }
  ]
}
"""
        
        response = llm.invoke([HumanMessage(content=prompt)])
        print("📝 GLM-4.5输出:")
        print(response.content[:200] + "...")
        
        # 使用改进的解析器
        parsed_data = extract_json_from_response(response.content)
        print("\n✅ 改进的解析器成功！")
        print(f"📋 项目: {parsed_data['project_name']}")
        print(f"⏱️ 总工时: {parsed_data['total_estimated_hours']}小时")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_simple_json_output()
    create_improved_structured_system()