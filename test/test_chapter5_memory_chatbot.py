"""
测试第5章记忆与状态管理 - MemorySaver 聊天机器人功能
针对第11-68行的 create_memory_chatbot 代码段进行全面测试
"""

import os
import pytest
from unittest.mock import patch, MagicMock
from typing_extensions import TypedDict
from typing import Annotated

# 测试用的模拟依赖
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage


class ConversationState(TypedDict):
    """对话状态定义 - 从第5章代码复制"""
    messages: Annotated[list, 'add_messages']
    user_name: str
    conversation_count: int
    topics_discussed: list[str]


class TestMemoryChatbot:
    """MemorySaver 聊天机器人测试类"""
    
    def setup_method(self):
        """每个测试方法执行前的设置"""
        # 设置模拟的API密钥
        os.environ["OPENAI_API_KEY"] = "test_key_123"
        
    def test_conversation_state_structure(self):
        """测试 ConversationState 状态结构定义"""
        # 测试状态字典的基本结构
        state = ConversationState(
            messages=[],
            user_name="测试用户",
            conversation_count=0,
            topics_discussed=[]
        )
        
        assert isinstance(state["messages"], list)
        assert isinstance(state["user_name"], str)
        assert isinstance(state["conversation_count"], int)
        assert isinstance(state["topics_discussed"], list)
        assert state["user_name"] == "测试用户"
        assert state["conversation_count"] == 0
        
    @patch('langgraph.checkpoint.memory.MemorySaver')
    @patch('langgraph.graph.StateGraph')
    def test_create_memory_chatbot_structure(self, mock_state_graph, mock_memory_saver):
        """测试 create_memory_chatbot 函数的基本结构"""
        # 模拟依赖
        mock_memory = MagicMock()
        mock_memory_saver.return_value = mock_memory
        
        mock_graph = MagicMock()
        mock_state_graph.return_value = mock_graph
        
        mock_compiled_app = MagicMock()
        mock_graph.compile.return_value = mock_compiled_app
        
        # 导入并执行函数（模拟）
        from langgraph.checkpoint.memory import MemorySaver
        from langgraph.graph import StateGraph, START, END
        
        def create_memory_chatbot():
            """创建带内存的聊天机器人"""
            memory = MemorySaver()
            
            def chatbot_node(state: ConversationState):
                """聊天节点，能够记住对话历史"""
                user_name = state.get("user_name", "朋友")
                conversation_count = state.get("conversation_count", 0)
                topics = state.get("topics_discussed", [])
                
                # 返回模拟响应而不是真正调用LLM
                return {
                    "messages": [AIMessage(content=f"你好 {user_name}！这是第{conversation_count + 1}次对话。")],
                    "conversation_count": conversation_count + 1
                }
            
            graph = StateGraph(ConversationState)
            graph.add_node("chat", chatbot_node)
            graph.add_edge(START, "chat")
            graph.add_edge("chat", END)
            
            return graph.compile(checkpointer=memory)
        
        # 执行函数
        app = create_memory_chatbot()
        
        # 验证调用
        mock_memory_saver.assert_called_once()
        mock_state_graph.assert_called_once_with(ConversationState)
        mock_graph.compile.assert_called_once_with(checkpointer=mock_memory)
        
    def test_chatbot_node_logic(self):
        """测试 chatbot_node 函数的逻辑"""
        def chatbot_node(state: ConversationState):
            """聊天节点逻辑测试版本"""
            user_name = state.get("user_name", "朋友")
            conversation_count = state.get("conversation_count", 0)
            topics = state.get("topics_discussed", [])
            
            # 构造系统提示
            system_prompt = f"""
你是一个友好的AI助手。你正在与{user_name}对话。
这是你们的第{conversation_count + 1}次交流。
之前讨论过的话题：{', '.join(topics) if topics else '无'}
请保持对话的连贯性，记住之前的交流内容。
"""
            
            # 模拟LLM响应
            response = AIMessage(content=f"收到，{user_name}！这是我们的第{conversation_count + 1}次交流。")
            
            new_count = conversation_count + 1
            
            return {
                "messages": [response],
                "conversation_count": new_count
            }
        
        # 测试初始状态
        initial_state = ConversationState(
            messages=[HumanMessage(content="你好")],
            user_name="小明",
            conversation_count=0,
            topics_discussed=[]
        )
        
        result = chatbot_node(initial_state)
        
        # 验证返回结果
        assert "messages" in result
        assert "conversation_count" in result
        assert len(result["messages"]) == 1
        assert isinstance(result["messages"][0], AIMessage)
        assert result["conversation_count"] == 1
        assert "小明" in result["messages"][0].content
        
        # 测试有历史记录的状态
        state_with_history = ConversationState(
            messages=[
                HumanMessage(content="你好"),
                AIMessage(content="你好，小明！"),
                HumanMessage(content="今天天气如何？")
            ],
            user_name="小明",
            conversation_count=2,
            topics_discussed=["天气", "问候"]
        )
        
        result2 = chatbot_node(state_with_history)
        
        assert result2["conversation_count"] == 3
        assert isinstance(result2["messages"][0], AIMessage)
        
    def test_state_defaults(self):
        """测试状态字段的默认值处理"""
        def test_defaults(state: ConversationState):
            user_name = state.get("user_name", "朋友")
            conversation_count = state.get("conversation_count", 0)
            topics = state.get("topics_discussed", [])
            
            return {
                "user_name": user_name,
                "conversation_count": conversation_count,
                "topics": topics
            }
        
        # 测试完整状态
        complete_state = ConversationState(
            messages=[],
            user_name="张三",
            conversation_count=5,
            topics_discussed=["科技", "生活"]
        )
        
        result = test_defaults(complete_state)
        assert result["user_name"] == "张三"
        assert result["conversation_count"] == 5
        assert result["topics"] == ["科技", "生活"]
        
        # 测试部分缺失的状态
        partial_state = ConversationState(
            messages=[],
            user_name="",
            conversation_count=0,
            topics_discussed=[]
        )
        
        # 模拟 get 方法的默认值行为
        user_name = partial_state.get("user_name", "朋友") or "朋友"
        conversation_count = partial_state.get("conversation_count", 0)
        topics = partial_state.get("topics_discussed", [])
        
        assert user_name == "朋友"  # 空字符串时使用默认值
        assert conversation_count == 0
        assert topics == []
        
    def test_system_prompt_generation(self):
        """测试系统提示的生成逻辑"""
        def generate_system_prompt(user_name: str, conversation_count: int, topics: list):
            return f"""
你是一个友好的AI助手。你正在与{user_name}对话。
这是你们的第{conversation_count + 1}次交流。
之前讨论过的话题：{', '.join(topics) if topics else '无'}
请保持对话的连贯性，记住之前的交流内容。
"""
        
        # 测试新用户
        prompt1 = generate_system_prompt("新用户", 0, [])
        assert "新用户" in prompt1
        assert "第1次交流" in prompt1
        assert "无" in prompt1
        
        # 测试老用户
        prompt2 = generate_system_prompt("老用户", 5, ["天气", "新闻", "科技"])
        assert "老用户" in prompt2
        assert "第6次交流" in prompt2
        assert "天气, 新闻, 科技" in prompt2
        
    @patch('langchain_openai.ChatOpenAI')
    def test_llm_integration_mock(self, mock_chat_openai):
        """测试LLM集成（使用模拟）"""
        # 设置模拟的LLM响应
        mock_llm = MagicMock()
        mock_response = AIMessage(content="这是模拟的AI响应")
        mock_llm.invoke.return_value = mock_response
        mock_chat_openai.return_value = mock_llm
        
        def chatbot_node_with_llm(state: ConversationState):
            """带LLM调用的聊天节点（模拟版本）"""
            from langchain_openai import ChatOpenAI
            from langchain_core.messages import SystemMessage
            
            user_name = state.get("user_name", "朋友")
            conversation_count = state.get("conversation_count", 0)
            topics = state.get("topics_discussed", [])
            
            system_prompt = f"你正在与{user_name}对话，这是第{conversation_count + 1}次交流。"
            messages = [SystemMessage(content=system_prompt)] + state["messages"]
            
            llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0.7)
            response = llm.invoke(messages)
            
            return {
                "messages": [response],
                "conversation_count": conversation_count + 1
            }
        
        # 测试状态
        test_state = ConversationState(
            messages=[HumanMessage(content="你好")],
            user_name="测试用户",
            conversation_count=0,
            topics_discussed=[]
        )
        
        result = chatbot_node_with_llm(test_state)
        
        # 验证LLM被正确调用
        mock_chat_openai.assert_called_once_with(model="gpt-3.5-turbo", temperature=0.7)
        mock_llm.invoke.assert_called_once()
        
        # 验证返回结果
        assert result["messages"][0] == mock_response
        assert result["conversation_count"] == 1
        
    def test_memory_conversation_flow(self):
        """测试完整的记忆对话流程（模拟版本）"""
        # 模拟对话历史
        conversation_history = []
        
        def simulate_conversation_turn(user_input: str, user_name: str, count: int, topics: list):
            """模拟一轮对话"""
            state = ConversationState(
                messages=conversation_history + [HumanMessage(content=user_input)],
                user_name=user_name,
                conversation_count=count,
                topics_discussed=topics
            )
            
            # 模拟AI响应
            ai_response = AIMessage(content=f"针对'{user_input}'的回复（第{count+1}轮）")
            
            # 更新历史
            conversation_history.extend([HumanMessage(content=user_input), ai_response])
            
            return {
                "messages": [ai_response],
                "conversation_count": count + 1
            }
        
        # 模拟多轮对话
        result1 = simulate_conversation_turn("你好", "小王", 0, [])
        assert result1["conversation_count"] == 1
        
        result2 = simulate_conversation_turn("今天天气如何？", "小王", 1, ["问候"])
        assert result2["conversation_count"] == 2
        assert len(conversation_history) == 4  # 2轮对话，每轮2条消息
        
        result3 = simulate_conversation_turn("谢谢你", "小王", 2, ["问候", "天气"])
        assert result3["conversation_count"] == 3
        assert len(conversation_history) == 6
        
if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])