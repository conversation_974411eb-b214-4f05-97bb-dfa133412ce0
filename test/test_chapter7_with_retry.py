#!/usr/bin/env python3
"""
测试带重试机制的第7章代码
使用新的API密钥
"""
import os
import time
from typing_extensions import TypedDict
from typing import Annotated

from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

class SmartChatState(TypedDict):
    messages: Annotated[list, add_messages]
    role: str
    context: dict
    response_format: str

# 角色定义
ROLES = {
    "技术专家": {
        "system_prompt": "你是一位资深的技术专家，回答要简洁专业。",
        "response_format": "technical"
    },
    "产品经理": {
        "system_prompt": "你是一位产品经理，从商业角度分析问题。",
        "response_format": "business"
    },
    "教学助手": {
        "system_prompt": "你是编程教学助手，用简单易懂的方式解释。",
        "response_format": "educational"
    }
}

def create_retry_based_chatbot():
    """创建带重试机制的聊天机器人"""
    
    def role_selection_node(state: SmartChatState):
        """角色选择节点"""
        last_message = state["messages"][-1].content.lower()
        
        if any(keyword in last_message for keyword in ["代码", "编程", "bug", "算法"]):
            selected_role = "技术专家"
        elif any(keyword in last_message for keyword in ["产品", "用户", "需求", "商业"]):
            selected_role = "产品经理"
        elif any(keyword in last_message for keyword in ["学习", "教", "不懂", "初学"]):
            selected_role = "教学助手"
        else:
            selected_role = "技术专家"
            
        return {
            "role": selected_role,
            "response_format": ROLES[selected_role]["response_format"]
        }
    
    def smart_response_node(state: SmartChatState):
        """智能回复节点（带重试）"""
        role = state.get("role", "技术专家")
        role_config = ROLES[role]
        
        # 构造角色化的系统提示
        system_message = SystemMessage(content=role_config["system_prompt"])
        messages = [system_message] + state["messages"]
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"messages": [AIMessage(content="⚠️ 未配置智谱API密钥")]}
        
        def call_with_retry(messages, role, max_retries=2):
            """带重试的GLM调用"""
            for attempt in range(max_retries):
                print(f"🔄 [{role}] 第{attempt + 1}次尝试...")
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    
                    if role == "技术专家":
                        llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
                    elif role == "产品经理":
                        llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key, timeout=35)
                    else:  # 教学助手
                        llm = ChatZhipuAI(model="glm-4.5", temperature=0.5, api_key=api_key, timeout=35)
                    
                    start_time = time.time()
                    response = llm.invoke(messages)
                    elapsed = time.time() - start_time
                    
                    print(f"✅ [{role}] 成功！用时: {elapsed:.2f}秒")
                    
                    # 添加角色标识
                    role_tagged_response = f"[{role}] {response.content}"
                    response.content = role_tagged_response
                    
                    return {"messages": [response]}
                    
                except Exception as e:
                    elapsed = time.time() - start_time if 'start_time' in locals() else 0
                    print(f"⚠️ [{role}] 第{attempt + 1}次失败: {str(e)[:50]}... (用时: {elapsed:.2f}秒)")
                    
                    if attempt < max_retries - 1:
                        # 重试前短暂等待
                        print(f"⏳ 等待1秒后重试...")
                        time.sleep(1)
                        continue
                    else:
                        # 最后一次失败，返回友好的错误信息
                        return {"messages": [AIMessage(content=f"[{role}] ⚠️ 网络繁忙，请稍后重试。（尝试了{max_retries}次）")]}
        
        return call_with_retry(messages, role)
    
    # 构建图
    graph = StateGraph(SmartChatState)
    graph.add_node("role_selection", role_selection_node)
    graph.add_node("smart_response", smart_response_node)
    
    graph.add_edge(START, "role_selection")
    graph.add_edge("role_selection", "smart_response")
    graph.add_edge("smart_response", END)
    
    return graph.compile()

def main():
    """测试重试机制"""
    print("🔄 测试带重试机制的第7章代码")
    print("=" * 60)
    
    # 设置新的API密钥
    os.environ["ZHIPUAI_API_KEY"] = "2d6b78b1032c403eb43ba59c28afed18.ZiDmzgNMRRX0Z9gY"
    print(f"🔑 使用新API密钥: 2d6b78b103...X0Z9gY")
    
    bot = create_retry_based_chatbot()
    
    test_questions = [
        "你好，请简单介绍一下你自己",  # 简单问题，应该快速成功
        "我是编程新手，如何开始学习Python？",  # 复杂问题，可能需要重试
        "这段代码的时间复杂度是多少？def find_max(arr): return max(arr)"  # 技术问题
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{'='*20} 测试 {i}/{len(test_questions)} {'='*20}")
        print(f"❓ 问题: {question}")
        
        start_time = time.time()
        try:
            result = bot.invoke({
                "messages": [HumanMessage(content=question)],
                "role": "",
                "context": {},
                "response_format": ""
            })
            
            total_time = time.time() - start_time
            response = result['messages'][-1].content
            
            print(f"📝 最终回复: {response[:150]}{'...' if len(response) > 150 else ''}")
            print(f"⏱️ 总用时: {total_time:.2f}秒")
            
        except Exception as e:
            total_time = time.time() - start_time
            print(f"❌ 最终失败: {e}")
            print(f"⏱️ 总用时: {total_time:.2f}秒")
        
        # 短暂间隔
        if i < len(test_questions):
            print("⏳ 等待3秒...")
            time.sleep(3)
    
    print(f"\n{'='*60}")
    print("🎉 重试机制测试完成！")
    print("💡 即使偶尔超时，重试机制也能提高成功率")

if __name__ == "__main__":
    main()