#!/usr/bin/env python3
"""
测试新的GLM API密钥
"""
import os
import time
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_community.chat_models import ChatZhipuAI

def test_new_api_key():
    """测试新的API密钥"""
    print("🔑 测试新的GLM API密钥")
    print("=" * 50)
    
    new_api_key = "2d6b78b1032c403eb43ba59c28afed18.ZiDmzgNMRRX0Z9gY"
    print(f"✅ 新API Key: {new_api_key[:10]}...{new_api_key[-6:]}")
    
    # 测试不同类型的问题
    test_cases = [
        {
            "name": "简单问题",
            "prompt": "你好，请简单介绍一下你自己",
            "timeout": 30
        },
        {
            "name": "编程问题",
            "prompt": "我是编程新手，如何开始学习Python？",
            "timeout": 30
        },
        {
            "name": "技术分析",
            "prompt": "这段代码的时间复杂度是多少？def find_max(arr): return max(arr)",
            "timeout": 30
        },
        {
            "name": "结构化输出",
            "prompt": """请将以下需求分解为任务，用JSON格式返回：
需求：开发一个简单的博客系统

返回格式：
{
  "project": "项目名",
  "tasks": [
    {"title": "任务1", "hours": 10}
  ]
}""",
            "timeout": 40
        }
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}/{total_tests}: {test_case['name']}")
        print(f"📝 问题: {test_case['prompt'][:50]}...")
        
        try:
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.3,
                api_key=new_api_key,
                timeout=test_case['timeout']
            )
            
            start_time = time.time()
            response = llm.invoke([HumanMessage(content=test_case['prompt'])])
            elapsed = time.time() - start_time
            
            print(f"✅ 成功！用时: {elapsed:.2f}秒")
            print(f"📄 回复长度: {len(response.content)}字符")
            print(f"🔤 回复开头: {response.content[:100]}...")
            
            success_count += 1
            
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 失败！用时: {elapsed:.2f}秒")
            print(f"💥 错误: {str(e)[:100]}...")
        
        # 短暂间隔
        if i < total_tests:
            time.sleep(2)
    
    print(f"\n📊 测试结果:")
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"📈 成功率: {success_count/total_tests*100:.1f}%")
    
    return success_count == total_tests

def test_role_based_system_with_new_key():
    """用新密钥测试角色扮演系统"""
    print("\n🎭 测试角色扮演系统（新密钥）")
    print("=" * 50)
    
    new_api_key = "2d6b78b1032c403eb43ba59c28afed18.ZiDmzgNMRRX0Z9gY"
    
    roles_and_questions = [
        {
            "role": "技术专家",
            "system_prompt": "你是一位资深的技术专家，回答要简洁专业。",
            "question": "Python中列表和元组的主要区别是什么？"
        },
        {
            "role": "产品经理", 
            "system_prompt": "你是一位产品经理，从商业角度分析问题。",
            "question": "为什么要在产品中添加用户认证功能？"
        },
        {
            "role": "教学助手",
            "system_prompt": "你是编程教学助手，用简单易懂的方式解释。",
            "question": "什么是变量？请举个例子。"
        }
    ]
    
    for test in roles_and_questions:
        print(f"\n👤 角色: {test['role']}")
        print(f"❓ 问题: {test['question']}")
        
        try:
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.3,
                api_key=new_api_key,
                timeout=35
            )
            
            messages = [
                SystemMessage(content=test['system_prompt']),
                HumanMessage(content=test['question'])
            ]
            
            start_time = time.time()
            response = llm.invoke(messages)
            elapsed = time.time() - start_time
            
            print(f"✅ 成功！用时: {elapsed:.2f}秒")
            print(f"💬 [{test['role']}] {response.content[:150]}...")
            
        except Exception as e:
            print(f"❌ 失败: {str(e)[:100]}...")

if __name__ == "__main__":
    # 测试基本功能
    basic_success = test_new_api_key()
    
    # 如果基本测试成功，继续测试角色系统
    if basic_success:
        test_role_based_system_with_new_key()
        print("\n🎉 新API密钥测试完成！所有功能正常")
    else:
        print("\n⚠️ 新API密钥存在一些问题，但可能是网络原因")