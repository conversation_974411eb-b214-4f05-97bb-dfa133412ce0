# LangGraph 测试套件

这是《白话 LangGraph》项目的测试环境，使用 uv 进行管理。

## 环境设置

这个测试项目使用 uv 作为包管理器，已经预配置了以下测试工具：

- **pytest**: Python 测试框架
- **pytest-asyncio**: 异步测试支持
- **pytest-cov**: 代码覆盖率测试

## 快速开始

### 1. 激活虚拟环境

```bash
# 进入测试目录
cd test

# 使用 uv 进入虚拟环境
uv shell
```

### 2. 运行测试

```bash
# 运行所有测试
uv run pytest

# 运行指定测试文件
uv run pytest tests/test_example.py

# 运行指定测试函数
uv run pytest tests/test_example.py::test_basic_example

# 运行测试并生成覆盖率报告
uv run pytest --cov

# 运行测试并生成HTML覆盖率报告
uv run pytest --cov --cov-report=html
```

### 3. 查看覆盖率报告

HTML覆盖率报告会生成在 `htmlcov/` 目录中，用浏览器打开 `htmlcov/index.html` 查看。

## 编写测试

### 测试文件命名规则

- 测试文件以 `test_` 开头或以 `_test` 结尾
- 测试类以 `Test` 开头
- 测试函数以 `test_` 开头

### 示例测试

参考 `tests/test_example.py` 中的示例：

```python
def test_basic_example():
    """基本测试示例"""
    assert 1 + 1 == 2

@pytest.mark.asyncio
async def test_async_example():
    """异步测试示例"""
    # 异步测试代码
    pass
```

## 添加新依赖

```bash
# 添加新的依赖包
uv add package_name

# 添加开发依赖
uv add --dev package_name
```

## 测试标记

已配置的测试标记：

- `@pytest.mark.slow`: 标记慢速测试
- `@pytest.mark.integration`: 标记集成测试

使用方法：

```bash
# 跳过慢速测试
uv run pytest -m "not slow"

# 只运行集成测试
uv run pytest -m integration
```

## 项目结构

```
test/
├── tests/              # 测试文件目录
│   ├── __init__.py
│   └── test_example.py # 示例测试文件
├── .venv/              # 虚拟环境（uv 自动创建）
├── pyproject.toml      # 项目配置
├── README.md           # 说明文档
└── main.py             # 主程序文件
```

## 配置说明

测试配置在 `pyproject.toml` 的 `[tool.pytest.ini_options]` 部分，包括：

- 测试目录路径
- 文件和函数命名规则
- 覆盖率报告设置
- 测试标记定义

## 提示

1. 使用 `uv` 命令管理所有依赖和虚拟环境
2. 每次添加新测试时，确保遵循命名规则
3. 为复杂功能编写集成测试
4. 定期查看覆盖率报告，确保测试充分
