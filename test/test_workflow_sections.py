#!/usr/bin/env python3
"""
测试第04章4.3和4.4部分的工具工作流代码
验证条件执行、错误处理、状态管理等功能
"""

import os
import math
from typing import Annotated
from typing_extensions import TypedDict

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, ToolMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, tools_condition

# 设置GLM-4.5配置
os.environ.setdefault("OPENAI_API_KEY", "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW")
os.environ.setdefault("OPENAI_BASE_URL", "https://open.bigmodel.cn/api/paas/v4/")

# 定义基础状态
class AssistantState(TypedDict):
    messages: Annotated[list, add_messages]

# 定义增强状态
class EnhancedState(TypedDict):
    messages: Annotated[list, add_messages]
    tool_results: dict  # 存储工具执行结果
    context: dict  # 存储上下文信息
    user_preferences: dict  # 用户偏好

# 定义工具
@tool
def calculator(expression: str) -> str:
    """数学计算器，支持基本运算"""
    try:
        import math
        allowed_names = {
            k: v for k, v in math.__dict__.items() if not k.startswith("__")
        }
        allowed_names.update({"abs": abs, "round": round})
        allowed_names["math"] = math
        
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return f"计算结果：{result}"
    except Exception as e:
        return f"计算错误：{str(e)}"

@tool
def web_search(query: str) -> str:
    """网络搜索工具"""
    # 模拟搜索结果
    mock_results = {
        "人工智能": "最新AI发展：GPT-4、DALL-E、ChatGPT等技术突破",
        "新闻": "今日头条新闻：科技、经济、社会等各领域最新动态",
        "天气": "天气预报：今日晴天，温度适宜",
        "langgraph": "LangGraph：构建有状态多参与者应用的强大框架"
    }
    
    for key, result in mock_results.items():
        if key in query.lower():
            return f"搜索结果：{result}"
    
    return f"搜索结果：关于'{query}'的相关信息"

@tool
def get_weather(city: str) -> str:
    """获取天气信息"""
    weather_data = {
        "北京": "晴天，气温 25°C，湿度 45%",
        "上海": "多云，气温 22°C，湿度 60%",
        "广州": "小雨，气温 28°C，湿度 80%",
        "深圳": "晴天，气温 30°C，湿度 55%"
    }
    return weather_data.get(city, f"抱歉，暂时无法获取{city}的天气信息")

@tool
def current_time() -> str:
    """获取当前时间"""
    from datetime import datetime
    now = datetime.now()
    return f"当前时间：{now.strftime('%Y-%m-%d %H:%M:%S')}"

# 工具列表
tools = [calculator, web_search, get_weather, current_time]

# 4.3 条件执行：何时调用工具？

def smart_tool_routing(state: AssistantState) -> str:
    """智能工具路由决策"""
    last_message = state["messages"][-1]
    
    # 如果是 AI 消息且包含工具调用
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "execute_tools"
    
    # 分析用户意图
    user_content = ""
    for msg in reversed(state["messages"]):
        if isinstance(msg, HumanMessage):
            user_content = msg.content.lower()
            break
    
    # 基于关键词的简单路由
    if any(keyword in user_content for keyword in ["计算", "算", "数学"]):
        return "suggest_calculator"
    elif any(keyword in user_content for keyword in ["搜索", "查找", "搜"]):
        return "suggest_search"
    elif any(keyword in user_content for keyword in ["天气", "气温", "下雨"]):
        return "suggest_weather"
    elif any(keyword in user_content for keyword in ["时间", "几点", "现在"]):
        return "suggest_time"
    else:
        return "general_response"

def robust_tool_node(state: AssistantState):
    """带错误处理的工具节点"""
    messages = state["messages"]
    last_message = messages[-1]
    
    if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
        return {"messages": []}
    
    tool_results = []
    
    for tool_call in last_message.tool_calls:
        try:
            # 查找对应的工具
            tool_func = None
            for tool in tools:
                if tool.name == tool_call["name"]:
                    tool_func = tool
                    break
            
            if tool_func is None:
                result = f"错误：未找到工具 {tool_call['name']}"
            else:
                # 执行工具
                result = tool_func.invoke(tool_call["args"])
            
            # 创建工具消息
            tool_message = ToolMessage(
                content=result,
                tool_call_id=tool_call["id"],
                name=tool_call["name"]
            )
            tool_results.append(tool_message)
            
        except Exception as e:
            # 错误处理
            error_message = ToolMessage(
                content=f"工具执行失败：{str(e)}",
                tool_call_id=tool_call["id"],
                name=tool_call["name"]
            )
            tool_results.append(error_message)
    
    return {"messages": tool_results}

@tool
def research_and_summarize(topic: str) -> str:
    """研究主题并总结（组合工具示例）"""
    
    # 步骤1：搜索信息
    search_result = web_search.invoke({"query": topic})
    
    # 步骤2：如果涉及数字，进行计算
    if any(char.isdigit() for char in search_result):
        # 这里可以提取数字并进行相关计算
        pass
    
    # 步骤3：获取当前时间作为报告时间戳
    timestamp = current_time.invoke({})
    
    # 步骤4：组合结果
    summary = f"""
研究报告：{topic}

{timestamp}

搜索结果摘要：
{search_result[:200]}...

注：这是一个自动生成的研究摘要。
    """
    
    return summary

# 将组合工具添加到工具列表
advanced_tools = tools + [research_and_summarize]

# 4.4 把工具结果写回状态

def execute_tools(messages):
    """执行工具调用的辅助函数"""
    # 使用 ToolNode 来执行工具
    tool_node = ToolNode(tools)
    state = {"messages": messages}
    result = tool_node.invoke(state)
    return result.get("messages", [])

def enhanced_tool_node(state: EnhancedState):
    """增强的工具节点，更新多个状态字段"""
    
    # 执行工具调用（使用之前的逻辑）
    tool_messages = execute_tools(state["messages"])
    
    # 提取工具结果到专门的字段
    tool_results = {}
    context_updates = {}
    
    for msg in tool_messages:
        if isinstance(msg, ToolMessage):
            tool_results[msg.name] = msg.content
            
            # 根据工具类型更新上下文
            if msg.name == "get_weather":
                context_updates["last_weather_query"] = msg.content
            elif msg.name == "calculator":
                context_updates["last_calculation"] = msg.content
    
    return {
        "messages": tool_messages,
        "tool_results": tool_results,
        "context": context_updates
    }

def format_tool_results(tool_results: dict) -> str:
    """格式化工具结果为用户友好的文本"""
    if not tool_results:
        return ""
    
    formatted_parts = []
    for tool_name, result in tool_results.items():
        if tool_name == "calculator":
            formatted_parts.append(f"🧮 计算结果：{result}")
        elif tool_name == "web_search":
            formatted_parts.append(f"🔍 Tavily搜索发现：{result}")
        elif tool_name == "get_weather":
            formatted_parts.append(f"🌤️ 天气信息：{result}")
        elif tool_name == "current_time":
            formatted_parts.append(f"⏰ {result}")
        else:
            formatted_parts.append(f"🔧 {tool_name}：{result}")
    
    return "\n\n".join(formatted_parts)

def summary_node(state: EnhancedState):
    """总结节点：整合工具结果并生成最终回复"""
    
    # 获取工具结果
    tool_results = state.get("tool_results", {})
    formatted_results = format_tool_results(tool_results)
    
    if formatted_results:
        # 如果有工具结果，生成包含结果的回复
        try:
            llm = ChatOpenAI(model="glm-4.5", temperature=0.3)
            summary_prompt = f"""
基于以下工具执行结果，为用户生成一个友好、有用的回复：

{formatted_results}

请用自然的语言总结这些信息，并回答用户的问题。
            """
            
            response = llm.invoke([HumanMessage(content=summary_prompt)])
            return {"messages": [response]}
        except Exception as e:
            # 如果LLM调用失败，返回格式化的结果
            fallback_response = AIMessage(content=f"工具执行结果：\n{formatted_results}")
            return {"messages": [fallback_response]}
    else:
        # 没有工具结果，直接进行对话
        return {"messages": []}

def create_complete_tool_workflow():
    """创建完整的工具集成工作流"""
    
    graph = StateGraph(EnhancedState)
    
    # 意图分析节点
    def intent_analysis_node(state: EnhancedState):
        """分析用户意图"""
        last_user_message = ""
        for msg in reversed(state["messages"]):
            if isinstance(msg, HumanMessage):
                last_user_message = msg.content
                break
        
        # 简单的意图分析
        intent = "general"
        confidence = 0.5
        
        if any(kw in last_user_message.lower() for kw in ["计算", "算"]):
            intent = "calculation"
            confidence = 0.9
        elif any(kw in last_user_message.lower() for kw in ["搜索", "查"]):
            intent = "search"
            confidence = 0.8
        elif any(kw in last_user_message.lower() for kw in ["天气"]):
            intent = "weather"
            confidence = 0.9
        
        return {
            "context": {
                "intent": intent,
                "confidence": confidence,
                "analyzed_message": last_user_message
            }
        }
    
    # LLM 决策节点
    def llm_decision_node(state: EnhancedState):
        """LLM 决策是否需要工具"""
        try:
            llm = ChatOpenAI(model="glm-4.5", temperature=0.3)
            llm_with_tools = llm.bind_tools(tools)
            
            # 构造消息，包含上下文信息
            context = state.get("context", {})
            system_prompt = f"""
你是一个智能助手，可以使用工具来帮助用户。

当前分析的用户意图：{context.get('intent', 'unknown')}
置信度：{context.get('confidence', 0)}

根据用户的需求，决定是否需要使用工具，以及使用哪些工具。
            """
            
            messages = [SystemMessage(content=system_prompt)] + state["messages"]
            response = llm_with_tools.invoke(messages)
            return {"messages": [response]}
        except Exception as e:
            # 如果LLM调用失败，返回错误信息
            error_response = AIMessage(content=f"抱歉，处理您的请求时出现错误：{str(e)}")
            return {"messages": [error_response]}
    
    # 路由函数
    def route_after_llm(state: EnhancedState) -> str:
        """LLM 决策后的路由"""
        last_message = state["messages"][-1]
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "execute_tools"
        else:
            return "final_response"
    
    # 最终回复节点
    def final_response_node(state: EnhancedState):
        """生成最终回复"""
        # 如果没有工具调用，直接返回 LLM 的回复
        return {}
    
    # 构建图
    graph.add_node("intent_analysis", intent_analysis_node)
    graph.add_node("llm_decision", llm_decision_node)
    graph.add_node("execute_tools", enhanced_tool_node)
    graph.add_node("summary", summary_node)
    graph.add_node("final_response", final_response_node)
    
    # 设置边
    graph.add_edge(START, "intent_analysis")
    graph.add_edge("intent_analysis", "llm_decision")
    
    # 条件边：根据 LLM 决策路由
    graph.add_conditional_edges(
        "llm_decision",
        route_after_llm,
        {
            "execute_tools": "execute_tools",
            "final_response": "final_response"
        }
    )
    
    # 工具执行后进行总结
    graph.add_edge("execute_tools", "summary")
    
    # 结束边
    graph.add_edge("summary", END)
    graph.add_edge("final_response", END)
    
    return graph.compile()

def test_individual_functions():
    """测试各个函数的功能"""
    print("🔧 测试各个函数功能")
    print("=" * 60)
    
    # 测试智能路由
    print("1. 测试智能工具路由：")
    test_state = {
        "messages": [HumanMessage(content="帮我计算 2+3")]
    }
    routing_result = smart_tool_routing(test_state)
    print(f"   输入: '帮我计算 2+3' -> 路由结果: {routing_result}")
    
    test_state2 = {
        "messages": [HumanMessage(content="搜索人工智能新闻")]
    }
    routing_result2 = smart_tool_routing(test_state2)
    print(f"   输入: '搜索人工智能新闻' -> 路由结果: {routing_result2}")
    
    # 测试工具组合
    print("\n2. 测试研究总结工具：")
    research_result = research_and_summarize.invoke({"topic": "人工智能"})
    print(f"   研究结果: {research_result[:150]}...")
    
    # 测试工具结果格式化
    print("\n3. 测试工具结果格式化：")
    mock_tool_results = {
        "calculator": "计算结果：5",
        "web_search": "搜索结果：AI新闻...",
        "current_time": "当前时间：2024-01-01 12:00:00"
    }
    formatted = format_tool_results(mock_tool_results)
    print(f"   格式化结果:\n{formatted}")

def test_complete_workflow():
    """测试完整的工具工作流"""
    print("\n🚀 测试完整工具工作流")
    print("=" * 60)
    
    app = create_complete_tool_workflow()
    
    test_cases = [
        "帮我计算 123 * 456",
        "搜索最新的人工智能新闻", 
        "北京今天天气如何？",
        "你好，很高兴认识你",
        "现在几点了？然后帮我算一下 100 除以 7"
    ]
    
    for query in test_cases:
        print(f"\n📝 测试查询: {query}")
        print("-" * 40)
        
        try:
            result = app.invoke({
                "messages": [HumanMessage(content=query)],
                "tool_results": {},
                "context": {},
                "user_preferences": {}
            })
            
            # 显示最终结果
            if result["messages"]:
                last_message = result["messages"][-1]
                print(f"🤖 助手回复: {last_message.content}")
            
            # 显示工具使用情况
            if result.get("tool_results"):
                print(f"🔧 使用的工具: {list(result['tool_results'].keys())}")
            
            # 显示上下文信息
            if result.get("context"):
                print(f"📝 上下文: {result['context']}")
                
        except Exception as e:
            print(f"❌ 执行失败: {str(e)}")

def test_error_handling():
    """测试错误处理"""
    print("\n🛡️ 测试错误处理")
    print("=" * 60)
    
    # 模拟带工具调用的AI消息
    ai_message = AIMessage(
        content="我需要计算",
        tool_calls=[
            {
                "name": "calculator",
                "args": {"expression": "2+3"},
                "id": "call_1"
            },
            {
                "name": "nonexistent_tool",  # 不存在的工具
                "args": {"param": "value"},
                "id": "call_2"
            }
        ]
    )
    
    state = {"messages": [HumanMessage(content="计算2+3"), ai_message]}
    
    try:
        result = robust_tool_node(state)
        print("错误处理测试结果:")
        for msg in result["messages"]:
            print(f"  - {msg.name}: {msg.content}")
    except Exception as e:
        print(f"错误处理测试失败: {str(e)}")

if __name__ == "__main__":
    print("🔍 第04章工作流代码验证")
    print("=" * 60)
    
    # 测试各个函数
    test_individual_functions()
    
    # 测试错误处理
    test_error_handling()
    
    # 测试完整工作流
    test_complete_workflow()
    
    print("\n✅ 所有测试完成！")