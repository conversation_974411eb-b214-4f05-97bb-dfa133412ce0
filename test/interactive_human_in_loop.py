#!/usr/bin/env python3
"""
交互式人机协作演示
用户可以亲自体验 interrupt 机制
"""
import os
import uuid
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt, Command
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class WorkflowState(TypedDict):
    messages: Annotated[list, add_messages]
    user_request: str

def create_interactive_workflow():
    """创建交互式工作流"""
    
    def task_execution_node(state: WorkflowState):
        """任务执行节点 - 检查是否需要人工指导"""
        import os
        user_request = state.get("user_request", state["messages"][-1].content)
        
        print(f"🤖 AI正在分析任务: {user_request}")
        
        # 简单判断：包含这些关键词就请求人工指导
        if any(keyword in user_request for keyword in ["复杂", "困难", "不确定", "不知道", "需要帮助"]):
            print("📊 AI分析结果: 任务复杂，需要人工指导")
            
            # 使用interrupt请求人工指导
            guidance_request = {
                "task": user_request,
                "question": "这个任务比较复杂，您希望我如何处理？",
                "options": ["继续处理", "简化方式", "详细分析", "转交人工"]
            }
            
            print("⏸️ 工作流已暂停，请求人工指导...")
            human_guidance = interrupt(guidance_request)
            
            # 根据人工指导生成回复
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key)
                    
                    prompt = f"用户请求：{user_request}\n人工指导：{human_guidance}\n请用1-2句话简短回复。"
                    response = llm.invoke([HumanMessage(content=prompt)])
                    final_message = response.content
                    print(f"🧠 GLM-4.5处理完成")
                except Exception as e:
                    print(f"⚠️ GLM-4.5调用失败: {e}")
                    final_message = f"根据指导'{human_guidance}'，已处理您的请求。"
            else:
                final_message = f"根据指导'{human_guidance}'，已处理您的请求。"
        else:
            print("📊 AI分析结果: 任务简单，直接处理")
            
            # 简单任务直接处理
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key)
                    prompt = f"请简短回复：{user_request}"
                    response = llm.invoke([HumanMessage(content=prompt)])
                    final_message = response.content
                    print(f"🧠 GLM-4.5处理完成")
                except Exception as e:
                    print(f"⚠️ GLM-4.5调用失败: {e}")
                    final_message = f"已处理您的请求：{user_request}"
            else:
                final_message = f"已处理您的请求：{user_request}"
        
        return {"messages": [AIMessage(content=final_message)]}
    
    # 构建图
    from langgraph.checkpoint.memory import MemorySaver
    
    graph = StateGraph(WorkflowState)
    graph.add_node("task_execution", task_execution_node)
    graph.add_edge(START, "task_execution")
    
    # 使用checkpointer保存状态（支持interrupt）
    checkpointer = MemorySaver()
    return graph.compile(checkpointer=checkpointer)

def interactive_demo():
    """交互式演示"""
    print("🚀 交互式人机协作演示")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置")
    else:
        print("⚠️ 未配置智谱API，将使用模拟模式")
    print()
    
    # 创建应用
    app = create_interactive_workflow()
    print("✅ 成功创建交互式工作流")
    print()
    
    while True:
        # 获取用户输入
        print("请输入您的任务（输入 'quit' 退出）:")
        print("💡 提示: 包含'复杂'、'困难'、'不确定'等词会触发interrupt")
        user_input = input("👤 您: ").strip()
        
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
            
        if not user_input:
            print("❌ 请输入有效任务")
            continue
        
        print("\n" + "="*50)
        print(f"🎯 开始处理任务: {user_input}")
        print("-" * 50)
        
        # 配置线程ID
        config = {"configurable": {"thread_id": str(uuid.uuid4())}}
        
        try:
            # 执行任务
            result = app.invoke({
                "messages": [HumanMessage(content=user_input)],
                "user_request": user_input
            }, config=config)
            
            # 检查是否触发了interrupt
            if "__interrupt__" in result:
                print("\n🔔 触发了INTERRUPT！")
                interrupt_info = result["__interrupt__"][0]
                request_data = interrupt_info.value
                
                print(f"📋 任务: {request_data['task']}")
                print(f"❓ 问题: {request_data['question']}")
                print("🎯 可选指导:")
                for i, option in enumerate(request_data['options'], 1):
                    print(f"  {i}. {option}")
                
                print("\n请选择您的指导意见:")
                while True:
                    choice = input("👤 您的选择 (输入选项内容或编号): ").strip()
                    
                    if choice.isdigit():
                        idx = int(choice) - 1
                        if 0 <= idx < len(request_data['options']):
                            human_guidance = request_data['options'][idx]
                            break
                    elif choice in request_data['options']:
                        human_guidance = choice
                        break
                    else:
                        print("❌ 无效选择，请重新输入")
                
                print(f"\n📝 您选择了: {human_guidance}")
                print("🔄 恢复执行中...")
                
                # 使用Command恢复执行
                final_result = app.invoke(Command(resume=human_guidance), config=config)
                print(f"\n✅ 最终回复: {final_result['messages'][-1].content}")
            else:
                # 直接完成
                print(f"\n✅ 直接完成: {result['messages'][-1].content}")
                
        except Exception as e:
            print(f"❌ 执行失败: {e}")
        
        print("\n" + "="*50)
        print()

if __name__ == "__main__":
    interactive_demo()