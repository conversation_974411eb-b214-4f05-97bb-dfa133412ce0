#!/usr/bin/env python3
"""
6.2章节核心概念详解和可视化演示
展示interrupt机制的工作原理
"""
import os
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

def explain_chapter_6_2():
    """详细解释6.2章节的interrupt机制"""
    print("📚 6.2章节：interrupt 与 Command 机制详解")
    print("=" * 60)
    
    print("\n🎯 核心概念：")
    print("• interrupt：让AI在不确定时'举手'请求人工帮助")
    print("• Command：提供更灵活的状态更新和流程控制")
    print("• 人机协作：AI分析→人工审核→最终执行")
    
    print("\n⚡ interrupt机制的工作流程：")
    print("┌─────────────┐")
    print("│ 1. AI分析请求 │")
    print("└─────┬───────┘")
    print("      │")
    print("┌─────▼───────┐")
    print("│ 2. 风险评估   │ ──→ 低风险：直接执行")
    print("└─────┬───────┘")
    print("      │ 高风险/低置信度")
    print("┌─────▼───────┐")
    print("│ 3. 触发interrupt │ ⏸️ 暂停等待人工")
    print("└─────┬───────┘")
    print("      │ 人工决策")
    print("┌─────▼───────┐")
    print("│ 4. 执行动作   │")
    print("└─────────────┘")
    
    print("\n✨ 从测试结果看到的效果：")
    print("• 案例1 '你好！' → 低风险 → 自动通过 ✅")
    print("• 案例2 '删除文件' → 高风险 → 触发interrupt ⏸️")
    print("• 案例3 '发送通知' → 中风险 → 触发interrupt ⏸️")

def create_visual_demo():
    """创建可视化的interrupt演示"""
    print("\n🎬 可视化演示：人机协作决策过程")
    print("=" * 60)
    
    scenarios = [
        {
            "request": "帮我查询今天天气",
            "risk": "低",
            "confidence": 0.9,
            "need_review": False,
            "auto_decision": "直接执行",
            "result": "提供天气信息"
        },
        {
            "request": "删除服务器上的所有日志文件", 
            "risk": "高",
            "confidence": 0.8,
            "need_review": True,
            "auto_decision": "暂停并请求人工审核",
            "result": "等待人工决策：approved/rejected/修改"
        },
        {
            "request": "这个API调用我不太确定参数",
            "risk": "中", 
            "confidence": 0.4,
            "need_review": True,
            "auto_decision": "置信度太低，需要人工确认",
            "result": "请求技术专家协助"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🔍 场景 {i}: {scenario['request']}")
        print("─" * 50)
        print(f"📊 AI分析:")
        print(f"   • 风险等级: {scenario['risk']}")
        print(f"   • 置信度: {scenario['confidence']}")
        print(f"   • 需要审核: {'是' if scenario['need_review'] else '否'}")
        
        if scenario['need_review']:
            print(f"⏸️  INTERRUPT触发: {scenario['auto_decision']}")
            print(f"👤 等待人工决策...")
            print(f"🎯 预期结果: {scenario['result']}")
        else:
            print(f"✅ 自动执行: {scenario['result']}")
        
        print("─" * 50)

def demonstrate_real_world_usage():
    """演示真实场景中的应用"""
    print("\n🌍 真实应用场景：")
    print("=" * 60)
    
    print("1. 🏥 医疗AI助手：")
    print("   • 低风险：查询常见症状 → 直接回答")
    print("   • 高风险：诊断建议 → interrupt，等待医生确认")
    
    print("\n2. 💰 金融交易系统：")
    print("   • 低风险：查询余额 → 直接执行")
    print("   • 高风险：大额转账 → interrupt，等待风控审核")
    
    print("\n3. 🏭 工业控制系统：")
    print("   • 低风险：查看状态 → 直接执行")
    print("   • 高风险：停机操作 → interrupt，等待工程师确认")
    
    print("\n4. 📧 邮件营销系统：")
    print("   • 低风险：单个邮件 → 直接发送")
    print("   • 高风险：群发邮件 → interrupt，等待内容审核")

def show_code_highlights():
    """展示关键代码片段"""
    print("\n💻 关键代码解析：")
    print("=" * 60)
    
    print("1️⃣ 触发interrupt：")
    print("```python")
    print("def human_review_node(state):")
    print("    if state.get('requires_approval', False):")
    print("        review_info = {")
    print("            'user_request': state['messages'][-1].content,")
    print("            'ai_suggestion': state.get('pending_action'),")
    print("            'confidence': state.get('confidence_score')")
    print("        }")
    print("        return interrupt(review_info)  # ⚡ 关键：暂停执行")
    print("    return {'human_feedback': 'auto_approved'}")
    print("```")
    
    print("\n2️⃣ 风险评估逻辑：")
    print("```python")
    print("needs_approval = (")
    print("    confidence < 0.7 or      # 置信度低")
    print("    risk_level == '高' or    # 高风险")
    print("    '删除' in last_message   # 敏感操作")
    print(")")
    print("```")
    
    print("\n3️⃣ 人工决策处理：")
    print("```python")
    print("if human_feedback == 'rejected':")
    print("    return AIMessage(content='操作已被拒绝')")
    print("elif human_feedback == 'approved':")
    print("    return llm.invoke(state['messages'])  # 执行原始请求")
    print("else:")
    print("    # 根据人工反馈调整回复")
    print("    return llm.invoke(adjusted_prompt)")
    print("```")

if __name__ == "__main__":
    explain_chapter_6_2()
    create_visual_demo()
    demonstrate_real_world_usage() 
    show_code_highlights()
    
    print("\n🎉 总结：")
    print("6.2章节展示了如何通过interrupt机制实现真正的人机协作，")
    print("让AI在面对不确定或高风险情况时能够'举手'请求人工帮助！")