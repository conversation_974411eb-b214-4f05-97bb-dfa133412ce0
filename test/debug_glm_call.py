#!/usr/bin/env python3
"""
调试GLM-4.5调用问题
检查正确的模型名称和参数设置
"""
import os
from langchain_core.messages import HumanMessage, SystemMessage

def test_basic_glm_call():
    """测试基本的GLM调用"""
    print("🔧 测试GLM基本调用")
    print("=" * 40)
    
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ 未找到ZHIPUAI_API_KEY")
        return
        
    print(f"✅ API Key: {api_key[:10]}...{api_key[-6:]}")
    
    try:
        from langchain_community.chat_models import ChatZhipuAI
        
        # 测试不同的模型名称
        model_names = ["glm-4", "glm-4-plus", "glm-4.5", "glm-4-0520", "glm-4-air", "glm-4-airx", "glm-4-flash"]
        
        for model_name in model_names:
            print(f"\n🧪 测试模型: {model_name}")
            try:
                llm = ChatZhipuAI(
                    model=model_name, 
                    temperature=0.3, 
                    api_key=api_key,
                    timeout=10  # 设置10秒超时
                )
                
                response = llm.invoke([HumanMessage(content="你好，请简单介绍一下你自己")])
                print(f"✅ 成功！回复: {response.content[:50]}...")
                break  # 找到可用模型就停止
                
            except Exception as e:
                print(f"❌ 失败: {str(e)[:100]}...")
                continue
        else:
            print("❌ 所有模型都测试失败")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def test_structured_prompt():
    """测试结构化输出的提示"""
    print("\n📋 测试结构化输出提示")
    print("=" * 40)
    
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ 未找到ZHIPUAI_API_KEY")
        return
    
    try:
        from langchain_community.chat_models import ChatZhipuAI
        
        # 使用简化的提示，避免复杂的Pydantic格式
        llm = ChatZhipuAI(
            model="glm-4", 
            temperature=0.3, 
            api_key=api_key,
            timeout=15
        )
        
        simple_prompt = """
请将以下需求分解为任务列表，用JSON格式返回：

用户需求：我需要开发一个在线商城，包括用户注册、商品展示、购物车和支付功能

请返回如下格式：
{
  "project_name": "项目名称",
  "tasks": [
    {
      "title": "任务标题",
      "description": "任务描述", 
      "priority": "high/medium/low",
      "estimated_hours": 数字
    }
  ]
}
"""
        
        response = llm.invoke([HumanMessage(content=simple_prompt)])
        print(f"✅ 结构化输出成功: {response.content[:200]}...")
        
    except Exception as e:
        print(f"❌ 结构化输出失败: {e}")

if __name__ == "__main__":
    test_basic_glm_call()
    test_structured_prompt()