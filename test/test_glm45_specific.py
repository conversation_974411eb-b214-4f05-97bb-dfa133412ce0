#!/usr/bin/env python3
"""
专门测试GLM-4.5模型
"""
import os
from langchain_core.messages import HumanMessage, SystemMessage

def test_glm45_specifically():
    """专门测试GLM-4.5"""
    print("🧪 专门测试GLM-4.5")
    print("=" * 40)
    
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ 未找到ZHIPUAI_API_KEY")
        return
        
    print(f"✅ API Key: {api_key[:10]}...{api_key[-6:]}")
    
    try:
        from langchain_community.chat_models import ChatZhipuAI
        
        print("\n🚀 测试GLM-4.5基本调用...")
        llm = ChatZhipuAI(
            model="glm-4.5", 
            temperature=0.3, 
            api_key=api_key,
            timeout=20  # 增加超时时间
        )
        
        response = llm.invoke([HumanMessage(content="你好，请简单回复一下")])
        print(f"✅ GLM-4.5基本调用成功！")
        print(f"📝 回复: {response.content}")
        
        print("\n🎭 测试角色扮演...")
        system_prompt = """你是一位资深的技术专家，请简洁地回答技术问题。"""
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content="Python中列表和元组的区别是什么？")
        ]
        
        response = llm.invoke(messages)
        print(f"✅ 角色扮演测试成功！")
        print(f"📝 回复: {response.content[:100]}...")
        
        print("\n📋 测试结构化输出...")
        structured_prompt = """
请将以下需求分解为任务，用JSON格式返回：
需求：开发一个简单的博客系统

返回格式：
{
  "project": "项目名",
  "tasks": [
    {"title": "任务1", "hours": 10},
    {"title": "任务2", "hours": 15}
  ]
}
"""
        response = llm.invoke([HumanMessage(content=structured_prompt)])
        print(f"✅ 结构化输出测试成功！")
        print(f"📝 JSON输出: {response.content[:150]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ GLM-4.5测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

if __name__ == "__main__":
    success = test_glm45_specifically()
    if success:
        print("\n🎉 GLM-4.5完全可用！")
    else:
        print("\n⚠️ GLM-4.5存在问题")