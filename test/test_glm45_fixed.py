#!/usr/bin/env python3
"""
使用GLM-4.5测试修复后的工具工作流
验证原始问题是否得到解决
"""

import os
import re
import math
from typing import Annotated
from typing_extensions import TypedDict

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, ToolMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode

# 设置GLM-4.5配置
os.environ.setdefault("OPENAI_API_KEY", "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW")
os.environ.setdefault("OPENAI_BASE_URL", "https://open.bigmodel.cn/api/paas/v4/")

class EnhancedState(TypedDict):
    messages: Annotated[list, add_messages]
    tool_results: dict
    context: dict
    user_preferences: dict

# 定义工具
@tool
def calculator(expression: str) -> str:
    """数学计算器，支持基本运算"""
    try:
        import math
        allowed_names = {
            k: v for k, v in math.__dict__.items() if not k.startswith("__")
        }
        allowed_names.update({"abs": abs, "round": round})
        allowed_names["math"] = math
        
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return f"计算结果：{result}"
    except Exception as e:
        return f"计算错误：{str(e)}"

@tool
def current_time() -> str:
    """获取当前时间"""
    from datetime import datetime
    now = datetime.now()
    return f"当前时间：{now.strftime('%Y-%m-%d %H:%M:%S')}"

tools = [calculator, current_time]

# 修复后的 enhanced_tool_node
def robust_tool_node(state):
    """带错误处理和参数验证的工具节点"""
    messages = state["messages"]
    last_message = messages[-1]
    
    if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
        return {"messages": []}
    
    tool_results = []
    
    for tool_call in last_message.tool_calls:
        try:
            # 查找对应的工具
            tool_func = None
            for tool in tools:
                if tool.name == tool_call["name"]:
                    tool_func = tool
                    break
            
            if tool_func is None:
                result = f"错误：未找到工具 {tool_call['name']}"
            else:
                # 获取工具调用参数并进行验证
                tool_args = tool_call.get("args", {})
                
                # 参数验证和智能修复
                if not tool_args or tool_args == {}:
                    # 针对计算器工具的智能参数修复
                    if tool_call['name'] == 'calculator':
                        # 尝试从AI消息内容中提取数学表达式
                        ai_content = last_message.content.lower()
                        if any(op in ai_content for op in ['计算', '算', '+', '-', '*', '/', '除以', '乘以']):
                            # 提取可能的数学表达式
                            math_patterns = [
                                r'(\d+)\s*除以\s*(\d+)',  # "100除以7"
                                r'(\d+)\s*/\s*(\d+)',     # "100/7"
                                r'(\d+)\s*\*\s*(\d+)',    # "2*3"
                                r'(\d+)\s*\+\s*(\d+)',    # "2+3"
                                r'(\d+)\s*-\s*(\d+)',     # "5-2"
                            ]
                            
                            expression = None
                            for pattern in math_patterns:
                                match = re.search(pattern, ai_content)
                                if match:
                                    if '除以' in pattern:
                                        expression = f"{match.group(1)} / {match.group(2)}"
                                    elif '/' in pattern:
                                        expression = f"{match.group(1)} / {match.group(2)}"
                                    elif '*' in pattern:
                                        expression = f"{match.group(1)} * {match.group(2)}"
                                    elif '+' in pattern:
                                        expression = f"{match.group(1)} + {match.group(2)}"
                                    elif '-' in pattern:
                                        expression = f"{match.group(1)} - {match.group(2)}"
                                    break
                            
                            if expression:
                                tool_args = {"expression": expression}
                                result = tool_func.invoke(tool_args)
                            else:
                                result = "错误：无法识别要计算的数学表达式，请明确指定计算内容"
                        else:
                            result = "错误：calculator工具缺少必要的expression参数"
                    else:
                        result = f"错误：工具 {tool_call['name']} 缺少必要参数"
                else:
                    # 正常执行工具
                    result = tool_func.invoke(tool_args)
            
            # 创建工具消息
            tool_message = ToolMessage(
                content=result,
                tool_call_id=tool_call["id"],
                name=tool_call["name"]
            )
            tool_results.append(tool_message)
            
        except Exception as e:
            # 错误处理
            error_message = ToolMessage(
                content=f"工具执行失败：{str(e)}",
                tool_call_id=tool_call["id"],
                name=tool_call["name"]
            )
            tool_results.append(error_message)
    
    return {"messages": tool_results}

def enhanced_tool_node(state: EnhancedState):
    """增强的工具节点，更新多个状态字段"""
    
    # 使用改进的工具执行逻辑
    tool_messages = robust_tool_node(state)["messages"]
    
    # 提取工具结果到专门的字段
    tool_results = {}
    context_updates = {}
    
    for msg in tool_messages:
        if isinstance(msg, ToolMessage):
            tool_results[msg.name] = msg.content
            
            # 根据工具类型更新上下文
            if msg.name == "calculator":
                context_updates["last_calculation"] = msg.content
            elif msg.name == "current_time":
                context_updates["last_time_query"] = msg.content
    
    return {
        "messages": tool_messages,
        "tool_results": tool_results,
        "context": context_updates
    }

def summary_node(state: EnhancedState):
    """总结节点：整合工具结果并生成最终回复"""
    
    # 获取工具结果
    tool_results = state.get("tool_results", {})
    
    if tool_results:
        # 格式化工具结果
        formatted_parts = []
        for tool_name, result in tool_results.items():
            if tool_name == "calculator":
                formatted_parts.append(f"🧮 {result}")
            elif tool_name == "current_time":
                formatted_parts.append(f"⏰ {result}")
            else:
                formatted_parts.append(f"🔧 {tool_name}：{result}")
        
        formatted_results = "\n\n".join(formatted_parts)
        
        try:
            llm = ChatOpenAI(model="glm-4.5", temperature=0.3)
            summary_prompt = f"""
基于以下工具执行结果，为用户生成一个友好、有用的回复：

{formatted_results}

请用自然的语言总结这些信息，并回答用户的问题。
            """
            
            response = llm.invoke([HumanMessage(content=summary_prompt)])
            return {"messages": [response]}
        except Exception as e:
            # 如果LLM调用失败，返回格式化的结果
            fallback_response = AIMessage(content=f"工具执行结果：\n{formatted_results}")
            return {"messages": [fallback_response]}
    else:
        # 没有工具结果，直接进行对话
        return {"messages": []}

def create_fixed_workflow():
    """创建修复后的工具工作流"""
    
    graph = StateGraph(EnhancedState)
    
    # LLM 决策节点
    def llm_decision_node(state: EnhancedState):
        """LLM 决策是否需要工具"""
        try:
            llm = ChatOpenAI(model="glm-4.5", temperature=0.3)
            llm_with_tools = llm.bind_tools(tools)
            
            system_prompt = """
你是一个智能助手，可以使用工具来帮助用户。

可用工具：
1. calculator(expression: str) - 数学计算器，需要数学表达式参数
2. current_time() - 获取当前时间，无需参数

根据用户的需求，决定是否需要使用工具，以及使用哪些工具。
如果需要计算，请确保传递正确的数学表达式参数。
            """
            
            messages = [SystemMessage(content=system_prompt)] + state["messages"]
            response = llm_with_tools.invoke(messages)
            return {"messages": [response]}
        except Exception as e:
            error_response = AIMessage(content=f"抱歉，处理您的请求时出现错误：{str(e)}")
            return {"messages": [error_response]}
    
    # 路由函数
    def route_after_llm(state: EnhancedState) -> str:
        """LLM 决策后的路由"""
        last_message = state["messages"][-1]
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "execute_tools"
        else:
            return "final_response"
    
    # 最终回复节点
    def final_response_node(state: EnhancedState):
        """生成最终回复"""
        return {}
    
    # 构建图
    graph.add_node("llm_decision", llm_decision_node)
    graph.add_node("execute_tools", enhanced_tool_node)
    graph.add_node("summary", summary_node)
    graph.add_node("final_response", final_response_node)
    
    # 设置边
    graph.add_edge(START, "llm_decision")
    
    # 条件边：根据 LLM 决策路由
    graph.add_conditional_edges(
        "llm_decision",
        route_after_llm,
        {
            "execute_tools": "execute_tools",
            "final_response": "final_response"
        }
    )
    
    # 工具执行后进行总结
    graph.add_edge("execute_tools", "summary")
    
    # 结束边
    graph.add_edge("summary", END)
    graph.add_edge("final_response", END)
    
    return graph.compile()

def test_original_problem():
    """测试原始问题是否得到解决"""
    print("🔍 测试原始问题修复")
    print("=" * 60)
    
    app = create_fixed_workflow()
    
    # 这是原来失败的测试用例
    problematic_query = "现在几点了？然后帮我算一下 100 除以 7"
    
    print(f"🤔 用户：{problematic_query}")
    
    try:
        result = app.invoke({
            "messages": [HumanMessage(content=problematic_query)],
            "tool_results": {},
            "context": {},
            "user_preferences": {}
        })
        
        # 显示最终结果
        if result["messages"]:
            last_message = result["messages"][-1]
            print(f"🤖 助手：{last_message.content}")
        
        # 显示工具使用情况
        if result.get("tool_results"):
            print(f"\n🔧 使用的工具：{list(result['tool_results'].keys())}")
            for tool_name, tool_result in result['tool_results'].items():
                print(f"   {tool_name}: {tool_result}")
        
        # 显示上下文信息
        if result.get("context"):
            print(f"\n📝 上下文：{result['context']}")
        
        # 检查是否成功解决了原始问题
        if result.get("tool_results"):
            has_time = "current_time" in result["tool_results"]
            has_calc = "calculator" in result["tool_results"]
            calc_success = has_calc and "计算结果" in result["tool_results"].get("calculator", "")
            
            print(f"\n✅ 修复验证结果：")
            print(f"   时间工具: {'✅' if has_time else '❌'}")
            print(f"   计算工具: {'✅' if has_calc else '❌'}")
            print(f"   计算成功: {'✅' if calc_success else '❌'}")
            
            if has_time and calc_success:
                print(f"   🎉 原始问题已成功修复！")
            else:
                print(f"   ⚠️ 仍存在问题需要进一步调试")
        else:
            print(f"   ❌ 没有工具执行结果")
            
    except Exception as e:
        print(f"❌ 测试执行失败：{str(e)}")

def test_additional_cases():
    """测试其他修复后的用例"""
    print("\n🔍 测试其他修复用例")
    print("=" * 60)
    
    app = create_fixed_workflow()
    
    test_cases = [
        "帮我计算 123 * 456",
        "算一下 50 除以 3",
        "现在几点了？",
        "计算 2+3，然后告诉我时间"
    ]
    
    for query in test_cases:
        print(f"\n📝 测试：{query}")
        print("-" * 40)
        
        try:
            result = app.invoke({
                "messages": [HumanMessage(content=query)],
                "tool_results": {},
                "context": {},
                "user_preferences": {}
            })
            
            if result.get("tool_results"):
                print(f"   🔧 工具结果：{list(result['tool_results'].keys())}")
                for tool_name, tool_result in result['tool_results'].items():
                    print(f"      {tool_name}: {tool_result}")
                print(f"   ✅ 成功")
            else:
                print(f"   ⚠️ 无工具调用")
                
        except Exception as e:
            print(f"   ❌ 失败：{str(e)}")

if __name__ == "__main__":
    print("🔍 GLM-4.5 修复验证测试")
    print("=" * 60)
    
    # 测试原始问题
    test_original_problem()
    
    # 测试其他用例
    test_additional_cases()
    
    print("\n🎉 修复验证完成！")