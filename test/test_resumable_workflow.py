#!/usr/bin/env python3
"""
6.3章节 - 可恢复工作流交互式测试
让用户亲自体验恢复执行和人工反馈处理
"""
import os
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class WorkflowState(TypedDict):
    messages: Annotated[list, add_messages]
    workflow_step: str
    human_input: str
    approval_status: str
    retry_count: int
    max_retries: int

def create_resumable_workflow():
    """创建可恢复的工作流"""
    
    def task_execution_node(state: WorkflowState):
        """任务执行节点"""
        current_step = state.get("workflow_step", "start")
        print(f"📍 当前步骤: {current_step}")
        
        if current_step == "start":
            print("🚀 开始处理任务...")
            return {
                "workflow_step": "processing",
                "messages": [AIMessage(content="开始处理您的请求...")]
            }
        elif current_step == "processing":
            last_message = state["messages"][-1].content
            print(f"🔍 分析请求: {last_message}")
            
            if "复杂" in last_message or "困难" in last_message:
                print("⏸️ 遇到复杂任务，请求人工指导...")
                guidance_request = {
                    "type": "guidance_needed",
                    "current_task": last_message,
                    "specific_question": "这个任务比较复杂，您希望我如何处理？",
                    "options": [
                        "继续尝试自动处理",
                        "简化处理方式",
                        "暂停等待更多信息",
                        "转交人工处理"
                    ]
                }
                return interrupt(guidance_request)
            else:
                print("✅ 任务处理完成")
                return {
                    "workflow_step": "completed",
                    "messages": [AIMessage(content="任务处理完成！")]
                }
        
        return {"workflow_step": "error"}
    
    def feedback_processing_node(state: WorkflowState):
        """反馈处理节点"""
        human_input = state.get("human_input", "")
        retry_count = state.get("retry_count", 0)
        max_retries = state.get("max_retries", 3)
        
        print(f"🤖 处理人工反馈: '{human_input}'")
        
        if not human_input:
            # 没有人工输入，可能是自动恢复
            print("🔄 自动继续")
            return {"approval_status": "auto_continue"}
        
        # 解析人工指令
        if "继续" in human_input:
            print("✅ 收到继续指令")
            return {
                "approval_status": "continue",
                "workflow_step": "processing"
            }
        elif "简化" in human_input:
            print("🔧 收到简化指令")
            return {
                "approval_status": "simplify",
                "workflow_step": "processing",
                "messages": [AIMessage(content="好的，我将采用简化的处理方式。")]
            }
        elif "暂停" in human_input:
            print("⏸️ 收到暂停指令")
            return {
                "approval_status": "pause",
                "workflow_step": "waiting"
            }
        elif "转交" in human_input:
            print("👤 收到转交指令")
            return {
                "approval_status": "escalate",
                "workflow_step": "human_takeover",
                "messages": [AIMessage(content="好的，我将把这个任务转交给人工处理。")]
            }
        elif "重试" in human_input:
            if retry_count < max_retries:
                print(f"🔄 收到重试指令 (第{retry_count + 1}次)")
                return {
                    "approval_status": "retry",
                    "workflow_step": "processing",
                    "retry_count": retry_count + 1
                }
            else:
                print(f"❌ 已达到最大重试次数 ({max_retries})")
                return {
                    "approval_status": "max_retries_reached",
                    "workflow_step": "failed",
                    "messages": [AIMessage(content="已达到最大重试次数，任务失败。")]
                }
        else:
            # 自定义指令
            print(f"📝 收到自定义指令: {human_input}")
            return {
                "approval_status": "custom_instruction",
                "workflow_step": "processing",
                "messages": [AIMessage(content=f"收到指令：{human_input}，我将按此执行。")]
            }
    
    def workflow_router(state: WorkflowState) -> str:
        """工作流路由"""
        workflow_step = state.get("workflow_step", "start")
        approval_status = state.get("approval_status", "")
        
        print(f"🧭 路由决策: step={workflow_step}, status={approval_status}")
        
        if workflow_step == "completed":
            return "end"
        elif workflow_step == "failed":
            return "end"
        elif workflow_step == "human_takeover":
            return "end"
        elif workflow_step == "waiting":
            return "pause"
        else:
            return "continue_processing"
    
    # 构建图
    graph = StateGraph(WorkflowState)
    graph.add_node("task_execution", task_execution_node)
    graph.add_node("feedback_processing", feedback_processing_node)
    graph.add_node("pause", lambda state: {"workflow_step": "paused"})
    graph.add_node("continue_processing", task_execution_node)
    
    # 设置流程
    graph.add_edge(START, "task_execution")
    graph.add_edge("task_execution", "feedback_processing")
    
    # 条件路由
    graph.add_conditional_edges(
        "feedback_processing",
        workflow_router,
        {
            "end": END,
            "pause": "pause",
            "continue_processing": "continue_processing"
        }
    )
    
    graph.add_edge("continue_processing", "feedback_processing")
    graph.add_edge("pause", END)
    
    return graph.compile()

def interactive_resumable_workflow():
    """交互式可恢复工作流演示"""
    print("🚀 6.3章节：可恢复工作流交互式演示")
    print("=" * 60)
    print("🤖 AI会在遇到复杂情况时请求人工指导")
    print("📋 支持多种人工指令：继续、简化、暂停、转交、重试")
    print("🔄 工作流可以根据人工反馈恢复执行")
    print()
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-4:]})")
    else:
        print("⚠️ 未配置智谱API，将使用模拟模式")
    
    try:
        app = create_resumable_workflow()
        print("✅ 可恢复工作流已创建")
    except Exception as e:
        print(f"❌ 创建工作流失败: {e}")
        return
    
    print("\n📝 建议测试的任务类型:")
    print("• 简单任务: '帮我查询天气'")  
    print("• 复杂任务: '这是一个复杂的数据分析任务' '这个困难的问题需要处理'")
    print("• 输入 'quit' 退出")
    print()
    
    while True:
        print("-" * 50)
        user_request = input("👤 请输入您的任务请求: ").strip()
        
        if user_request.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
        
        if not user_request:
            continue
            
        print(f"\n🔄 开始处理任务: {user_request}")
        print("=" * 50)
        
        # 初始状态
        initial_state = {
            "messages": [HumanMessage(content=user_request)],
            "workflow_step": "start",
            "human_input": "",
            "approval_status": "",
            "retry_count": 0,
            "max_retries": 3
        }
        
        current_state = initial_state
        
        while True:
            try:
                # 执行工作流
                result = app.invoke(current_state)
                
                # 检查是否完成
                final_step = result.get("workflow_step", "")
                if final_step in ["completed", "failed", "human_takeover", "paused"]:
                    print(f"\n🎯 工作流结束: {final_step}")
                    if "messages" in result and result["messages"]:
                        print(f"💬 最终消息: {result['messages'][-1].content}")
                    break
                else:
                    print("🤔 工作流状态异常")
                    break
                    
            except Exception as e:
                error_str = str(e)
                if "interrupt" in error_str.lower():
                    print("\n" + "🔴" * 20 + " INTERRUPT触发 " + "🔴" * 20)
                    print("⏸️ AI请求人工指导！")
                    
                    # 解析interrupt数据
                    try:
                        import json
                        # 简单解析interrupt信息
                        print("📋 请求详情:")
                        if "guidance_needed" in error_str:
                            print("   • 类型: 需要人工指导")
                            print("   • 原因: 遇到复杂任务")
                        print(f"   • 具体内容: {user_request}")
                    except:
                        print(f"   • 原始信息: {error_str}")
                    
                    print("\n💡 可用指令:")
                    instructions = [
                        "继续尝试自动处理",
                        "简化处理方式", 
                        "暂停等待更多信息",
                        "转交人工处理",
                        "重试一次"
                    ]
                    for i, instruction in enumerate(instructions, 1):
                        print(f"   {i}. {instruction}")
                    
                    # 获取人工指导
                    print()
                    human_guidance = input("👤 请输入您的指导 (或输入对应数字): ").strip()
                    
                    # 处理数字选择
                    if human_guidance.isdigit():
                        idx = int(human_guidance) - 1
                        if 0 <= idx < len(instructions):
                            human_guidance = instructions[idx]
                    
                    print(f"📝 收到指导: {human_guidance}")
                    
                    # 更新状态并继续
                    current_state = {
                        **result,
                        "human_input": human_guidance
                    }
                    continue
                    
                else:
                    print(f"❌ 其他错误: {error_str}")
                    break
        
        print("=" * 50)

if __name__ == "__main__":
    interactive_resumable_workflow()