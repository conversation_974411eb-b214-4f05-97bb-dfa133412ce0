#!/usr/bin/env python3
"""
测试第10章：部署与上线
验证部署相关功能：生产级聊天机器人、监控集成、指标收集等
"""
import os
import time
import json
from datetime import datetime
from typing_extensions import TypedDict
from typing import Annotated, Dict, Any, Optional

from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

# 检查API密钥
def check_api_key():
    """检查API密钥配置"""
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-6:]})")
        return True
    else:
        print("⚠️ 未配置智谱API密钥，将使用模拟模式")
        return False

# ===== 生产级状态定义 =====
class ProductionState(TypedDict):
    messages: Annotated[list, add_messages]
    user_id: str
    session_metadata: dict

# ===== 监控系统模拟 =====
class MockLangSmithClient:
    """模拟 LangSmith 客户端"""
    
    def __init__(self, api_url: str, api_key: str):
        self.api_url = api_url
        self.api_key = api_key
        self.runs = []
        self.datasets = []
        print(f"🔗 LangSmith 客户端初始化: {api_url}")
    
    def create_run(self, **kwargs):
        """创建运行记录"""
        run_id = f"run_{len(self.runs)}_{datetime.now().timestamp()}"
        run_record = {
            "run_id": run_id,
            "timestamp": datetime.now().isoformat(),
            **kwargs
        }
        self.runs.append(run_record)
        print(f"📝 记录对话: {kwargs.get('name', 'unknown')} -> {run_id}")
        return {"run_id": run_id}
    
    def create_dataset(self, dataset_name: str, description: str):
        """创建数据集"""
        dataset_id = f"dataset_{len(self.datasets)}_{datetime.now().timestamp()}"
        dataset = {
            "dataset_id": dataset_id,
            "name": dataset_name,
            "description": description,
            "examples": []
        }
        self.datasets.append(dataset)
        print(f"📊 创建数据集: {dataset_name} -> {dataset_id}")
        return {"dataset_id": dataset_id}
    
    def create_example(self, dataset_id: str, inputs: Dict, outputs: Dict):
        """创建示例"""
        for dataset in self.datasets:
            if dataset["dataset_id"] == dataset_id:
                example = {
                    "inputs": inputs,
                    "outputs": outputs,
                    "timestamp": datetime.now().isoformat()
                }
                dataset["examples"].append(example)
                print(f"➕ 添加示例到数据集: {dataset_id}")
                return
        print(f"❌ 数据集不存在: {dataset_id}")
    
    def get_stats(self):
        """获取统计信息"""
        return {
            "total_runs": len(self.runs),
            "total_datasets": len(self.datasets),
            "latest_run": self.runs[-1] if self.runs else None
        }

class LangSmithMonitoring:
    """LangSmith 监控集成"""
    
    def __init__(self):
        self.client = MockLangSmithClient(
            api_url=os.getenv("LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com"),
            api_key=os.getenv("LANGCHAIN_API_KEY", "mock_key")
        )
        self.project_name = os.getenv("LANGCHAIN_PROJECT", "langgraph-production")
    
    def setup_tracing(self):
        """设置链路追踪"""
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
        os.environ["LANGCHAIN_PROJECT"] = self.project_name
        print(f"🔍 启用链路追踪: {self.project_name}")
        return True
    
    def log_conversation(self, 
                        session_id: str, 
                        user_message: str, 
                        ai_response: str, 
                        processing_time: float, 
                        metadata: Optional[Dict] = None):
        """记录对话"""
        run_data = {
            "name": "chat_interaction",
            "run_type": "chain",
            "inputs": {"user_message": user_message},
            "outputs": {"ai_response": ai_response},
            "session_id": session_id,
            "extra": {
                "processing_time": processing_time,
                "timestamp": datetime.now().isoformat(),
                **(metadata or {})
            }
        }
        
        try:
            result = self.client.create_run(**run_data)
            return result
        except Exception as e:
            print(f"❌ 记录对话失败: {e}")
            return None
    
    def log_error(self, session_id: str, error: str, context: Dict):
        """记录错误"""
        error_data = {
            "name": "error_event",
            "run_type": "tool",
            "inputs": context,
            "outputs": {"error": error},
            "session_id": session_id,
            "extra": {
                "timestamp": datetime.now().isoformat(),
                "severity": "error"
            }
        }
        
        try:
            result = self.client.create_run(**error_data)
            print(f"⚠️ 错误记录成功: {result['run_id']}")
            return result
        except Exception as e:
            print(f"❌ 记录错误失败: {e}")
            return None
    
    def get_stats(self):
        """获取统计信息"""
        return self.client.get_stats()

# ===== 指标收集系统 =====
from collections import defaultdict

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.request_count = defaultdict(int)
        self.request_duration = []
        self.active_sessions = set()
        self.llm_token_usage = defaultdict(int)
        self.error_count = defaultdict(int)
        self.start_time = time.time()
    
    def track_session(self, session_id: str):
        """会话追踪"""
        self.active_sessions.add(session_id)
        print(f"📊 开始追踪会话: {session_id}")
    
    def end_session(self, session_id: str):
        """结束会话追踪"""
        self.active_sessions.discard(session_id)
        print(f"📊 结束追踪会话: {session_id}")
    
    def track_request(self, endpoint: str, duration: float, status: str = "success"):
        """追踪请求"""
        self.request_count[f"{endpoint}_{status}"] += 1
        self.request_duration.append(duration)
        
        # 保持最近1000个请求的记录
        if len(self.request_duration) > 1000:
            self.request_duration = self.request_duration[-1000:]
    
    def track_llm_usage(self, model: str, tokens: int):
        """LLM 使用量追踪"""
        self.llm_token_usage[model] += tokens
        print(f"🔢 Token使用: {model} +{tokens}")
    
    def track_error(self, endpoint: str, error_type: str):
        """追踪错误"""
        self.error_count[f"{endpoint}_{error_type}"] += 1
        print(f"❌ 错误追踪: {endpoint} - {error_type}")
    
    def get_metrics(self) -> dict:
        """获取当前指标"""
        uptime = time.time() - self.start_time
        avg_duration = sum(self.request_duration) / len(self.request_duration) if self.request_duration else 0
        
        return {
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": uptime,
            "request_count": dict(self.request_count),
            "error_count": dict(self.error_count),
            "average_response_time": avg_duration,
            "active_sessions": len(self.active_sessions),
            "llm_token_usage": dict(self.llm_token_usage),
            "total_requests": sum(self.request_count.values()),
            "total_errors": sum(self.error_count.values())
        }
    
    def reset_metrics(self):
        """重置指标"""
        self.request_count.clear()
        self.request_duration.clear()
        self.llm_token_usage.clear()
        self.error_count.clear()
        print("🔄 指标已重置")

# ===== 生产级聊天机器人 =====
def create_test_production_chatbot():
    """创建测试版生产级聊天机器人"""
    
    # 全局监控和指标
    monitoring = LangSmithMonitoring()
    metrics = MetricsCollector()
    
    monitoring.setup_tracing()
    
    def production_chat_node(state: ProductionState):
        """生产级聊天节点"""
        start_time = datetime.now()
        session_id = state.get("user_id", "test_session")
        user_message = state["messages"][-1].content
        
        # 追踪会话
        metrics.track_session(session_id)
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                
                llm = ChatZhipuAI(
                    model=os.getenv("ZHIPU_MODEL", "glm-4.5"),
                    temperature=float(os.getenv("ZHIPU_TEMPERATURE", "0.7")),
                    api_key=api_key,
                    timeout=35,
                    max_retries=2
                )
                
                system_prompt = """
你是一个友好、专业的生产级AI助手。
请提供准确、有用的回答，保持礼貌和专业。
"""
                
                messages = [SystemMessage(content=system_prompt)] + state["messages"]
                response = llm.invoke(messages)
                
                # 计算处理时间和记录
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # 记录成功交互
                monitoring.log_conversation(
                    session_id=session_id,
                    user_message=user_message,
                    ai_response=response.content,
                    processing_time=processing_time,
                    metadata={"status": "success", "model": "glm-4.5"}
                )
                
                # 更新指标
                metrics.track_request("chat", processing_time, "success")
                metrics.track_llm_usage("glm-4.5", len(response.content) // 4)  # 估算token数
                
                return {"messages": [response]}
                
            except Exception as e:
                # 处理错误
                processing_time = (datetime.now() - start_time).total_seconds()
                error_msg = f"抱歉，系统繁忙，请稍后重试。"
                
                # 记录错误
                monitoring.log_error(
                    session_id=session_id,
                    error=str(e),
                    context={"user_message": user_message, "processing_time": processing_time}
                )
                
                metrics.track_request("chat", processing_time, "error")
                metrics.track_error("chat", "llm_error")
                
                return {"messages": [AIMessage(content=error_msg)]}
        else:
            # 模拟模式
            processing_time = 1.0  # 模拟处理时间
            mock_response = f"[模拟回复] 我理解您的问题：{user_message[:50]}..."
            
            # 记录模拟交互
            monitoring.log_conversation(
                session_id=session_id,
                user_message=user_message,
                ai_response=mock_response,
                processing_time=processing_time,
                metadata={"status": "mock", "model": "simulation"}
            )
            
            metrics.track_request("chat", processing_time, "success")
            metrics.track_llm_usage("simulation", 50)
            
            return {"messages": [AIMessage(content=mock_response)]}
    
    def call_with_retry(state: ProductionState):
        """带重试机制的调用"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                return production_chat_node(state)
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"⚠️ 重试 {attempt + 1}/{max_retries}: {e}")
                    time.sleep(1)
                    continue
                else:
                    # 最后一次尝试失败
                    error_message = AIMessage(content="抱歉，系统暂时不可用，请稍后重试。")
                    return {"messages": [error_message]}
    
    # 构建图
    graph = StateGraph(ProductionState)
    graph.add_node("chat", call_with_retry)
    graph.add_edge(START, "chat")
    graph.add_edge("chat", END)
    
    # 返回编译后的图和监控组件
    compiled_graph = graph.compile()
    return compiled_graph, monitoring, metrics

# ===== 配置管理测试 =====
class CloudConfig:
    """云端配置管理"""
    
    def __init__(self):
        self.zhipuai_api_key = os.getenv("ZHIPUAI_API_KEY")
        self.model_name = os.getenv("MODEL_NAME", "glm-4.5")
        self.temperature = float(os.getenv("TEMPERATURE", "0.7"))
        self.timeout = int(os.getenv("TIMEOUT", "35"))
    
    def get_llm_config(self) -> Dict[str, Any]:
        """获取 LLM 配置"""
        return {
            "model": self.model_name,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "api_key": self.zhipuai_api_key
        }
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return os.getenv("SYSTEM_PROMPT", "你是一个专业的AI助手。")
    
    def validate_config(self):
        """验证配置"""
        issues = []
        
        if not self.zhipuai_api_key:
            issues.append("ZHIPUAI_API_KEY 未设置")
        
        if self.temperature < 0 or self.temperature > 1:
            issues.append(f"TEMPERATURE 值无效: {self.temperature}")
        
        if self.timeout <= 0:
            issues.append(f"TIMEOUT 值无效: {self.timeout}")
        
        return issues

# ===== 测试函数 =====
def test_production_chatbot():
    """测试生产级聊天机器人"""
    print("\n🚀 测试生产级聊天机器人")
    print("=" * 60)
    
    try:
        chatbot, monitoring, metrics = create_test_production_chatbot()
        
        test_messages = [
            "你好，我需要帮助",
            "请解释一下机器学习",
            "什么是部署？"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n--- 测试 {i} ---")
            print(f"❓ 用户: {message}")
            
            try:
                result = chatbot.invoke({
                    "messages": [HumanMessage(content=message)],
                    "user_id": f"test_user_{i}",
                    "session_metadata": {"test": True}
                })
                
                ai_response = result["messages"][-1].content
                print(f"🤖 AI: {ai_response[:100]}...")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        # 显示监控统计
        print(f"\n📊 监控统计:")
        stats = monitoring.get_stats()
        print(f"   总运行数: {stats['total_runs']}")
        print(f"   数据集数: {stats['total_datasets']}")
        
        # 显示指标
        print(f"\n📈 系统指标:")
        current_metrics = metrics.get_metrics()
        print(f"   总请求数: {current_metrics['total_requests']}")
        print(f"   活跃会话: {current_metrics['active_sessions']}")
        print(f"   平均响应时间: {current_metrics['average_response_time']:.2f}秒")
        print(f"   Token使用: {dict(current_metrics['llm_token_usage'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生产级聊天机器人测试失败: {e}")
        return False

def test_monitoring_system():
    """测试监控系统"""
    print("\n🔍 测试监控系统")
    print("=" * 60)
    
    try:
        monitoring = LangSmithMonitoring()
        monitoring.setup_tracing()
        
        # 测试对话记录
        print("\n📝 测试对话记录:")
        result = monitoring.log_conversation(
            session_id="test_session_monitor",
            user_message="测试用户消息",
            ai_response="测试AI回复",
            processing_time=1.25,
            metadata={"test": True, "model": "glm-4.5"}
        )
        
        if result:
            print(f"✅ 对话记录成功: {result['run_id']}")
        else:
            print("❌ 对话记录失败")
        
        # 测试错误记录
        print("\n⚠️ 测试错误记录:")
        error_result = monitoring.log_error(
            session_id="test_session_error",
            error="测试错误信息",
            context={"action": "test_error", "timestamp": datetime.now().isoformat()}
        )
        
        if error_result:
            print(f"✅ 错误记录成功")
        else:
            print("❌ 错误记录失败")
        
        # 获取统计信息
        stats = monitoring.get_stats()
        print(f"\n📊 监控统计:")
        print(f"   总记录数: {stats['total_runs']}")
        if stats['latest_run']:
            print(f"   最新记录: {stats['latest_run']['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 监控系统测试失败: {e}")
        return False

def test_metrics_collector():
    """测试指标收集器"""
    print("\n📊 测试指标收集器")
    print("=" * 60)
    
    try:
        metrics = MetricsCollector()
        
        # 测试会话追踪
        print("\n👥 测试会话追踪:")
        test_sessions = ["session_1", "session_2", "session_3"]
        for session in test_sessions:
            metrics.track_session(session)
        
        # 结束一个会话
        metrics.end_session("session_2")
        
        # 测试请求追踪
        print("\n🌐 测试请求追踪:")
        metrics.track_request("chat", 1.5, "success")
        metrics.track_request("chat", 2.1, "success")
        metrics.track_request("health", 0.1, "success")
        metrics.track_request("chat", 3.0, "error")
        
        # 测试LLM使用量追踪
        print("\n🔢 测试Token追踪:")
        metrics.track_llm_usage("glm-4.5", 150)
        metrics.track_llm_usage("glm-4.5", 200)
        metrics.track_llm_usage("simulation", 100)
        
        # 测试错误追踪
        print("\n❌ 测试错误追踪:")
        metrics.track_error("chat", "timeout")
        metrics.track_error("chat", "api_error")
        
        # 获取指标
        current_metrics = metrics.get_metrics()
        print(f"\n📈 当前指标:")
        print(f"   运行时间: {current_metrics['uptime_seconds']:.1f}秒")
        print(f"   总请求数: {current_metrics['total_requests']}")
        print(f"   总错误数: {current_metrics['total_errors']}")
        print(f"   活跃会话: {current_metrics['active_sessions']}")
        print(f"   平均响应时间: {current_metrics['average_response_time']:.2f}秒")
        print(f"   Token使用情况: {current_metrics['llm_token_usage']}")
        
        # 测试重置
        print(f"\n🔄 测试指标重置:")
        metrics.reset_metrics()
        reset_metrics = metrics.get_metrics()
        print(f"   重置后请求数: {reset_metrics['total_requests']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 指标收集器测试失败: {e}")
        return False

def test_config_management():
    """测试配置管理"""
    print("\n⚙️ 测试配置管理")
    print("=" * 60)
    
    try:
        config = CloudConfig()
        
        # 测试配置获取
        print("\n📋 LLM配置:")
        llm_config = config.get_llm_config()
        for key, value in llm_config.items():
            if key == "api_key" and value:
                print(f"   {key}: {value[:10]}...{value[-6:]}")
            else:
                print(f"   {key}: {value}")
        
        print(f"\n📝 系统提示词:")
        system_prompt = config.get_system_prompt()
        print(f"   {system_prompt[:80]}...")
        
        # 测试配置验证
        print(f"\n✅ 配置验证:")
        validation_issues = config.validate_config()
        if validation_issues:
            print("   发现问题:")
            for issue in validation_issues:
                print(f"   ❌ {issue}")
        else:
            print("   ✅ 配置验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        return False

def test_deployment_features():
    """测试部署特性"""
    print("\n🚀 测试部署特性")
    print("=" * 60)
    
    # 测试环境变量检查
    print("🔍 环境变量检查:")
    required_vars = ["ZHIPUAI_API_KEY"]
    optional_vars = ["MODEL_NAME", "TEMPERATURE", "TIMEOUT", "SYSTEM_PROMPT"]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: 已设置")
        else:
            print(f"   ⚠️ {var}: 未设置 (必需)")
    
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: {value}")
        else:
            print(f"   ➖ {var}: 使用默认值")
    
    # 测试健康检查
    print(f"\n💗 健康检查模拟:")
    health_check = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "chatbot_ready": True,
        "api_key_configured": bool(os.getenv("ZHIPUAI_API_KEY"))
    }
    print(f"   {json.dumps(health_check, indent=2, ensure_ascii=False)}")
    
    # 测试部署策略对比
    print(f"\n📋 部署策略对比:")
    strategies = {
        "本地Docker": {"复杂度": "⭐⭐", "成本": "低", "扩展性": "中"},
        "LangGraph Cloud": {"复杂度": "⭐", "成本": "中", "扩展性": "高"},
        "混合部署": {"复杂度": "⭐⭐⭐", "成本": "中", "扩展性": "高"},
        "Kubernetes": {"复杂度": "⭐⭐⭐⭐⭐", "成本": "高", "扩展性": "很高"}
    }
    
    for strategy, details in strategies.items():
        print(f"   🎯 {strategy}: {details}")

def main():
    """主测试函数"""
    print("🚀 第10章代码测试：部署与上线")
    print("=" * 80)
    
    # 检查API配置
    has_api_key = check_api_key()
    
    if not has_api_key:
        print("\n💡 提示：设置 ZHIPUAI_API_KEY 环境变量可体验完整功能")
    
    # 运行所有测试
    test_results = []
    
    test_results.append(("生产级聊天机器人", test_production_chatbot()))
    test_results.append(("监控系统", test_monitoring_system()))
    test_results.append(("指标收集器", test_metrics_collector()))
    test_results.append(("配置管理", test_config_management()))
    
    # 部署特性测试（不计入成功率）
    test_deployment_features()
    
    # 统计测试结果
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print("\n" + "=" * 80)
    print("📊 测试结果汇总:")
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    
    if has_api_key:
        print("🚀 生产级部署功能验证通过")
    else:
        print("🚀 模拟模式测试通过，配置API密钥可获得完整体验")
    
    print("\n💡 部署建议:")
    if success_rate >= 80:
        print("   ✅ 系统已准备好生产部署")
        print("   🔧 建议：选择合适的部署策略并配置监控")
    else:
        print("   ⚠️ 建议解决测试失败的问题后再部署")
        print("   🔧 建议：检查环境配置和依赖安装")

if __name__ == "__main__":
    main()