#!/usr/bin/env python3
"""
测试第11章：综合应用项目
验证两个主要项目：天气问答小助手、智能搜索-总结-汇报 Agent
"""
import os
import time
import json
import hashlib
from datetime import datetime, timedelta
from typing import Annotated, Optional, List, Dict
from typing_extensions import TypedDict
from collections import defaultdict

from langchain_core.messages import AnyMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langgraph.graph.message import add_messages
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import ToolNode, tools_condition

# 检查API密钥
def check_api_key():
    """检查API密钥配置"""
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-6:]})")
        return True
    else:
        print("⚠️ 未配置智谱API密钥，将使用模拟模式")
        return False

# ===== 项目1：天气问答小助手 =====

class WeatherState(TypedDict):
    messages: Annotated[List[AnyMessage], add_messages]
    user_location: Optional[str]
    query_city: Optional[str]
    weather_data: Optional[Dict]
    user_preferences: Dict[str, str]
    conversation_history: List[Dict]
    current_intent: str

@tool
def get_weather_info(city: str, country: str = "CN") -> str:
    """
    获取指定城市的天气信息
    
    Args:
        city: 城市名称，如"北京"、"上海"
        country: 国家代码，默认为"CN"
    
    Returns:
        包含天气信息的JSON字符串
    """
    try:
        # 模拟天气数据
        weather_conditions = ["晴天", "多云", "小雨", "中雨", "阴天", "雪天"]
        
        import random
        random.seed(hash(city))  # 确保同一城市返回相同结果
        
        result = {
            "city": city,
            "temperature": random.randint(15, 30),
            "feels_like": random.randint(15, 30),
            "humidity": random.randint(40, 80),
            "description": random.choice(weather_conditions),
            "wind_speed": round(random.uniform(1.0, 10.0), 1),
            "timestamp": datetime.now().isoformat()
        }
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        return f"获取{city}天气信息时出错：{str(e)}"

@tool
def get_weather_forecast(city: str, days: int = 3) -> str:
    """
    获取指定城市的天气预报
    
    Args:
        city: 城市名称
        days: 预报天数，1-5天
    
    Returns:
        天气预报信息
    """
    try:
        forecasts = []
        for i in range(min(days, 5)):
            date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            date = date.replace(day=date.day + i)
            
            forecast = {
                "date": date.isoformat()[:10],
                "temperature_high": 25 + i,
                "temperature_low": 15 + i,
                "description": "晴转多云" if i % 2 == 0 else "多云转阴"
            }
            forecasts.append(forecast)
        
        result = {
            "city": city,
            "forecasts": forecasts
        }
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        return f"获取{city}天气预报时出错：{str(e)}"

@tool
def save_user_city(city: str, user_id: str = "default") -> str:
    """
    保存用户常用城市
    
    Args:
        city: 城市名称
        user_id: 用户ID
    
    Returns:
        保存结果
    """
    try:
        print(f"💾 保存用户常用城市: {city}")
        return f"已将{city}设为您的常用城市"
    except Exception as e:
        return f"保存城市设置时出错：{str(e)}"

def simple_intent_analysis(text: str):
    """简化的意图识别"""
    text_lower = text.lower()
    
    # 提取城市名称（简化版）
    cities = ["北京", "上海", "深圳", "广州", "杭州", "成都", "武汉", "西安", "南京", "天津"]
    city = None
    for c in cities:
        if c in text:
            city = c
            break
    
    # 识别意图
    if any(word in text_lower for word in ["天气", "温度", "今天", "现在"]):
        intent = "weather_query"
    elif any(word in text_lower for word in ["预报", "明天", "未来", "几天"]):
        intent = "forecast_query"
    elif any(word in text_lower for word in ["设置", "常用", "默认", "保存"]):
        intent = "location_setting"
    else:
        intent = "general_chat"
        
    return intent, city

def create_test_weather_assistant():
    """创建测试天气助手"""
    
    # 工具列表
    tools = [get_weather_info, get_weather_forecast, save_user_city]
    tool_node = ToolNode(tools)
    
    def intent_analysis_node(state: WeatherState):
        """意图分析节点"""
        last_message = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        system_prompt = """
你是天气助手的意图分析模块。分析用户输入，识别以下意图：
1. weather_query - 查询天气
2. forecast_query - 查询天气预报  
3. location_setting - 设置常用城市
4. general_chat - 一般聊天

同时提取城市名称（如果有的话）。
请返回格式：意图|城市名称
如果没有城市名称，返回：意图|None
"""
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
                
                response = llm.invoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=last_message)
                ])
                
                try:
                    intent, city = response.content.strip().split('|')
                    city = city.strip() if city.strip() != "None" else None
                except:
                    intent, city = "general_chat", None
                    
            except Exception as e:
                print(f"⚠️ 意图分析失败: {e}")
                # 简化的意图识别
                intent, city = simple_intent_analysis(last_message)
        else:
            # 模拟模式的简化意图识别
            intent, city = simple_intent_analysis(last_message)
        
        return {
            "current_intent": intent.strip(),
            "query_city": city
        }
    
    def weather_agent_node(state: WeatherState):
        """天气智能体节点"""
        intent = state.get("current_intent", "general_chat")
        city = state.get("query_city")
        user_location = state.get("user_location")
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        # 构建系统提示
        system_prompt = f"""
你是一个专业的天气助手，当前用户意图：{intent}

你的能力：
- 查询实时天气信息 (使用get_weather_info工具)
- 提供天气预报 (使用get_weather_forecast工具)
- 保存用户常用城市 (使用save_user_city工具)
- 给出天气相关建议

用户信息：
- 常用城市：{user_location or "未设置"}
- 查询城市：{city or "未指定"}

请根据用户需求选择合适的工具，并提供友好的回复。
如果用户没有指定城市且没有常用城市，请询问城市名称。
"""
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key, timeout=35)
                llm_with_tools = llm.bind_tools(tools)
                
                messages = [SystemMessage(content=system_prompt)] + state["messages"]
                response = llm_with_tools.invoke(messages)
                
                return {"messages": [response]}
                
            except Exception as e:
                print(f"⚠️ 天气智能体调用失败: {e}")
                # 降级到简单回复
                return {"messages": [AIMessage(content="抱歉，天气服务暂时不可用，请稍后重试。")]}
        else:
            # 模拟模式
            if intent == "weather_query" and city:
                mock_response = f"根据最新数据，{city}当前天气：晴天，气温25°C，适合出行！"
            elif intent == "forecast_query" and city:
                mock_response = f"{city}未来3天天气预报：明天晴，后天多云，大后天小雨。"
            elif intent == "location_setting" and city:
                mock_response = f"好的，我已将{city}设为您的常用城市。"
            else:
                mock_response = "请告诉我您想查询哪个城市的天气？"
            
            return {"messages": [AIMessage(content=mock_response)]}
    
    # 路由函数
    def weather_router(state: WeatherState) -> str:
        """天气路由"""
        intent = state.get("current_intent", "general_chat")
        city = state.get("query_city")
        user_location = state.get("user_location")
        
        if intent in ["weather_query", "forecast_query"]:
            if city or user_location:
                return "weather_agent"
            else:
                return "ask_location"
        elif intent == "location_setting":
            return "weather_agent"
        else:
            return "general_chat"
    
    def ask_location_node(state: WeatherState):
        """询问位置节点"""
        return {
            "messages": [AIMessage(content="请告诉我您想查询哪个城市的天气？")]
        }
    
    def general_chat_node(state: WeatherState):
        """一般聊天节点"""
        return {"messages": [AIMessage(content="您好！我是天气助手，可以为您查询各个城市的天气信息。请问您需要查询哪里的天气？")]}
    
    # 构建图
    graph = StateGraph(WeatherState)
    
    # 添加节点
    graph.add_node("intent_analysis", intent_analysis_node)
    graph.add_node("weather_agent", weather_agent_node)
    graph.add_node("tools", tool_node)
    graph.add_node("ask_location", ask_location_node)
    graph.add_node("general_chat", general_chat_node)
    
    # 设置流程
    graph.add_edge(START, "intent_analysis")
    
    # 条件路由
    graph.add_conditional_edges(
        "intent_analysis",
        weather_router,
        {
            "weather_agent": "weather_agent",
            "ask_location": "ask_location",
            "general_chat": "general_chat"
        }
    )
    
    # 工具调用路由
    graph.add_conditional_edges(
        "weather_agent",
        tools_condition,
        {
            "tools": "tools",
            "__end__": END
        }
    )
    
    # 工具执行后直接结束（简化版）
    graph.add_edge("tools", END)
    graph.add_edge("ask_location", END)
    graph.add_edge("general_chat", END)
    
    return graph.compile()

# ===== 项目2：智能搜索-总结-汇报 Agent =====

class ResearchState(TypedDict):
    messages: Annotated[List[AnyMessage], add_messages]
    original_query: str
    optimized_queries: List[str]
    search_results: List[Dict]
    filtered_results: List[Dict]
    summary_content: str
    final_report: str
    research_depth: int
    sources: List[Dict]
    confidence_score: float

@tool
def web_search(query: str, num_results: int = 10) -> str:
    """
    网络搜索工具
    
    Args:
        query: 搜索查询
        num_results: 返回结果数量
    
    Returns:
        搜索结果JSON字符串
    """
    try:
        # 模拟搜索结果
        results = []
        for i in range(num_results):
            result = {
                "title": f"关于{query}的搜索结果 {i+1}",
                "snippet": f"这是关于{query}的详细信息，包含了相关的背景知识和最新发展动态...",
                "url": f"https://example.com/search/{hashlib.md5((query + str(i)).encode()).hexdigest()[:8]}",
                "timestamp": datetime.now().isoformat(),
                "relevance_score": 0.9 - (i * 0.1)
            }
            results.append(result)
        
        return json.dumps(results, ensure_ascii=False)
        
    except Exception as e:
        return f"搜索失败: {str(e)}"

@tool
def academic_search(query: str, num_results: int = 5) -> str:
    """
    学术搜索工具
    
    Args:
        query: 学术查询
        num_results: 返回结果数量
    
    Returns:
        学术搜索结果
    """
    try:
        results = []
        for i in range(num_results):
            result = {
                "title": f"学术论文：{query}的研究进展",
                "authors": ["张三", "李四", "王五"],
                "abstract": f"本文深入研究了{query}的相关理论和实践应用，提出了新的观点和方法...",
                "url": f"https://academic.example.com/paper/{hashlib.md5((query + str(i)).encode()).hexdigest()[:8]}",
                "citation_count": 42 - i * 5,
                "year": 2023 - i,
                "relevance_score": 0.95 - (i * 0.05)
            }
            results.append(result)
        
        return json.dumps(results, ensure_ascii=False)
        
    except Exception as e:
        return f"学术搜索失败: {str(e)}"

@tool
def news_search(query: str, days: int = 7) -> str:
    """
    新闻搜索工具
    
    Args:
        query: 新闻查询
        days: 搜索最近几天的新闻
    
    Returns:
        新闻搜索结果
    """
    try:
        results = []
        for i in range(5):  # 返回5条新闻
            result = {
                "title": f"最新报道：{query}的重要进展",
                "content": f"据最新消息，{query}领域出现了重要突破，这将对相关行业产生深远影响...",
                "source": f"科技日报{i+1}",
                "published_date": (datetime.now() - timedelta(days=i)).isoformat(),
                "url": f"https://news.example.com/{hashlib.md5((query + str(i)).encode()).hexdigest()[:8]}",
                "relevance_score": 0.8 - (i * 0.1)
            }
            results.append(result)
        
        return json.dumps(results, ensure_ascii=False)
        
    except Exception as e:
        return f"新闻搜索失败: {str(e)}"

def deduplicate_results(results: List[Dict]) -> List[Dict]:
    """去重搜索结果"""
    seen_urls = set()
    seen_titles = set()
    filtered = []
    
    for result in results:
        # 简化的去重逻辑
        url = result.get("url", "")
        title = result.get("title", "")
        
        if url not in seen_urls and title not in seen_titles:
            seen_urls.add(url)
            seen_titles.add(title)
            filtered.append(result)
    
    return filtered

def calculate_confidence_score(state: ResearchState) -> float:
    """计算置信度分数"""
    search_results = state.get("search_results", [])
    filtered_results = state.get("filtered_results", [])
    
    # 简化的置信度计算
    if not search_results:
        return 0.0
    
    # 基于结果数量、来源多样性等因素计算
    result_count = len(filtered_results)
    source_types = len(set(
        'academic' if 'academic' in r.get('url', '') else
        'news' if 'news' in r.get('url', '') else 'web'
        for r in search_results
    ))
    
    confidence = min(1.0, (source_types * 0.4 + min(result_count / 15, 1.0) * 0.6))
    return round(confidence, 2)

def create_test_research_assistant():
    """创建测试研究助手"""
    
    # 工具列表
    tools = [web_search, academic_search, news_search]
    tool_node = ToolNode(tools)
    
    def query_optimizer_node(state: ResearchState):
        """查询优化节点"""
        original_query = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        system_prompt = """
你是查询优化专家。将用户的原始查询转换为多个优化的搜索查询，以获得更全面的信息。

优化策略：
1. 分解复合查询为多个简单查询
2. 添加相关的同义词和术语
3. 考虑不同的角度和方面
4. 生成中文和英文查询

请返回3-5个优化后的查询，每行一个。
"""
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.4, api_key=api_key, timeout=35)
                
                response = llm.invoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=f"原始查询：{original_query}")
                ])
                
                optimized_queries = [q.strip() for q in response.content.strip().split('\n') if q.strip()]
                
            except Exception as e:
                print(f"⚠️ 查询优化失败: {e}")
                # 简化的查询优化
                optimized_queries = [
                    original_query,
                    f"{original_query} 技术",
                    f"{original_query} 应用",
                    f"{original_query} 发展趋势"
                ]
        else:
            # 模拟查询优化
            optimized_queries = [
                original_query,
                f"{original_query} 技术发展",
                f"{original_query} 实际应用",
                f"{original_query} 未来趋势"
            ]
        
        return {
            "original_query": original_query,
            "optimized_queries": optimized_queries
        }
    
    def result_processor_node(state: ResearchState):
        """结果处理节点"""
        # 模拟搜索结果处理
        queries = state.get("optimized_queries", [])
        search_results = []
        
        # 为每个查询生成模拟搜索结果
        for query in queries:
            web_result = json.loads(web_search.invoke({"query": query, "num_results": 3}))
            academic_result = json.loads(academic_search.invoke({"query": query, "num_results": 2}))
            news_result = json.loads(news_search.invoke({"query": query, "days": 7}))
            
            search_results.extend(web_result)
            search_results.extend(academic_result)
            search_results.extend(news_result)
        
        # 去重和筛选
        filtered_results = deduplicate_results(search_results)
        
        return {
            "search_results": search_results,
            "filtered_results": filtered_results
        }
    
    def content_summarizer_node(state: ResearchState):
        """内容总结节点"""
        filtered_results = state.get("filtered_results", [])
        original_query = state.get("original_query", "")
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        system_prompt = f"""
你是内容总结专家。基于搜索结果为用户查询"{original_query}"生成综合总结。

总结要求：
1. 客观准确，基于事实
2. 结构清晰，逻辑连贯
3. 突出重点信息
4. 注明信息来源
5. 评估信息可信度

请生成一个结构化的总结报告。
"""
        
        # 构造搜索结果摘要
        results_summary = "\n".join([
            f"来源{i+1}: {result.get('title', 'unknown')} - {str(result.get('snippet', result.get('abstract', result.get('content', ''))))[:200]}..."
            for i, result in enumerate(filtered_results[:10])  # 限制处理的结果数量
        ])
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=40)
                
                response = llm.invoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=f"搜索结果：\n{results_summary}")
                ])
                
                summary_content = response.content
                
            except Exception as e:
                print(f"⚠️ 内容总结失败: {e}")
                summary_content = f"关于{original_query}的研究总结：基于{len(filtered_results)}个信息源的综合分析。"
        else:
            # 模拟总结
            summary_content = f"""
关于"{original_query}"的研究总结：

## 主要发现
基于{len(filtered_results)}个信息源的分析，{original_query}是一个重要且活跃的研究领域。

## 核心观点
1. 技术发展迅速，应用前景广阔
2. 学术界和产业界都高度关注
3. 存在一些挑战和机遇

## 信息来源
- 网络搜索：{len([r for r in filtered_results if 'example.com' in r.get('url', '')])}个结果
- 学术论文：{len([r for r in filtered_results if 'academic' in r.get('url', '')])}篇
- 新闻报道：{len([r for r in filtered_results if 'news' in r.get('url', '')])}条
"""
        
        return {"summary_content": summary_content}
    
    def report_generator_node(state: ResearchState):
        """报告生成节点"""
        summary_content = state.get("summary_content", "")
        original_query = state.get("original_query", "")
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.4, api_key=api_key, timeout=40)
                
                system_prompt = """
你是报告生成专家。将总结内容转换为专业的研究报告格式。

报告结构：
1. 执行摘要
2. 研究背景
3. 主要发现
4. 详细分析
5. 结论和建议
6. 参考来源

请生成一份完整的研究报告。
"""
                
                response = llm.invoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=f"""
研究主题：{original_query}
总结内容：{summary_content}

请生成正式的研究报告。
""")
                ])
                
                final_report = response.content
                
            except Exception as e:
                print(f"⚠️ 报告生成失败: {e}")
                final_report = f"# {original_query} 研究报告\n\n{summary_content}"
        else:
            # 模拟报告生成
            final_report = f"""
# {original_query} 研究报告

## 执行摘要
本报告深入分析了{original_query}的相关情况，基于多个信息源进行综合研究。

## 研究背景
{original_query}是当前备受关注的重要主题，具有重要的理论和实践价值。

## 主要发现
{summary_content}

## 结论和建议
1. 继续关注该领域的发展动态
2. 深入研究相关技术和应用
3. 加强产学研合作

## 参考来源
本报告基于{len(state.get('filtered_results', []))}个信息源的综合分析。

---
报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 计算置信度分数
        confidence_score = calculate_confidence_score(state)
        
        return {
            "final_report": final_report,
            "confidence_score": confidence_score
        }
    
    # 构建图
    graph = StateGraph(ResearchState)
    
    # 添加节点
    graph.add_node("query_optimizer", query_optimizer_node)
    graph.add_node("result_processor", result_processor_node)
    graph.add_node("content_summarizer", content_summarizer_node)
    graph.add_node("report_generator", report_generator_node)
    
    # 设置流程
    graph.add_edge(START, "query_optimizer")
    graph.add_edge("query_optimizer", "result_processor")
    graph.add_edge("result_processor", "content_summarizer")
    graph.add_edge("content_summarizer", "report_generator")
    graph.add_edge("report_generator", END)
    
    return graph.compile()

# ===== 测试函数 =====

def test_weather_assistant():
    """测试天气助手"""
    print("\n🌤️ 测试天气问答小助手")
    print("=" * 60)
    
    try:
        weather_app = create_test_weather_assistant()
        
        test_questions = [
            "北京今天天气怎么样？",
            "上海未来三天的天气预报",
            "帮我设置深圳为常用城市",
            "你好，今天过得怎么样？"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n--- 测试 {i} ---")
            print(f"👤 用户: {question}")
            
            try:
                # 初始状态
                conversation_state = {
                    "messages": [HumanMessage(content=question)],
                    "user_location": None,
                    "query_city": None,
                    "weather_data": None,
                    "user_preferences": {},
                    "conversation_history": [],
                    "current_intent": ""
                }
                
                # 调用天气助手
                result = weather_app.invoke(conversation_state)
                
                # 获取AI回复
                ai_response = result["messages"][-1].content
                print(f"🤖 助手: {ai_response[:200]}...")
                
                # 显示状态信息
                intent = result.get("current_intent", "unknown")
                city = result.get("query_city", "无")
                print(f"   📊 意图: {intent}, 城市: {city}")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 天气助手测试失败: {e}")
        return False

def test_research_assistant():
    """测试研究助手"""
    print("\n🔍 测试智能搜索-总结-汇报 Agent")
    print("=" * 60)
    
    try:
        research_app = create_test_research_assistant()
        
        test_queries = [
            "人工智能在教育领域的应用",
            "区块链技术的发展趋势"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- 研究测试 {i} ---")
            print(f"📋 研究主题: {query}")
            print("🔄 开始研究，请稍候...")
            
            try:
                # 初始状态
                initial_state = {
                    "messages": [HumanMessage(content=query)],
                    "original_query": "",
                    "optimized_queries": [],
                    "search_results": [],
                    "filtered_results": [],
                    "summary_content": "",
                    "final_report": "",
                    "research_depth": 1,
                    "sources": [],
                    "confidence_score": 0.0
                }
                
                # 执行研究
                result = research_app.invoke(initial_state)
                
                # 显示结果
                confidence = result.get('confidence_score', 0)
                print(f"\n📊 研究报告 (置信度: {confidence:.2f})")
                print("-" * 40)
                
                final_report = result.get('final_report', '报告生成失败')
                print(final_report[:500] + "..." if len(final_report) > 500 else final_report)
                
                # 显示统计信息
                print(f"\n📈 研究统计:")
                print(f"   - 优化查询: {len(result.get('optimized_queries', []))} 个")
                print(f"   - 搜索结果: {len(result.get('search_results', []))} 个")
                print(f"   - 筛选结果: {len(result.get('filtered_results', []))} 个")
                
            except Exception as e:
                print(f"❌ 研究测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 研究助手测试失败: {e}")
        return False

def test_tools():
    """测试工具功能"""
    print("\n🧪 测试工具功能")
    print("=" * 60)
    
    try:
        # 测试天气工具
        print("📊 测试天气工具:")
        weather_result = get_weather_info.invoke({"city": "北京"})
        print(f"   天气查询: {weather_result[:100]}...")
        
        forecast_result = get_weather_forecast.invoke({"city": "上海", "days": 3})
        print(f"   天气预报: {json.loads(forecast_result)['city']} - {len(json.loads(forecast_result)['forecasts'])}天预报")
        
        save_result = save_user_city.invoke({"city": "深圳", "user_id": "test_user"})
        print(f"   城市保存: {save_result}")
        
        # 测试搜索工具
        print(f"\n🔍 测试搜索工具:")
        web_result = web_search.invoke({"query": "人工智能", "num_results": 3})
        web_data = json.loads(web_result)
        print(f"   网络搜索: 找到 {len(web_data)} 个结果")
        
        academic_result = academic_search.invoke({"query": "机器学习", "num_results": 2})
        academic_data = json.loads(academic_result)
        print(f"   学术搜索: 找到 {len(academic_data)} 篇论文")
        
        news_result = news_search.invoke({"query": "科技发展", "days": 7})
        news_data = json.loads(news_result)
        print(f"   新闻搜索: 找到 {len(news_data)} 条新闻")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 第11章代码测试：综合应用项目")
    print("=" * 80)
    
    # 检查API配置
    has_api_key = check_api_key()
    
    if not has_api_key:
        print("\n💡 提示：设置 ZHIPUAI_API_KEY 环境变量可体验完整功能")
    
    # 运行所有测试
    test_results = []
    
    test_results.append(("工具功能", test_tools()))
    test_results.append(("天气问答小助手", test_weather_assistant()))
    test_results.append(("智能搜索-总结-汇报 Agent", test_research_assistant()))
    
    # 统计测试结果
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print("\n" + "=" * 80)
    print("📊 测试结果汇总:")
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体成功率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    
    if has_api_key:
        print("🚀 综合项目功能验证通过")
    else:
        print("🚀 模拟模式测试通过，配置API密钥可获得完整体验")
    
    print("\n💡 项目特色:")
    print("   ✅ 项目1: 天气助手 - 状态管理、工具调用、意图识别")
    print("   ✅ 项目2: 研究助手 - 工具链协作、内容生成、报告输出")
    print("   ✅ 完整的错误处理和降级策略")
    print("   ✅ 智谱GLM-4.5完美集成")
    
    print(f"\n🎯 学习建议:")
    if success_rate >= 80:
        print("   ✅ 综合项目验证通过，可以进入实际应用开发")
        print("   🔧 建议：深入理解每个项目的架构设计")
    else:
        print("   ⚠️ 建议解决测试失败的问题后再进行下一步")
        print("   🔧 建议：检查环境配置和依赖安装")

if __name__ == "__main__":
    main()