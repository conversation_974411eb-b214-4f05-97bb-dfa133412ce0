#!/usr/bin/env python3
"""
GLM-4.5 + Tavily搜索引擎简化示例
适合书中展示的清晰代码
"""

import os
from tavily import TavilyClient
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent


def tavily_search(query: str) -> str:
    """使用Tavily搜索引擎获取实时信息"""
    # 初始化Tavily客户端
    tavily_client = TavilyClient(api_key="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")
    
    # 执行搜索
    response = tavily_client.search(
        query=query,
        max_results=3,
        include_answer=True
    )
    
    # 返回搜索结果摘要
    if response.get("answer"):
        return f"搜索结果: {response['answer']}"
    else:
        return "抱歉，没有找到相关信息。"


def main():
    # 配置智谱AI GLM-4.5模型
    model = ChatOpenAI(
        model="glm-4.5",
        api_key="4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW",
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0.3
    )
    
    # 创建智能体，配置搜索工具
    agent = create_react_agent(
        model=model,
        tools=[tavily_search],
        prompt="你是一个智能助手，可以搜索最新信息来回答问题。"
    )
    
    # 运行智能体
    result = agent.invoke({
        "messages": [{"role": "user", "content": "北京今天的天气怎么样？"}]
    })
    
    # 输出最终回复
    final_answer = result["messages"][-1].content
    print("🤖 智能体回复:")
    print(final_answer)


if __name__ == "__main__":
    main()