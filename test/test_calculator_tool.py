#!/usr/bin/env python3
"""
数学计算器工具的测试用例
测试 math_calculator 工具的各种功能和边界情况
"""

import pytest
import math
from langchain_core.tools import tool


# 复制计算器工具的实现（用于测试）
@tool
def math_calculator(expression: str) -> str:
    """
    数学计算器
    
    Args:
        expression: 数学表达式，支持基本运算和数学函数
                   例如：'2 + 3 * 4', 'math.sqrt(16)', 'math.sin(math.pi/2)'
    
    Returns:
        计算结果
    """
    try:
        # 安全的数学计算环境
        allowed_names = {
            k: v for k, v in math.__dict__.items() if not k.startswith("__")
        }
        # 添加内置函数
        allowed_names.update({"abs": abs, "round": round})
        # 添加 math 模块本身，以支持 math.xxx 的调用方式
        allowed_names["math"] = math
        
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return f"计算结果：{result}"
    except Exception as e:
        return f"计算错误：{str(e)}"


class TestMathCalculator:
    """数学计算器工具测试类"""
    
    def test_basic_arithmetic(self):
        """测试基本算术运算"""
        # 加法
        result = math_calculator("2 + 3")
        assert result == "计算结果：5"
        
        # 减法
        result = math_calculator("10 - 3")
        assert result == "计算结果：7"
        
        # 乘法
        result = math_calculator("4 * 5")
        assert result == "计算结果：20"
        
        # 除法
        result = math_calculator("15 / 3")
        assert result == "计算结果：5.0"
        
        # 幂运算
        result = math_calculator("2 ** 3")
        assert result == "计算结果：8"
        
        # 取模
        result = math_calculator("17 % 5")
        assert result == "计算结果：2"
    
    def test_complex_expressions(self):
        """测试复杂表达式"""
        # 运算优先级
        result = math_calculator("2 + 3 * 4")
        assert result == "计算结果：14"
        
        # 括号
        result = math_calculator("(2 + 3) * 4")
        assert result == "计算结果：20"
        
        # 嵌套括号
        result = math_calculator("((2 + 3) * 4) / 2")
        assert result == "计算结果：10.0"
        
        # 多重运算
        result = math_calculator("2 ** 3 + 4 * 5 - 6 / 2")
        assert result == "计算结果：25.0"
    
    def test_math_functions(self):
        """测试数学函数"""
        # 平方根
        result = math_calculator("math.sqrt(16)")
        assert result == "计算结果：4.0"
        
        # 三角函数
        result = math_calculator("math.sin(math.pi / 2)")
        assert result == "计算结果：1.0"
        
        result = math_calculator("math.cos(0)")
        assert result == "计算结果：1.0"
        
        # 对数函数
        result = math_calculator("math.log(math.e)")
        assert result == "计算结果：1.0"
        
        result = math_calculator("math.log10(100)")
        assert result == "计算结果：2.0"
        
        # 指数函数
        result = math_calculator("math.exp(1)")
        assert "计算结果：2.718" in result  # 近似值
        
        # 阶乘
        result = math_calculator("math.factorial(5)")
        assert result == "计算结果：120"
        
        # 向上取整
        result = math_calculator("math.ceil(4.3)")
        assert result == "计算结果：5"
        
        # 向下取整
        result = math_calculator("math.floor(4.9)")
        assert result == "计算结果：4"
    
    def test_builtin_functions(self):
        """测试内置函数"""
        # 绝对值
        result = math_calculator("abs(-5)")
        assert result == "计算结果：5"
        
        result = math_calculator("abs(5)")
        assert result == "计算结果：5"
        
        # 四舍五入
        result = math_calculator("round(3.7)")
        assert result == "计算结果：4"
        
        result = math_calculator("round(3.2)")
        assert result == "计算结果：3"
        
        # 指定小数位数的四舍五入
        result = math_calculator("round(3.14159, 2)")
        assert result == "计算结果：3.14"
    
    def test_constants(self):
        """测试数学常数"""
        # 圆周率
        result = math_calculator("math.pi")
        assert "计算结果：3.141" in result
        
        # 自然常数
        result = math_calculator("math.e")
        assert "计算结果：2.718" in result
        
        # 使用常数进行计算
        result = math_calculator("2 * math.pi")
        assert "计算结果：6.283" in result
    
    def test_floating_point_operations(self):
        """测试浮点数运算"""
        # 浮点数加法
        result = math_calculator("0.1 + 0.2")
        assert "计算结果：0.30000000000000004" in result
        
        # 浮点数除法
        result = math_calculator("7 / 3")
        assert "计算结果：2.333" in result
        
        # 大数运算
        result = math_calculator("999999 * 999999")
        assert result == "计算结果：999998000001"
    
    def test_error_handling(self):
        """测试错误处理"""
        # 除零错误
        result = math_calculator("1 / 0")
        assert "计算错误" in result
        assert "division by zero" in result
        
        # 语法错误
        result = math_calculator("2 +")
        assert "计算错误" in result
        
        # 无效函数调用
        result = math_calculator("math.sqrt(-1)")
        assert "计算错误" in result
        assert "math domain error" in result
        
        # 未定义的变量
        result = math_calculator("x + 1")
        assert "计算错误" in result
        assert "name 'x' is not defined" in result
        
        # 空字符串
        result = math_calculator("")
        assert "计算错误" in result
    
    def test_security_restrictions(self):
        """测试安全限制"""
        # 尝试访问被禁止的内置函数
        result = math_calculator("__import__('os')")
        assert "计算错误" in result
        
        # 尝试执行系统命令
        result = math_calculator("exec('print(1)')")
        assert "计算错误" in result
        
        # 尝试访问全局变量
        result = math_calculator("globals()")
        assert "计算错误" in result
        
        # 尝试访问局部变量
        result = math_calculator("locals()")
        assert "计算错误" in result
        
        # 尝试使用eval
        result = math_calculator("eval('1+1')")
        assert "计算错误" in result
        
        # 尝试导入模块
        result = math_calculator("import os")
        assert "计算错误" in result
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 非常大的数
        result = math_calculator("10 ** 100")
        assert "计算结果：" in result
        
        # 非常小的数
        result = math_calculator("1e-10")
        assert "计算结果：1e-10" in result
        
        # 负数运算
        result = math_calculator("-5 * -3")
        assert result == "计算结果：15"
        
        # 零运算
        result = math_calculator("0 * 1000")
        assert result == "计算结果：0"
        
        # 复杂嵌套
        result = math_calculator("math.sin(math.cos(math.tan(0)))")
        assert "计算结果：" in result
    
    def test_whitespace_handling(self):
        """测试空白字符处理"""
        # 多余空格
        result = math_calculator("  2   +   3  ")
        assert result == "计算结果：5"
        
        # 制表符
        result = math_calculator("2\t+\t3")
        assert result == "计算结果：5"
        
        # 换行符（应该失败）
        result = math_calculator("2 +\n3")
        assert "计算错误" in result
    
    def test_string_inputs(self):
        """测试字符串输入"""
        # 数字字符串
        result = math_calculator("'2' + '3'")
        assert result == "计算结果：23"  # 字符串连接
        
        # 无效字符串表达式
        result = math_calculator("'hello' + 1")
        assert "计算错误" in result
    
    def test_tool_metadata(self):
        """测试工具元数据"""
        # 检查工具名称
        assert math_calculator.name == "math_calculator"
        
        # 检查工具描述
        assert "数学计算器" in math_calculator.description
        
        # 检查参数定义
        assert hasattr(math_calculator, 'args_schema')


def test_calculator_integration():
    """集成测试：模拟实际使用场景"""
    test_cases = [
        ("计算圆的面积，半径为5", "math.pi * 5 ** 2"),
        ("计算直角三角形斜边，两边长为3和4", "math.sqrt(3**2 + 4**2)"),
        ("计算复利，本金1000，年利率5%，3年", "1000 * (1 + 0.05) ** 3"),
        ("计算标准正态分布在x=1处的概率密度", "1/math.sqrt(2*math.pi) * math.exp(-0.5)"),
    ]
    
    for description, expression in test_cases:
        result = math_calculator(expression)
        print(f"\n{description}")
        print(f"表达式: {expression}")
        print(f"结果: {result}")
        assert "计算结果：" in result
        assert "计算错误" not in result


if __name__ == "__main__":
    # 运行基本测试
    pytest.main([__file__, "-v"])
    
    # 运行集成测试
    print("\n" + "="*50)
    print("运行集成测试")
    print("="*50)
    test_calculator_integration()