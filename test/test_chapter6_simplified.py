#!/usr/bin/env python3
"""
第6章 人机协作功能简化测试
专注于核心逻辑验证，避免 interrupt 机制的环境依赖问题
"""

import os
from typing import Annotated, Literal
from typing_extensions import TypedDict
from datetime import datetime

from langgraph.types import Command
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

# 设置环境变量（如果没有的话使用模拟模式）
if not os.getenv("ZHIPUAI_API_KEY"):
    print("⚠️ 未设置 ZHIPUAI_API_KEY，将使用模拟模式")

class ReviewState(TypedDict):
    messages: Annotated[list, add_messages]
    pending_action: str
    confidence_score: float
    requires_approval: bool
    human_feedback: str
    risk_level: str

def analyze_request(user_request: str) -> tuple[str, str, float]:
    """分析用户请求，返回 (操作, 风险等级, 置信度)"""
    try:
        from langchain_community.chat_models import ChatZhipuAI
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if api_key:
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.1,
                api_key=api_key
            )
            
            analysis_prompt = f"""
            分析以下用户请求，评估其风险等级和你的置信度：
            用户请求：{user_request}
            
            请返回：
            1. 建议的操作
            2. 风险等级（低/中/高）
            3. 置信度（0-1之间的数字）
            
            格式：操作|风险等级|置信度
            """
            
            response = llm.invoke([HumanMessage(content=analysis_prompt)])
            analysis = response.content.strip()
        else:
            raise Exception("No API key")
            
    except Exception as e:
        print(f"⚠️ API调用失败，使用模拟分析: {e}")
        # 基于关键词的模拟分析
        if "删除" in user_request:
            analysis = "删除文件操作|高|0.9"
        elif "发送" in user_request:
            analysis = "发送通知|高|0.8"
        elif "复杂" in user_request or "不确定" in user_request:
            analysis = "复杂咨询|中|0.4"
        elif "天气" in user_request:
            analysis = "天气查询|低|0.95"
        else:
            analysis = "一般回复|中|0.7"
    
    try:
        parts = analysis.split('|')
        action = parts[0].strip()
        risk_level = parts[1].strip()
        confidence = float(parts[2].strip())
    except:
        # 解析失败时的默认值
        action = "general_response"
        risk_level = "中"
        confidence = 0.5
    
    return action, risk_level, confidence

def create_simplified_human_loop_bot():
    """创建简化的人机协作机器人（不使用 interrupt）"""
    
    def analysis_node(state: ReviewState):
        """分析节点"""
        last_message = state["messages"][-1].content
        action, risk_level, confidence = analyze_request(last_message)
        
        # 判断是否需要人工审核
        needs_approval = (
            confidence < 0.7 or  # 置信度低
            risk_level == "高" or  # 高风险
            "删除" in last_message or 
            "发送" in last_message  # 敏感操作
        )
        
        print(f"🔍 AI分析结果:")
        print(f"   操作: {action}")
        print(f"   风险等级: {risk_level}")
        print(f"   置信度: {confidence}")
        print(f"   需要审核: {needs_approval}")
        
        return {
            "pending_action": action,
            "confidence_score": confidence,
            "requires_approval": needs_approval,
            "risk_level": risk_level
        }
    
    def decision_router(state: ReviewState) -> Literal["human_review", "auto_execute"]:
        """决策路由器"""
        if state.get("requires_approval", False):
            return "human_review"
        else:
            return "auto_execute"
    
    def human_review_node(state: ReviewState):
        """人工审核节点（模拟）"""
        print("👤 进入人工审核流程")
        
        # 在实际应用中，这里会暂停等待人工输入
        # 为了测试，我们模拟不同的人工决策
        user_request = state["messages"][-1].content
        
        if "删除" in user_request:
            # 模拟拒绝危险操作
            human_decision = "rejected"
            print("👤 人工决策: 拒绝删除操作")
        elif "发送" in user_request:
            # 模拟需要修改
            human_decision = "请改为发送给管理员确认"
            print("👤 人工决策: 修改发送方式")
        else:
            # 模拟批准
            human_decision = "approved"
            print("👤 人工决策: 批准执行")
        
        return {"human_feedback": human_decision}
    
    def auto_execute_node(state: ReviewState):
        """自动执行节点"""
        print("🤖 自动执行")
        user_content = state["messages"][-1].content
        
        if "天气" in user_content:
            response = AIMessage(content="今天天气晴朗，温度适宜，适合外出活动。")
        else:
            response = AIMessage(content="我已经自动处理了您的请求。")
        
        return {"messages": [response]}
    
    def execution_node(state: ReviewState):
        """最终执行节点"""
        human_feedback = state.get("human_feedback", "")
        
        print(f"🚀 最终执行，人工反馈: {human_feedback}")
        
        if human_feedback == "rejected":
            response = AIMessage(content="抱歉，该操作已被人工审核拒绝。")
        elif human_feedback == "approved":
            user_content = state["messages"][-1].content
            if "复杂" in user_content:
                response = AIMessage(content="经过人工确认，我将为您处理这个复杂问题。")
            else:
                response = AIMessage(content="经过人工审核批准，操作已执行。")
        else:
            # 自定义反馈
            response = AIMessage(content=f"根据人工反馈「{human_feedback}」，我已调整处理方式。")
        
        return {"messages": [response]}
    
    # 构建图
    graph = StateGraph(ReviewState)
    
    # 添加节点
    graph.add_node("analysis", analysis_node)
    graph.add_node("human_review", human_review_node)
    graph.add_node("auto_execute", auto_execute_node)
    graph.add_node("execution", execution_node)
    
    # 设置流程
    graph.add_edge(START, "analysis")
    
    # 条件路由
    graph.add_conditional_edges(
        "analysis",
        decision_router,
        {
            "human_review": "human_review",
            "auto_execute": "auto_execute"
        }
    )
    
    graph.add_edge("human_review", "execution")
    graph.add_edge("auto_execute", END)
    graph.add_edge("execution", END)
    
    return graph.compile()

def create_command_based_bot():
    """创建基于 Command 的机器人"""
    
    class CommandState(TypedDict):
        messages: Annotated[list, add_messages]
        requires_approval: bool
        pending_action: str
        processing_mode: str
    
    def smart_routing_node(state: CommandState) -> Command[Literal["human_review", "auto_execute"]]:
        """智能路由节点"""
        last_message = state["messages"][-1].content.lower()
        
        # 分析风险因素
        risk_keywords = ["删除", "发送", "支付", "转账", "发布"]
        has_risk = any(keyword in last_message for keyword in risk_keywords)
        
        # 分析复杂度
        complexity_indicators = ["复杂", "不确定", "可能", "也许", "不知道"]
        is_complex = any(indicator in last_message for indicator in complexity_indicators)
        
        print(f"🔍 Command路由分析:")
        print(f"   有风险关键词: {has_risk}")
        print(f"   有复杂度指标: {is_complex}")
        
        if has_risk or is_complex:
            print("🔀 Command路由到: human_review")
            return Command(
                update={
                    "requires_approval": True,
                    "pending_action": "需要人工确认",
                    "processing_mode": "manual"
                },
                goto="human_review"
            )
        else:
            print("🔀 Command路由到: auto_execute")
            return Command(
                update={
                    "requires_approval": False,
                    "pending_action": "自动处理",
                    "processing_mode": "auto"
                },
                goto="auto_execute"
            )
    
    def auto_execute_node(state: CommandState):
        """自动执行节点"""
        print("🤖 Command自动执行")
        user_content = state["messages"][-1].content
        
        if "天气" in user_content:
            response = AIMessage(content="今天天气不错，适合外出。")
        else:
            response = AIMessage(content="我已经自动处理了您的请求。")
        
        return {"messages": [response]}
    
    def human_review_node(state: CommandState):
        """人工审核节点"""
        print("👤 Command人工审核")
        return {
            "messages": [AIMessage(content="该请求已提交人工审核，请等待处理结果。")]
        }
    
    # 构建图
    graph = StateGraph(CommandState)
    
    # 添加节点
    graph.add_node("smart_routing", smart_routing_node)
    graph.add_node("human_review", human_review_node)
    graph.add_node("auto_execute", auto_execute_node)
    
    # 设置流程
    graph.add_edge(START, "smart_routing")
    graph.add_edge("human_review", END)
    graph.add_edge("auto_execute", END)
    
    return graph.compile()

def test_simplified_human_loop():
    """测试简化的人机协作流程"""
    print("\n" + "="*60)
    print("🧪 测试: 简化人机协作流程")
    print("="*60)
    
    app = create_simplified_human_loop_bot()
    
    test_cases = [
        ("你好，今天天气怎么样？", "低风险自动处理"),
        ("帮我删除所有文件", "高风险人工审核"),
        ("这个复杂的技术问题我不太确定", "低置信度人工审核"),
        ("发送邮件给所有客户", "敏感操作人工审核")
    ]
    
    results = []
    
    for request, expected in test_cases:
        print(f"\n📝 测试请求: {request}")
        print(f"🎯 预期结果: {expected}")
        
        try:
            result = app.invoke({
                "messages": [HumanMessage(content=request)],
                "pending_action": "",
                "confidence_score": 0.0,
                "requires_approval": False,
                "human_feedback": "",
                "risk_level": ""
            })
            
            final_message = result['messages'][-1].content
            print(f"✅ AI回复: {final_message}")
            
            # 验证结果
            if "天气" in request and "天气" in final_message:
                results.append(True)
                print("✅ 测试通过")
            elif "删除" in request and ("拒绝" in final_message or "审核" in final_message):
                results.append(True)
                print("✅ 测试通过")
            elif "复杂" in request and ("确认" in final_message or "审核" in final_message):
                results.append(True)
                print("✅ 测试通过")
            elif "发送" in request and ("反馈" in final_message or "审核" in final_message):
                results.append(True)
                print("✅ 测试通过")
            else:
                results.append(False)
                print("⚠️ 结果需要验证")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append(False)
    
    return all(results)

def test_command_routing():
    """测试 Command 路由机制"""
    print("\n" + "="*60)
    print("🧪 测试: Command 路由机制")
    print("="*60)
    
    app = create_command_based_bot()
    
    test_cases = [
        ("今天天气如何？", "auto_execute"),
        ("帮我删除这些文件", "human_review"),
        ("这个问题很复杂", "human_review"),
        ("查询用户信息", "auto_execute")
    ]
    
    results = []
    
    for request, expected_route in test_cases:
        print(f"\n📝 测试请求: {request}")
        print(f"🎯 预期路由: {expected_route}")
        
        try:
            result = app.invoke({
                "messages": [HumanMessage(content=request)],
                "requires_approval": False,
                "pending_action": "",
                "processing_mode": ""
            })
            
            final_message = result['messages'][-1].content
            processing_mode = result.get('processing_mode', 'unknown')
            
            print(f"✅ AI回复: {final_message}")
            print(f"📊 处理模式: {processing_mode}")
            
            # 验证路由结果
            if expected_route == "auto_execute" and ("自动" in final_message or "天气" in final_message):
                results.append(True)
                print("✅ 路由测试通过")
            elif expected_route == "human_review" and ("审核" in final_message or "等待" in final_message):
                results.append(True)
                print("✅ 路由测试通过")
            else:
                results.append(False)
                print("⚠️ 路由结果需要验证")
                
        except Exception as e:
            print(f"❌ 路由测试失败: {e}")
            results.append(False)
    
    return all(results)

def test_risk_assessment():
    """测试风险评估逻辑"""
    print("\n" + "="*60)
    print("🧪 测试: 风险评估逻辑")
    print("="*60)
    
    test_cases = [
        ("查询天气", "低", 0.95),
        ("删除文件", "高", 0.9),
        ("发送邮件", "高", 0.8),
        ("复杂问题", "中", 0.4),
        ("一般咨询", "中", 0.7)
    ]
    
    results = []
    
    for request, expected_risk, expected_confidence in test_cases:
        print(f"\n📝 分析请求: {request}")
        
        action, risk_level, confidence = analyze_request(request)
        
        print(f"🔍 分析结果:")
        print(f"   操作: {action}")
        print(f"   风险等级: {risk_level} (预期: {expected_risk})")
        print(f"   置信度: {confidence} (预期: {expected_confidence})")
        
        # 验证结果
        risk_match = risk_level == expected_risk
        confidence_match = abs(confidence - expected_confidence) < 0.1
        
        if risk_match and confidence_match:
            results.append(True)
            print("✅ 风险评估准确")
        else:
            results.append(False)
            print("⚠️ 风险评估需要调整")
    
    return all(results)

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始人机协作功能测试（简化版）")
    print("🔧 使用智谱GLM-4.5模型（模拟模式）")
    print("=" * 80)
    
    tests = [
        ("风险评估逻辑", test_risk_assessment),
        ("简化人机协作流程", test_simplified_human_loop),
        ("Command路由机制", test_command_routing),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*80)
    print("📊 测试结果总结")
    print("="*80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！人机协作核心逻辑正常工作")
        print("\n💡 说明:")
        print("   - 风险评估逻辑正确识别不同类型请求")
        print("   - 路由机制能够正确分流高风险和低风险请求")
        print("   - Command机制提供了灵活的流程控制")
        print("   - 在生产环境中，interrupt机制需要配合适当的UI界面")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)