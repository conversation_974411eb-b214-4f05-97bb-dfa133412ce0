#!/usr/bin/env python3
"""
第03章核心概念演示
展示状态、节点、边、持久化等核心功能
使用智谱GLM-4.5和Tavily搜索引擎
"""

import os
from typing import TypedDict, Annotated
from tavily import TavilyClient
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, AnyMessage
from langgraph.graph.message import add_messages
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver


# ============== 状态定义 ==============

class ChatState(TypedDict):
    """聊天状态定义"""
    messages: Annotated[list[AnyMessage], add_messages]
    search_results: str
    intent: str


# ============== 节点实现 ==============

def create_llm_node():
    """创建LLM节点 - 使用智谱GLM-4.5"""
    def llm_node(state: ChatState):
        print("🤖 调用智谱GLM-4.5模型...")
        
        llm = ChatOpenAI(
            model="glm-4.5",
            api_key="4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW",
            base_url="https://open.bigmodel.cn/api/paas/v4/",
            temperature=0.1
        )
        
        system_message = SystemMessage(content="你是一个有用的助手")
        messages = [system_message] + state["messages"]
        
        response = llm.invoke(messages)
        print(f"✅ GLM-4.5 回复: {response.content}")
        
        return {"messages": [response]}
    
    return llm_node


def create_search_node():
    """创建搜索节点 - 使用Tavily搜索引擎"""
    def search_node(state: ChatState):
        print("🔍 使用Tavily搜索引擎...")
        
        try:
            tavily_client = TavilyClient(api_key="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")
            
            last_message = state["messages"][-1].content
            query = extract_search_query(last_message)
            
            if query:
                print(f"📝 搜索查询: {query}")
                response = tavily_client.search(
                    query=query,
                    search_depth="basic",
                    max_results=3,
                    include_answer=True
                )
                
                if response.get("answer"):
                    results = f"搜索摘要: {response['answer']}"
                elif response.get("results"):
                    results_list = response["results"][:2]
                    results = "搜索结果:\n"
                    for i, result in enumerate(results_list, 1):
                        title = result.get("title", "无标题")
                        content = result.get("content", "无内容")[:200]
                        results += f"{i}. {title}: {content}...\n"
                else:
                    results = "未找到相关信息"
                
                print(f"✅ 搜索完成: {results[:100]}...")
                
                return {
                    "search_results": results,
                    "messages": [AIMessage(content=f"我找到了相关信息：\n{results}")]
                }
            
            return {"messages": [AIMessage(content="抱歉，我没有理解你的搜索需求")]}
            
        except Exception as e:
            print(f"❌ 搜索出错: {str(e)}")
            return {"messages": [AIMessage(content=f"搜索出错: {str(e)}")]}
    
    return search_node


def extract_search_query(message: str) -> str:
    """从消息中提取搜索查询"""
    search_keywords = ["搜索", "查询", "找", "天气", "新闻", "信息", "什么是", "怎么"]
    for keyword in search_keywords:
        if keyword in message:
            return message
    return ""


# ============== 路由函数 ==============

def create_routing_function():
    """创建路由函数"""
    def routing_function(state: ChatState) -> str:
        last_message = state["messages"][-1].content.lower()
        
        # 检查是否需要搜索
        search_keywords = ["搜索", "查询", "找", "天气", "新闻", "最新", "什么是", "怎么"]
        need_search = any(keyword in last_message for keyword in search_keywords)
        
        if need_search:
            print("🔀 路由决策: 需要搜索信息")
            return "search_node"
        else:
            print("🔀 路由决策: 直接对话")
            return "llm_node"
    
    return routing_function


# ============== 创建应用 ==============

def create_demo_app():
    """创建演示应用"""
    print("🏗️  构建LangGraph应用...")
    
    # 创建状态图
    graph = StateGraph(ChatState)
    
    # 添加节点
    graph.add_node("llm_node", create_llm_node())
    graph.add_node("search_node", create_search_node())
    
    # 添加条件边
    graph.add_conditional_edges(
        START,
        create_routing_function(),
        {
            "llm_node": "llm_node",
            "search_node": "search_node"
        }
    )
    
    # 添加结束边
    graph.add_edge("llm_node", END)
    graph.add_edge("search_node", END)
    
    # 使用内存持久化
    memory = MemorySaver()
    app = graph.compile(checkpointer=memory)
    
    print("✅ 应用构建完成！")
    return app


# ============== 演示函数 ==============

def run_demo():
    """运行演示"""
    print("=" * 60)
    print("🚀 第03章核心概念演示")
    print("=" * 60)
    
    # 创建应用
    app = create_demo_app()
    
    # 配置会话
    config = {"configurable": {"thread_id": "demo_conversation"}}
    
    # 测试用例
    test_cases = [
        "你好，我是小明",
        "搜索今天北京天气",
        "我刚才说我叫什么名字？",
        "查询人工智能最新发展",
        "谢谢你的帮助"
    ]
    
    print("\n🎭 开始演示...")
    
    for i, user_input in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i} ---")
        print(f"👤 用户: {user_input}")
        
        # 调用应用
        result = app.invoke({
            "messages": [HumanMessage(content=user_input)],
            "search_results": "",
            "intent": ""
        }, config=config)
        
        # 显示结果
        ai_response = result["messages"][-1].content
        print(f"🤖 助手: {ai_response}")
        
        print(f"📊 当前消息总数: {len(result['messages'])}")
        print("-" * 40)
    
    print("\n🎉 演示完成！")
    print("\n💡 核心概念总结:")
    print("• 状态(State): 保存对话历史和上下文")
    print("• 节点(Node): LLM节点和搜索节点执行具体任务")
    print("• 边(Edge): 路由函数智能决定执行路径")
    print("• 持久化(Checkpointer): 记住完整对话历史")


def interactive_demo():
    """交互式演示"""
    print("=" * 60)
    print("🎮 交互式演示模式")
    print("=" * 60)
    
    app = create_demo_app()
    config = {"configurable": {"thread_id": "interactive_conversation"}}
    
    print("\n💬 开始对话 (输入 'quit' 退出):")
    print("💡 提示: 尝试说 '搜索...' 来测试搜索功能")
    
    while True:
        user_input = input("\n👤 你: ").strip()
        
        if user_input.lower() in ['quit', 'exit', '退出']:
            break
        
        if not user_input:
            continue
        
        try:
            result = app.invoke({
                "messages": [HumanMessage(content=user_input)],
                "search_results": "",
                "intent": ""
            }, config=config)
            
            ai_response = result["messages"][-1].content
            print(f"🤖 助手: {ai_response}")
            
        except Exception as e:
            print(f"❌ 错误: {str(e)}")
    
    print("\n👋 再见！")


if __name__ == "__main__":
    # 设置环境变量
    os.environ["OPENAI_API_KEY"] = "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW"
    os.environ["OPENAI_BASE_URL"] = "https://open.bigmodel.cn/api/paas/v4/"
    
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive_demo()
    else:
        run_demo()