#!/usr/bin/env python3
"""
测试第04章中其他工具的功能
包括API调用工具、文件操作工具等
"""

import pytest
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, Mock
from langchain_core.tools import tool
import requests
import os


# 复制文档中的工具实现
@tool
def api_call_tool(url: str, method: str = "GET", data: str = None) -> str:
    """
    通用 API 调用工具
    
    Args:
        url: API 端点 URL
        method: HTTP 方法（GET, POST, PUT, DELETE）
        data: 请求数据（JSON 字符串）
    
    Returns:
        API 响应结果
    """
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            import json
            json_data = json.loads(data) if data else {}
            response = requests.post(url, json=json_data, timeout=10)
        else:
            return f"不支持的 HTTP 方法：{method}"
        
        response.raise_for_status()
        return f"API 调用成功：{response.json()}"
    except requests.exceptions.RequestException as e:
        return f"API 调用失败：{str(e)}"
    except Exception as e:
        return f"处理错误：{str(e)}"


@tool
def file_reader_tool(file_path: str) -> str:
    """
    文件读取工具
    
    Args:
        file_path: 文件路径
    
    Returns:
        文件内容或错误信息
    """
    try:
        path = Path(file_path)
        
        # 安全检查：只允许读取特定目录下的文件
        allowed_dir = Path("./data")  # 限制访问范围
        if not path.is_relative_to(allowed_dir):
            return "错误：不允许访问该路径"
        
        if not path.exists():
            return f"错误：文件 {file_path} 不存在"
        
        with open(path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 限制返回内容长度
        if len(content) > 1000:
            content = content[:1000] + "...(内容过长，已截断)"
        
        return f"文件内容：\n{content}"
    except Exception as e:
        return f"读取文件失败：{str(e)}"


@tool
def file_writer_tool(file_path: str, content: str) -> str:
    """
    文件写入工具
    
    Args:
        file_path: 文件路径
        content: 要写入的内容
    
    Returns:
        操作结果
    """
    try:
        path = Path(file_path)
        
        # 安全检查
        allowed_dir = Path("./data")
        if not path.is_relative_to(allowed_dir):
            return "错误：不允许写入该路径"
        
        # 确保目录存在
        path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return f"成功写入文件：{file_path}"
    except Exception as e:
        return f"写入文件失败：{str(e)}"


class TestAPICallTool:
    """API调用工具测试类"""
    
    @patch('requests.get')
    def test_get_request_success(self, mock_get):
        """测试GET请求成功"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.json.return_value = {"status": "success", "data": "test"}
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = api_call_tool("https://api.example.com/test", "GET")
        assert "API 调用成功" in result
        assert "success" in result
        mock_get.assert_called_once_with("https://api.example.com/test", timeout=10)
    
    @patch('requests.post')
    def test_post_request_success(self, mock_post):
        """测试POST请求成功"""
        mock_response = Mock()
        mock_response.json.return_value = {"result": "created"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        test_data = '{"name": "test", "value": 123}'
        result = api_call_tool("https://api.example.com/create", "POST", test_data)
        
        assert "API 调用成功" in result
        mock_post.assert_called_once_with(
            "https://api.example.com/create", 
            json={"name": "test", "value": 123}, 
            timeout=10
        )
    
    def test_unsupported_method(self):
        """测试不支持的HTTP方法"""
        result = api_call_tool("https://api.example.com/test", "DELETE")
        assert "不支持的 HTTP 方法：DELETE" in result
    
    @patch('requests.get')
    def test_request_exception(self, mock_get):
        """测试请求异常"""
        mock_get.side_effect = requests.exceptions.ConnectionError("Connection failed")
        
        result = api_call_tool("https://api.example.com/test", "GET")
        assert "API 调用失败" in result
        assert "Connection failed" in result
    
    @patch('requests.post')
    def test_invalid_json_data(self, mock_post):
        """测试无效的JSON数据"""
        result = api_call_tool("https://api.example.com/test", "POST", "invalid json")
        assert "处理错误" in result
    
    @patch('requests.post')
    def test_post_without_data(self, mock_post):
        """测试POST请求无数据"""
        mock_response = Mock()
        mock_response.json.return_value = {"result": "ok"}
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        result = api_call_tool("https://api.example.com/test", "POST")
        assert "API 调用成功" in result
        mock_post.assert_called_once_with(
            "https://api.example.com/test", 
            json={}, 
            timeout=10
        )


class TestFileTools:
    """文件操作工具测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时数据目录
        self.test_dir = Path("./data")
        self.test_dir.mkdir(exist_ok=True)
        
        # 创建测试文件
        self.test_file = self.test_dir / "test.txt"
        self.test_file.write_text("Hello, World!\n这是测试内容。", encoding='utf-8')
    
    def teardown_method(self):
        """测试后清理"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_file_reader_success(self):
        """测试文件读取成功"""
        result = file_reader_tool("data/test.txt")
        assert "文件内容：" in result
        assert "Hello, World!" in result
        assert "这是测试内容" in result
    
    def test_file_reader_file_not_exists(self):
        """测试读取不存在的文件"""
        result = file_reader_tool("data/nonexistent.txt")
        assert "错误：文件 data/nonexistent.txt 不存在" in result
    
    def test_file_reader_path_security(self):
        """测试路径安全检查"""
        # 尝试访问上级目录
        result = file_reader_tool("../sensitive.txt")
        assert "错误：不允许访问该路径" in result
        
        # 尝试访问绝对路径
        result = file_reader_tool("/etc/passwd")
        assert "错误：不允许访问该路径" in result
    
    def test_file_reader_large_file(self):
        """测试大文件截断"""
        # 创建大文件
        large_content = "x" * 1500  # 超过1000字符限制
        large_file = self.test_dir / "large.txt"
        large_file.write_text(large_content, encoding='utf-8')
        
        result = file_reader_tool("data/large.txt")
        assert "文件内容：" in result
        assert "...(内容过长，已截断)" in result
        # 检查内容被截断到1000字符
        content_part = result.split("文件内容：\n")[1]
        truncated_content = content_part.replace("...(内容过长，已截断)", "")
        assert len(truncated_content) == 1000
    
    def test_file_writer_success(self):
        """测试文件写入成功"""
        content = "这是新写入的内容\nLine 2"
        result = file_writer_tool("data/new_file.txt", content)
        assert "成功写入文件：data/new_file.txt" in result
        
        # 验证文件确实被写入
        written_file = self.test_dir / "new_file.txt"
        assert written_file.exists()
        assert written_file.read_text(encoding='utf-8') == content
    
    def test_file_writer_create_directory(self):
        """测试自动创建目录"""
        content = "测试内容"
        result = file_writer_tool("data/subdir/newfile.txt", content)
        assert "成功写入文件" in result
        
        # 验证目录和文件都被创建
        new_file = self.test_dir / "subdir" / "newfile.txt"
        assert new_file.exists()
        assert new_file.read_text(encoding='utf-8') == content
    
    def test_file_writer_path_security(self):
        """测试写入路径安全检查"""
        # 尝试写入上级目录
        result = file_writer_tool("../malicious.txt", "bad content")
        assert "错误：不允许写入该路径" in result
        
        # 尝试写入绝对路径
        result = file_writer_tool("/tmp/malicious.txt", "bad content")
        assert "错误：不允许写入该路径" in result
    
    def test_file_writer_permission_error(self):
        """测试写入权限错误"""
        # 尝试写入只读目录（模拟）
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            result = file_writer_tool("data/readonly.txt", "content")
            assert "写入文件失败" in result
            assert "Permission denied" in result


class TestToolSecurity:
    """工具安全性测试"""
    
    def test_api_tool_security(self):
        """测试API工具安全性"""
        # 测试恶意URL
        result = api_call_tool("file:///etc/passwd", "GET")
        assert "API 调用失败" in result or "处理错误" in result
    
    def test_file_path_traversal_attempts(self):
        """测试路径遍历攻击"""
        malicious_paths = [
            "../../etc/passwd",
            "../../../sensitive.txt",
            "data/../../../etc/hosts",
            "data/./../../secret.txt"
        ]
        
        for path in malicious_paths:
            result = file_reader_tool(path)
            assert "错误：不允许访问该路径" in result, f"路径 {path} 应该被拒绝"
    
    def test_file_writer_security(self):
        """测试文件写入安全性"""
        # 尝试覆盖重要文件（通过路径遍历）
        result = file_writer_tool("data/../../../important.txt", "malicious content")
        assert "错误：不允许写入该路径" in result


class TestErrorHandling:
    """错误处理测试"""
    
    def test_api_tool_malformed_response(self):
        """测试API响应格式错误"""
        with patch('requests.get') as mock_get:
            mock_response = Mock()
            mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            
            result = api_call_tool("https://api.example.com/test", "GET")
            assert "处理错误" in result
    
    def test_file_encoding_error(self):
        """测试文件编码错误"""
        # 创建包含特殊字符的文件
        test_dir = Path("./data")
        test_dir.mkdir(exist_ok=True)
        
        try:
            # 写入二进制内容
            binary_file = test_dir / "binary.txt"
            binary_file.write_bytes(b'\x80\x81\x82\x83')
            
            result = file_reader_tool("data/binary.txt")
            # 应该能处理编码错误
            assert "读取文件失败" in result or "文件内容：" in result
        finally:
            if test_dir.exists():
                shutil.rmtree(test_dir)


def test_api_tool_integration():
    """API工具集成测试"""
    # 测试真实的公共API（如果网络可用）
    try:
        result = api_call_tool("https://httpbin.org/json", "GET")
        # httpbin可能不可用，所以只检查是否有合理的响应
        assert "API 调用成功" in result or "API 调用失败" in result
    except Exception:
        # 网络不可用时跳过
        pass


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])