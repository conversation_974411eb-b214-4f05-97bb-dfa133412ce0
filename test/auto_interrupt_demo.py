#!/usr/bin/env python3
"""
自动演示interrupt机制完整流程
不需要手动输入，自动展示人机协作过程
"""
import os
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class ReviewState(TypedDict):
    messages: Annotated[list, add_messages]
    pending_action: str
    confidence_score: float
    requires_approval: bool
    human_feedback: str

def create_demo_app():
    """创建演示应用"""
    
    def ai_analysis_node(state: ReviewState):
        """AI 分析节点"""
        last_message = state["messages"][-1].content
        print(f"🤖 AI正在分析请求: {last_message}")
        
        # 使用真实GLM-4.5分析（如果有API key）
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(
                    model="glm-4.5",
                    temperature=0.1,
                    api_key=api_key
                )
                
                analysis_prompt = f"""
                分析以下用户请求，评估其风险等级和你的置信度：
                用户请求：{last_message}
                
                请返回：
                1. 建议的操作
                2. 风险等级（低/中/高）
                3. 置信度（0-1之间的数字）
                
                格式：操作|风险等级|置信度
                """
                
                response = llm.invoke([HumanMessage(content=analysis_prompt)])
                analysis = response.content.strip()
                print(f"🧠 GLM-4.5分析结果: {analysis}")
                
            except Exception as e:
                print(f"⚠️ API调用失败: {e}")
                analysis = None
        else:
            analysis = None
        
        # 解析分析结果
        if analysis:
            try:
                parts = analysis.split('|')
                action = parts[0].strip()
                risk_level = parts[1].strip()
                confidence = float(parts[2].strip())
            except:
                action, risk_level, confidence = "默认回复", "中", 0.5
        else:
            # 基于关键词的分析
            if any(word in last_message.lower() for word in ["删除", "删掉", "清空"]):
                action, risk_level, confidence = "删除操作", "高", 0.9
            elif any(word in last_message.lower() for word in ["发送", "群发", "推送"]):
                action, risk_level, confidence = "发送操作", "高", 0.8
            elif any(word in last_message.lower() for word in ["复杂", "不确定", "不知道"]):
                action, risk_level, confidence = "咨询回复", "中", 0.4
            else:
                action, risk_level, confidence = "常规回复", "低", 0.9
        
        # 判断是否需要人工审核
        needs_approval = (
            confidence < 0.7 or 
            risk_level == "高" or
            any(word in last_message.lower() for word in ["删除", "发送", "支付"])
        )
        
        print(f"📊 AI分析结果:")
        print(f"   • 建议操作: {action}")
        print(f"   • 风险等级: {risk_level}")
        print(f"   • 置信度: {confidence:.2f}")
        print(f"   • 需要审核: {'是' if needs_approval else '否'}")
        
        return {
            "pending_action": action,
            "confidence_score": confidence,
            "requires_approval": needs_approval
        }
    
    def human_review_node(state: ReviewState):
        """人工审核节点 - 触发interrupt"""
        if state.get("requires_approval", False):
            print("\n" + "🔴" * 20 + " INTERRUPT触发 " + "🔴" * 20)
            print("⏸️  AI请求人工干预！执行已暂停...")
            
            review_info = {
                "用户请求": state["messages"][-1].content,
                "AI建议": state.get("pending_action", "未知"),
                "置信度": state.get("confidence_score", 0),
                "原因": "高风险或低置信度操作需要人工确认"
            }
            
            print("📋 需要审核的信息:")
            for key, value in review_info.items():
                print(f"   • {key}: {value}")
            
            print("\n💡 等待人工决策...")
            print("   选项: 'approved' | 'rejected' | '自定义指导'")
            
            # 这里是真正的interrupt - 会抛出异常暂停执行
            return interrupt(review_info)
        
        # 低风险，自动通过
        print("✅ 低风险操作，自动批准")
        return {"human_feedback": "auto_approved"}
    
    def execution_node(state: ReviewState):
        """执行节点"""
        human_feedback = state.get("human_feedback", "auto_approved")
        user_request = state["messages"][-1].content
        
        print(f"\n⚡ 执行阶段")
        print(f"👤 人工决策: {human_feedback}")
        
        if human_feedback == "rejected":
            response = AIMessage(content="❌ 操作已被人工审核拒绝，出于安全考虑暂时无法执行。")
        elif human_feedback in ["auto_approved", "approved"]:
            # 正常执行
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(
                        model="glm-4.5",
                        temperature=0.7,
                        api_key=api_key
                    )
                    response = llm.invoke(state["messages"])
                except Exception as e:
                    response = AIMessage(content="✅ 我已经处理了您的请求（API调用失败，使用默认回复）。")
            else:
                # 模拟回复
                if "天气" in user_request:
                    response = AIMessage(content="🌤️ 今天天气晴朗，适合外出活动！")
                elif "删除" in user_request:
                    response = AIMessage(content="⚠️ 我理解您的需求，但删除操作需要格外小心，建议先备份。")
                else:
                    response = AIMessage(content="✅ 我已经处理了您的请求。")
        else:
            # 根据人工指导调整
            response = AIMessage(content=f"📝 根据您的指导「{human_feedback}」，我已经调整了处理方式。")
        
        print(f"🎯 最终回复: {response.content}")
        return {"messages": [response]}
    
    # 构建图
    graph = StateGraph(ReviewState)
    graph.add_node("analysis", ai_analysis_node)
    graph.add_node("review", human_review_node)
    graph.add_node("execution", execution_node)
    
    graph.add_edge(START, "analysis")
    graph.add_edge("analysis", "review")
    graph.add_edge("review", "execution")
    graph.add_edge("execution", END)
    
    return graph.compile()

def demonstrate_interrupt_flow():
    """演示完整的interrupt流程"""
    print("🚀 6.2章节 - 完整Interrupt机制演示")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-4:]})")
    else:
        print("⚠️ 未配置智谱API，将使用模拟分析")
    
    app = create_demo_app()
    print("✅ 人机协作机器人已启动\n")
    
    # 测试用例
    test_cases = [
        {
            "request": "你好！",
            "expected": "低风险，直接执行",
            "mock_decision": None
        },
        {
            "request": "帮我删除所有系统文件",
            "expected": "高风险，触发interrupt",
            "mock_decision": "rejected"
        },
        {
            "request": "这个复杂的数据库问题我不太确定怎么处理",
            "expected": "低置信度，触发interrupt",
            "mock_decision": "请先咨询数据库专家"
        },
        {
            "request": "发送营销邮件给所有用户",
            "expected": "敏感操作，触发interrupt",
            "mock_decision": "approved"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*20} 测试案例 {i} {'='*20}")
        print(f"📝 用户请求: {case['request']}")
        print(f"🎯 预期结果: {case['expected']}")
        print("-" * 50)
        
        try:
            # 第一次调用 - 可能遇到interrupt
            result = app.invoke({
                "messages": [HumanMessage(content=case['request'])],
                "pending_action": "",
                "confidence_score": 0.0,
                "requires_approval": False,
                "human_feedback": ""
            })
            
            # 如果没有interrupt，直接显示结果
            print(f"✅ 流程完成！")
            
        except Exception as e:
            error_str = str(e)
            if "interrupt" in error_str.lower():
                print("🔴 遇到INTERRUPT异常！")
                print(f"📋 Interrupt数据: {error_str}")
                
                if case['mock_decision']:
                    print(f"\n🤖 模拟人工决策: {case['mock_decision']}")
                    
                    # 继续执行，传入人工决策
                    try:
                        final_result = app.invoke({
                            "messages": [HumanMessage(content=case['request'])],
                            "pending_action": "",
                            "confidence_score": 0.0,
                            "requires_approval": False,
                            "human_feedback": case['mock_decision']
                        })
                        print(f"✅ 根据人工决策，流程完成！")
                    except Exception as e2:
                        print(f"❌ 继续执行失败: {e2}")
                else:
                    print("💡 在真实场景中，这里会等待人工输入...")
            else:
                print(f"❌ 其他错误: {error_str}")
        
        print("=" * 60)
    
    print("\n🎉 演示完成！")
    print("📝 总结:")
    print("• interrupt机制成功在高风险情况下暂停执行")
    print("• AI能准确评估风险等级和置信度")
    print("• 人工决策能有效控制后续执行流程")
    print("• GLM-4.5提供了高质量的分析和回复")

if __name__ == "__main__":
    demonstrate_interrupt_flow()