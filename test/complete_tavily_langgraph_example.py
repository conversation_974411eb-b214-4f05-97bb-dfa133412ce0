#!/usr/bin/env python3
"""
完整的LangGraph + Tavily搜索工具示例
展示如何构建一个使用Tavily搜索的智能助手
"""

from typing import Optional, Type, TypedDict, List
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field
from tavily import TavilyClient
from langgraph.prebuilt import ToolNode, tools_condition, create_react_agent
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver


class TavilySearchInput(BaseModel):
    """Tavily搜索工具的输入模式"""
    query: str = Field(description="要搜索的查询内容")


class TavilySearchTool(BaseTool):
    """Tavily搜索工具 - 符合LangChain工具接口"""
    
    name: str = "tavily_search"
    description: str = "使用Tavily搜索引擎获取最新的网络信息。适用于需要实时数据、新闻、天气、股价等查询。"
    args_schema: Type[BaseModel] = TavilySearchInput
    api_key: str = Field(exclude=True)
    client: TavilyClient = Field(exclude=True)
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key=api_key, client=TavilyClient(api_key=api_key), **kwargs)
    
    def _run(self, query: str, run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """执行Tavily搜索"""
        try:
            response = self.client.search(
                query=query,
                search_depth="basic",
                max_results=3,
                include_answer=True,
                include_raw_content=False
            )
            
            if response.get("answer"):
                return f"搜索结果: {response['answer']}"
            elif response.get("results"):
                # 如果没有答案摘要，返回前几个结果
                results = response["results"][:2]
                summary = "搜索结果:\n"
                for i, result in enumerate(results, 1):
                    title = result.get("title", "无标题")
                    content = result.get("content", "无内容")[:200]
                    summary += f"{i}. {title}: {content}...\n"
                return summary.strip()
            else:
                return "未找到相关搜索结果"
                
        except Exception as e:
            return f"搜索出错: {str(e)}"


def create_tavily_react_agent():
    """创建使用Tavily搜索的ReAct代理"""
    
    # 1. 配置GLM-4.5模型
    model = ChatOpenAI(
        model="glm-4.5",
        api_key="4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW",
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0.3
    )
    
    # 2. 创建Tavily搜索工具
    tavily_tool = TavilySearchTool(api_key="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")
    tools = [tavily_tool]
    
    # 3. 使用create_react_agent创建代理
    agent = create_react_agent(model, tools)
    
    return agent


class AgentState(TypedDict):
    """代理状态定义"""
    messages: List[HumanMessage | AIMessage | ToolMessage]
    query: str
    search_performed: bool
    final_answer: str


def create_custom_search_agent():
    """创建自定义的搜索代理工作流"""
    
    # 配置模型和工具
    model = ChatOpenAI(
        model="glm-4.5",
        api_key="4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW",
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0.3
    )
    
    tavily_tool = TavilySearchTool(api_key="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")
    
    def search_node(state: AgentState) -> AgentState:
        """搜索节点：执行Tavily搜索"""
        query = state["query"]
        search_result = tavily_tool._run(query)
        
        return {
            "search_performed": True,
            "messages": state["messages"] + [
                ToolMessage(content=search_result, tool_call_id="search_1")
            ]
        }
    
    def answer_node(state: AgentState) -> AgentState:
        """回答节点：基于搜索结果生成回答"""
        messages = state["messages"]
        
        # 获取搜索结果
        search_results = ""
        for msg in messages:
            if isinstance(msg, ToolMessage):
                search_results = msg.content
                break
        
        # 构建提示
        prompt = f"""基于以下搜索结果回答用户问题：

搜索结果：{search_results}

用户问题：{state["query"]}

请提供准确、有用的回答："""
        
        response = model.invoke([HumanMessage(content=prompt)])
        
        return {
            "final_answer": response.content,
            "messages": state["messages"] + [response]
        }
    
    # 构建状态图
    workflow = StateGraph(AgentState)
    workflow.add_node("search", search_node)
    workflow.add_node("answer", answer_node)
    
    workflow.set_entry_point("search")
    workflow.add_edge("search", "answer")
    workflow.add_edge("answer", END)
    
    # 添加持久化
    memory = MemorySaver()
    app = workflow.compile(checkpointer=memory)
    
    return app


def main():
    """主函数：演示两种不同的代理实现"""
    
    print("🚀 LangGraph + Tavily搜索工具完整示例")
    print("=" * 50)
    
    # 示例1：使用create_react_agent
    print("\n📝 示例1：使用create_react_agent")
    print("-" * 30)
    
    react_agent = create_tavily_react_agent()
    
    # 测试查询
    messages = [HumanMessage(content="今天北京的天气怎么样？")]
    
    print(f"用户查询: {messages[0].content}")
    print("代理思考中...")
    
    try:
        result = react_agent.invoke({"messages": messages})
        
        # 获取最终回答
        final_message = result["messages"][-1]
        if hasattr(final_message, 'content'):
            print(f"代理回复: {final_message.content}")
        else:
            print(f"代理回复: {final_message}")
    except Exception as e:
        print(f"ReAct代理出错: {e}")
    
    print("\n" + "=" * 50)
    
    # 示例2：使用自定义StateGraph
    print("\n🔧 示例2：使用自定义StateGraph工作流")
    print("-" * 40)
    
    custom_agent = create_custom_search_agent()
    
    # 测试查询
    query = "2024年人工智能发展趋势如何？"
    print(f"用户查询: {query}")
    
    try:
        config = {"configurable": {"thread_id": "demo_conversation"}}
        result = custom_agent.invoke({
            "query": query,
            "messages": [HumanMessage(content=query)],
            "search_performed": False,
            "final_answer": ""
        }, config=config)
        
        print(f"搜索完成: {result['search_performed']}")
        print(f"最终回答: {result['final_answer']}")
    except Exception as e:
        print(f"自定义代理出错: {e}")
    
    print("\n✅ 演示完成！")
    print("\n💡 关键特性：")
    print("- ✅ 使用Tavily搜索引擎获取实时信息")
    print("- ✅ 符合LangChain工具接口标准") 
    print("- ✅ 支持ReAct模式和自定义工作流")
    print("- ✅ 集成GLM-4.5大语言模型")
    print("- ✅ 支持状态持久化")


if __name__ == "__main__":
    main()