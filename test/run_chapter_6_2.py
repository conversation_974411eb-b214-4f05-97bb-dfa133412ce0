#!/usr/bin/env python3
"""
运行 06-人机协作.md 文档中 6.2 部分的代码
展示 interrupt 机制的效果
"""
import os
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class ReviewState(TypedDict):
    messages: Annotated[list, add_messages]
    pending_action: str
    confidence_score: float
    requires_approval: bool
    human_feedback: str

def create_human_in_loop_chatbot():
    """创建人机协作聊天机器人"""
    
    def ai_analysis_node(state: ReviewState):
        """AI 分析节点"""
        import os
        last_message = state["messages"][-1].content
        
        # 智谱GLM-4.5配置
        try:
            from langchain_community.chat_models import ChatZhipuAI
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.1,
                api_key=os.getenv("ZHIPUAI_API_KEY")
            )
            
            analysis_prompt = f"""
            分析以下用户请求，评估其风险等级和你的置信度：
            用户请求：{last_message}
            
            请返回：
            1. 建议的操作
            2. 风险等级（低/中/高）
            3. 置信度（0-1之间的数字）
            
            格式：操作|风险等级|置信度
            """
            
            response = llm.invoke([HumanMessage(content=analysis_prompt)])
            analysis = response.content.strip()
            
        except (ImportError, Exception) as e:
            print(f"⚠️ API调用失败，使用模拟分析: {e}")
            # 基于关键词的模拟分析
            if "删除" in last_message:
                analysis = "删除文件操作|高|0.9"
            elif "发送" in last_message:
                analysis = "发送通知|高|0.8"
            elif "复杂" in last_message or "不确定" in last_message:
                analysis = "复杂咨询|中|0.4"
            elif "天气" in last_message:
                analysis = "天气查询|低|0.95"
            else:
                analysis = "一般回复|中|0.7"
        
        try:
            parts = analysis.split('|')
            action = parts[0].strip()
            risk_level = parts[1].strip()
            confidence = float(parts[2].strip())
        except:
            # 解析失败时的默认值
            action = "general_response"
            risk_level = "中"
            confidence = 0.5
        
        # 判断是否需要人工审核
        needs_approval = (
            confidence < 0.7 or  # 置信度低
            risk_level == "高" or  # 高风险
            "删除" in last_message or 
            "发送" in last_message  # 敏感操作
        )
        
        print(f"🤖 AI分析结果: {analysis}")
        print(f"📊 评估结果: 操作={action}, 风险={risk_level}, 置信度={confidence:.2f}, 需审核={needs_approval}")
        
        return {
            "pending_action": action,
            "confidence_score": confidence,
            "requires_approval": needs_approval
        }
    
    def human_review_node(state: ReviewState):
        """人工审核节点"""
        if state.get("requires_approval", False):
            # 构造审核信息
            review_info = {
                "user_request": state["messages"][-1].content,
                "ai_suggestion": state.get("pending_action", "未知"),
                "confidence": state.get("confidence_score", 0),
                "reason": "需要人工确认"
            }
            
            print(f"🔍 需要人工审核: {review_info}")
            # 使用 interrupt 暂停执行，等待人工输入
            return interrupt(review_info)
        
        # 不需要审核，直接通过
        print("✅ 无需审核，自动通过")
        return {"human_feedback": "auto_approved"}
    
    def execution_node(state: ReviewState):
        """执行节点"""
        import os
        human_feedback = state.get("human_feedback", "")
        
        print(f"👤 人工反馈: {human_feedback}")
        
        if human_feedback == "rejected":
            return {
                "messages": [AIMessage(content="抱歉，该操作已被拒绝。")]
            }
        elif human_feedback == "auto_approved" or human_feedback == "approved":
            # 执行原始请求
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(
                    model="glm-4.5",
                    temperature=0.7,
                    api_key=os.getenv("ZHIPUAI_API_KEY")
                )
                response = llm.invoke(state["messages"])
                return {"messages": [response]}
            except (ImportError, Exception) as e:
                print(f"⚠️ API调用失败，使用模拟回复: {e}")
                # 模拟响应
                user_content = state["messages"][-1].content
                if "天气" in user_content:
                    mock_response = AIMessage(content="今天天气晴朗，温度适宜，适合外出活动。")
                elif "删除" in user_content:
                    mock_response = AIMessage(content="我不能执行删除文件的操作，这可能会造成数据丢失。")
                elif "发送" in user_content:
                    mock_response = AIMessage(content="邮件发送功能需要相应权限，请联系管理员。")
                else:
                    mock_response = AIMessage(content="我已经处理了您的请求。")
                return {"messages": [mock_response]}
        else:
            # 根据人工反馈调整回复
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(
                    model="glm-4.5",
                    temperature=0.7,
                    api_key=os.getenv("ZHIPUAI_API_KEY")
                )
                adjusted_prompt = f"""
                用户原始请求：{state['messages'][-1].content}
                人工反馈：{human_feedback}
                请根据人工反馈调整你的回复。
                """
                response = llm.invoke([HumanMessage(content=adjusted_prompt)])
                return {"messages": [response]}
            except (ImportError, Exception) as e:
                print(f"⚠️ API调用失败，使用模拟调整回复: {e}")
                mock_response = AIMessage(content=f"根据您的反馈「{human_feedback}」，我已经调整了处理方式。")
                return {"messages": [mock_response]}
    
    # 构建图
    graph = StateGraph(ReviewState)
    
    # 添加节点
    graph.add_node("ai_analysis", ai_analysis_node)
    graph.add_node("human_review", human_review_node)
    graph.add_node("execution", execution_node)
    
    # 设置流程
    graph.add_edge(START, "ai_analysis")
    graph.add_edge("ai_analysis", "human_review")
    graph.add_edge("human_review", "execution")
    graph.add_edge("execution", END)
    
    return graph.compile()

def demo_human_in_loop():
    """演示人机协作"""
    import os
    
    print("🚀 启动人机协作聊天机器人")
    print("🔧 使用智谱GLM-4.5模型")
    print("=" * 60)
    
    # 检查环境配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("⚠️ 未设置 ZHIPUAI_API_KEY 环境变量")
        print("💡 将使用模拟模式进行演示")
        print("🔧 如需使用真实API，请设置：export ZHIPUAI_API_KEY=your_key")
        print("-" * 60)
    else:
        print(f"✅ 检测到API Key: {api_key[:10]}...{api_key[-4:]} (已脱敏)")
        print("🚀 将使用真实智谱GLM-4.5 API")
        print("-" * 60)
    
    try:
        app = create_human_in_loop_chatbot()
        print("✅ 成功创建人机协作应用\n")
    except Exception as e:
        print(f"❌ 创建应用失败: {e}")
        return
    
    print("🤖 人机协作聊天机器人")
    print("📊 AI会自动评估风险，必要时请求人工确认")
    print("⚠️ 高风险或低置信度的操作需要人工审核\n")
    
    # 测试不同类型的请求
    test_requests = [
        "你好，今天天气怎么样？",  # 低风险
        "帮我删除所有文件",       # 高风险
        "这个复杂的技术问题我不太确定",  # 低置信度
        "发送邮件给所有客户"       # 敏感操作
    ]
    
    for i, request in enumerate(test_requests, 1):
        print(f"\n{'='*20} 测试 {i}/{len(test_requests)} {'='*20}")
        print(f"📝 测试请求: {request}")
        
        try:
            # 初始调用
            result = app.invoke({
                "messages": [HumanMessage(content=request)],
                "pending_action": "",
                "confidence_score": 0.0,
                "requires_approval": False,
                "human_feedback": ""
            })
            print(f"✅ 直接回复: {result['messages'][-1].content}")
            
        except Exception as e:
            # 如果遇到 interrupt，说明需要人工干预
            if "interrupt" in str(e).lower():
                print("⏸️ AI请求人工审核")
                print("📋 审核信息:", str(e))
                
                # 模拟人工决策
                print("\n🤖 模拟人工审核过程...")
                if "删除" in request:
                    human_decision = "rejected"
                    print(f"👤 人工决策: {human_decision} (删除操作太危险)")
                elif "发送" in request:
                    human_decision = "请先确认收件人列表和邮件内容"
                    print(f"👤 人工决策: {human_decision}")
                elif "复杂" in request or "不确定" in request:
                    human_decision = "approved"
                    print(f"👤 人工决策: {human_decision} (提供帮助)")
                else:
                    human_decision = "approved"
                    print(f"👤 人工决策: {human_decision}")
                
                # 继续执行
                try:
                    # 创建新的状态对象并添加人工反馈
                    updated_state = {
                        "messages": [HumanMessage(content=request)],
                        "pending_action": "",
                        "confidence_score": 0.0,
                        "requires_approval": False,
                        "human_feedback": human_decision
                    }
                    final_result = app.invoke(updated_state)
                    print(f"✅ 最终回复: {final_result['messages'][-1].content}")
                except Exception as e2:
                    print(f"❌ 执行失败: {e2}")
            else:
                print(f"❌ 其他错误: {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    demo_human_in_loop()