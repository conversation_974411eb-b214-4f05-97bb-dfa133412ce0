#!/usr/bin/env python3
"""
测试修复后的工具工作流代码
验证参数验证和智能修复功能
"""

import os
import re
import math
from typing import Annotated
from typing_extensions import TypedDict

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, ToolMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

# 设置GLM-4.5配置
os.environ.setdefault("OPENAI_API_KEY", "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW")
os.environ.setdefault("OPENAI_BASE_URL", "https://open.bigmodel.cn/api/paas/v4/")

class AssistantState(TypedDict):
    messages: Annotated[list, add_messages]

class EnhancedState(TypedDict):
    messages: Annotated[list, add_messages]
    tool_results: dict
    context: dict
    user_preferences: dict

# 定义工具
@tool
def calculator(expression: str) -> str:
    """数学计算器，支持基本运算"""
    try:
        import math
        allowed_names = {
            k: v for k, v in math.__dict__.items() if not k.startswith("__")
        }
        allowed_names.update({"abs": abs, "round": round})
        allowed_names["math"] = math
        
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return f"计算结果：{result}"
    except Exception as e:
        return f"计算错误：{str(e)}"

@tool
def web_search(query: str) -> str:
    """网络搜索工具"""
    mock_results = {
        "人工智能": "最新AI发展：GPT-4、DALL-E、ChatGPT等技术突破",
        "新闻": "今日头条新闻：科技、经济、社会等各领域最新动态",
        "天气": "天气预报：今日晴天，温度适宜",
        "langgraph": "LangGraph：构建有状态多参与者应用的强大框架"
    }
    
    for key, result in mock_results.items():
        if key in query.lower():
            return f"搜索结果：{result}"
    
    return f"搜索结果：关于'{query}'的相关信息"

@tool
def current_time() -> str:
    """获取当前时间"""
    from datetime import datetime
    now = datetime.now()
    return f"当前时间：{now.strftime('%Y-%m-%d %H:%M:%S')}"

tools = [calculator, web_search, current_time]

# 修复后的 robust_tool_node
def robust_tool_node(state: AssistantState):
    """带错误处理和参数验证的工具节点"""
    messages = state["messages"]
    last_message = messages[-1]
    
    if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
        return {"messages": []}
    
    tool_results = []
    
    for tool_call in last_message.tool_calls:
        try:
            # 查找对应的工具
            tool_func = None
            for tool in tools:
                if tool.name == tool_call["name"]:
                    tool_func = tool
                    break
            
            if tool_func is None:
                result = f"错误：未找到工具 {tool_call['name']}"
            else:
                # 获取工具调用参数并进行验证
                tool_args = tool_call.get("args", {})
                
                # 参数验证和智能修复
                if not tool_args or tool_args == {}:
                    # 针对计算器工具的智能参数修复
                    if tool_call['name'] == 'calculator':
                        # 尝试从AI消息内容中提取数学表达式
                        ai_content = last_message.content.lower()
                        if any(op in ai_content for op in ['计算', '算', '+', '-', '*', '/', '除以', '乘以']):
                            # 提取可能的数学表达式
                            # 查找数字和运算符的组合
                            math_patterns = [
                                r'(\d+)\s*除以\s*(\d+)',  # "100除以7"
                                r'(\d+)\s*/\s*(\d+)',     # "100/7"
                                r'(\d+)\s*\*\s*(\d+)',    # "2*3"
                                r'(\d+)\s*\+\s*(\d+)',    # "2+3"
                                r'(\d+)\s*-\s*(\d+)',     # "5-2"
                            ]
                            
                            expression = None
                            for pattern in math_patterns:
                                match = re.search(pattern, ai_content)
                                if match:
                                    if '除以' in pattern:
                                        expression = f"{match.group(1)} / {match.group(2)}"
                                    elif '/' in pattern:
                                        expression = f"{match.group(1)} / {match.group(2)}"
                                    elif '*' in pattern:
                                        expression = f"{match.group(1)} * {match.group(2)}"
                                    elif '+' in pattern:
                                        expression = f"{match.group(1)} + {match.group(2)}"
                                    elif '-' in pattern:
                                        expression = f"{match.group(1)} - {match.group(2)}"
                                    break
                            
                            if expression:
                                tool_args = {"expression": expression}
                                result = tool_func.invoke(tool_args)
                            else:
                                result = "错误：无法识别要计算的数学表达式，请明确指定计算内容"
                        else:
                            result = "错误：calculator工具缺少必要的expression参数"
                    else:
                        result = f"错误：工具 {tool_call['name']} 缺少必要参数"
                else:
                    # 正常执行工具
                    result = tool_func.invoke(tool_args)
            
            # 创建工具消息
            tool_message = ToolMessage(
                content=result,
                tool_call_id=tool_call["id"],
                name=tool_call["name"]
            )
            tool_results.append(tool_message)
            
        except Exception as e:
            # 错误处理
            error_message = ToolMessage(
                content=f"工具执行失败：{str(e)}",
                tool_call_id=tool_call["id"],
                name=tool_call["name"]
            )
            tool_results.append(error_message)
    
    return {"messages": tool_results}

def test_parameter_recovery():
    """测试参数智能修复功能"""
    print("🔧 测试参数智能修复功能")
    print("=" * 60)
    
    # 模拟LLM发送的空参数工具调用
    test_cases = [
        {
            "ai_content": "我需要计算100除以7",
            "tool_call": {
                "name": "calculator",
                "args": {},  # 空参数
                "id": "call_1"
            },
            "expected": "100 / 7"
        },
        {
            "ai_content": "帮我算一下 123 * 456",
            "tool_call": {
                "name": "calculator", 
                "args": {},
                "id": "call_2"
            },
            "expected": "123 * 456"
        },
        {
            "ai_content": "计算 50 + 25",
            "tool_call": {
                "name": "calculator",
                "args": {},
                "id": "call_3"
            },
            "expected": "50 + 25"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['ai_content']}")
        
        # 模拟AI消息
        ai_message = AIMessage(
            content=test_case["ai_content"],
            tool_calls=[test_case["tool_call"]]
        )
        
        state = {"messages": [HumanMessage(content="用户请求"), ai_message]}
        
        # 执行工具节点
        result = robust_tool_node(state)
        
        if result["messages"]:
            tool_msg = result["messages"][0]
            print(f"   工具结果: {tool_msg.content}")
            
            # 验证是否成功修复参数
            if "计算结果" in tool_msg.content:
                print(f"   ✅ 成功修复参数并执行计算")
            else:
                print(f"   ❌ 参数修复失败")
        else:
            print(f"   ❌ 没有返回工具结果")

def test_normal_parameters():
    """测试正常参数传递"""
    print("\n🔧 测试正常参数传递")
    print("=" * 60)
    
    # 模拟正常的工具调用
    ai_message = AIMessage(
        content="我来计算这个表达式",
        tool_calls=[{
            "name": "calculator",
            "args": {"expression": "2 + 3 * 4"},
            "id": "call_normal"
        }]
    )
    
    state = {"messages": [HumanMessage(content="计算2+3*4"), ai_message]}
    result = robust_tool_node(state)
    
    if result["messages"]:
        tool_msg = result["messages"][0]
        print(f"正常参数测试结果: {tool_msg.content}")
        if "计算结果：14" in tool_msg.content:
            print("✅ 正常参数传递测试通过")
        else:
            print("❌ 正常参数传递测试失败")

def test_error_cases():
    """测试错误情况处理"""
    print("\n🔧 测试错误情况处理")
    print("=" * 60)
    
    # 测试无法识别的表达式
    ai_message = AIMessage(
        content="我想要做一些计算，但没有具体说明",
        tool_calls=[{
            "name": "calculator",
            "args": {},
            "id": "call_error"
        }]
    )
    
    state = {"messages": [HumanMessage(content="模糊请求"), ai_message]}
    result = robust_tool_node(state)
    
    if result["messages"]:
        tool_msg = result["messages"][0]
        print(f"错误处理测试结果: {tool_msg.content}")
        if "无法识别" in tool_msg.content or "缺少必要" in tool_msg.content:
            print("✅ 错误处理测试通过")
        else:
            print("❌ 错误处理测试失败")

if __name__ == "__main__":
    print("🔍 修复后的工具工作流验证")
    print("=" * 60)
    
    # 测试参数智能修复
    test_parameter_recovery()
    
    # 测试正常参数传递 
    test_normal_parameters()
    
    # 测试错误情况
    test_error_cases()
    
    print("\n✅ 所有修复验证完成！")
    print("\n📋 修复总结:")
    print("- ✅ 添加了工具参数验证机制")
    print("- ✅ 实现了智能参数修复功能") 
    print("- ✅ 支持从AI消息内容中提取数学表达式")
    print("- ✅ 提供了清晰的错误信息")
    print("- ✅ 保持了原有功能的向后兼容性")