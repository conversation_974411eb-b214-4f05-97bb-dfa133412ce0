#!/usr/bin/env python3
"""
测试最终更新后的06文档代码
验证真正的交互式Human-in-the-Loop机制
"""
import os
import uuid
from typing_extensions import TypedDict
from langgraph.types import interrupt, Command
from langgraph.graph import StateGraph, START
from langchain_core.messages import HumanMessage

class WorkflowState(TypedDict):
    user_text: str

def create_resumable_workflow():
    """创建可恢复的工作流（基于官方示例）"""
    
    def human_node(state: WorkflowState):
        """人工干预节点 - 基于官方interrupt模式"""
        import os
        
        # 使用interrupt请求人工修改
        revised_text = interrupt({
            "text_to_revise": state["user_text"],
            "instruction": "请修改以下文本",
            "note": "程序已暂停，等待您的输入..."
        })
        
        # 可选：使用GLM-4.5处理修改后的文本
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key)
                
                prompt = f"请简短优化这段文本：{revised_text}"
                response = llm.invoke([HumanMessage(content=prompt)])
                final_text = response.content
            except Exception as e:
                print(f"⚠️ GLM-4.5调用失败: {e}")
                final_text = revised_text
        else:
            final_text = revised_text
        
        return {"user_text": final_text}
    
    # 构建图 - 基于官方示例
    from langgraph.checkpoint.memory import MemorySaver
    
    graph = StateGraph(WorkflowState)
    graph.add_node("human_node", human_node)
    graph.add_edge(START, "human_node")
    
    # checkpointer是interrupt机制的必需组件
    checkpointer = MemorySaver()
    return graph.compile(checkpointer=checkpointer)

def demo_resumable_workflow():
    """演示人工干预工作流（基于官方示例）"""
    import uuid
    from langgraph.types import Command
    
    print("🔄 真正的交互式 Human-in-the-Loop 演示")
    print("📝 演示文本修改和人工干预流程")
    print("⏸️ 程序将在 interrupt 处暂停，等待您的输入\n")
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置")
    else:
        print("⚠️ 未配置智谱API")
    
    # 创建应用
    app = create_resumable_workflow()
    print("✅ 工作流已创建")
    
    # 配置线程ID（interrupt机制必需）
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    # 初始文本
    original_text = "今天是个好天气，适合出去走走。"
    print(f"📝 原始文本: {original_text}")
    
    # 第一步：运行工作流直到interrupt
    print("\n🚀 启动工作流...")
    print("⏸️ 工作流将在 interrupt 处暂停，等待您的输入")
    result = app.invoke({"user_text": original_text}, config=config)
    
    # 检查是否触发了interrupt
    if "__interrupt__" in result:
        print("\n🔔 工作流已暂停！触发了 interrupt")
        interrupt_info = result["__interrupt__"][0]
        
        print("📋 Interrupt 信息:")
        for key, value in interrupt_info.value.items():
            print(f"  • {key}: {value}")
        
        print("\n" + "="*50)
        print("现在需要您亲自输入修改后的文本！")
        print("="*50)
        
        # 等待用户真实输入
        user_input = input("\n✏️ 请输入修改后的文本: ").strip()
        
        if not user_input:
            print("❌ 输入为空，使用原文本")
            user_input = original_text
        
        print(f"📝 您输入的文本: {user_input}")
        
        # 使用用户的真实输入
        human_revision = user_input
        
        # 第二步：使用Command恢复执行
        print("\n🔄 使用您的输入恢复工作流...")
        final_result = app.invoke(Command(resume=human_revision), config=config)
        
        print(f"\n✅ 最终处理结果:")
        print(f"📄 {final_result['user_text']}")
    else:
        print("❌ 没有触发interrupt，这不应该发生！")
    
    print("\n🎉 演示完成！这就是真正的 Human-in-the-Loop！")

if __name__ == "__main__":
    print("🚀 测试最终版本的06文档代码")
    print("=" * 60)
    demo_resumable_workflow()