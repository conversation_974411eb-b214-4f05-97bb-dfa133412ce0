#!/usr/bin/env python3
"""
分析GLM-4.5超时问题
测试不同的超时设置和重试机制
"""
import os
import time
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_community.chat_models import ChatZhipuAI

def test_timeout_settings():
    """测试不同的超时设置"""
    print("⏱️ 测试超时设置")
    print("=" * 40)
    
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ 未找到API密钥")
        return
    
    timeout_values = [10, 20, 30, 60]
    test_prompt = "请简单介绍一下Python的优势"
    
    for timeout in timeout_values:
        print(f"\n🧪 测试超时时间: {timeout}秒")
        start_time = time.time()
        
        try:
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.3,
                api_key=api_key,
                timeout=timeout
            )
            
            response = llm.invoke([HumanMessage(content=test_prompt)])
            elapsed = time.time() - start_time
            
            print(f"✅ 成功！用时: {elapsed:.2f}秒")
            print(f"📝 回复长度: {len(response.content)}字符")
            
            # 找到合适的超时时间就停止
            if elapsed < timeout * 0.8:  # 如果实际用时不到超时时间的80%
                print(f"💡 建议超时时间: {int(elapsed * 2)}秒")
                break
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 失败！用时: {elapsed:.2f}秒, 错误: {str(e)[:100]}...")

def test_retry_mechanism():
    """测试重试机制"""
    print("\n🔄 测试重试机制")
    print("=" * 40)
    
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ 未找到API密钥")
        return
    
    def call_with_retry(prompt, max_retries=3, timeout=30):
        """带重试的GLM调用"""
        for attempt in range(max_retries):
            print(f"🔄 第{attempt + 1}次尝试...")
            try:
                llm = ChatZhipuAI(
                    model="glm-4.5",
                    temperature=0.3,
                    api_key=api_key,
                    timeout=timeout
                )
                
                start_time = time.time()
                response = llm.invoke([HumanMessage(content=prompt)])
                elapsed = time.time() - start_time
                
                print(f"✅ 成功！用时: {elapsed:.2f}秒")
                return response.content
                
            except Exception as e:
                print(f"⚠️ 第{attempt + 1}次失败: {str(e)[:50]}...")
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    print(f"⏳ 等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    print("❌ 所有重试都失败了")
                    return None
    
    # 测试重试机制
    test_prompts = [
        "我是编程新手，如何开始学习Python？",
        "Python中列表和元组的区别？",
        "什么是面向对象编程？"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n📝 测试问题{i}: {prompt}")
        result = call_with_retry(prompt)
        if result:
            print(f"✅ 获得回复: {result[:100]}...")
        else:
            print("❌ 最终失败")

def test_network_stability():
    """测试网络稳定性"""
    print("\n🌐 测试网络稳定性")
    print("=" * 40)
    
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if not api_key:
        print("❌ 未找到API密钥")
        return
    
    # 连续快速调用，看是否有限流
    success_count = 0
    total_calls = 5
    
    for i in range(total_calls):
        print(f"\n🚀 第{i+1}次快速调用...")
        try:
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.3,
                api_key=api_key,
                timeout=25  # 适中的超时时间
            )
            
            start_time = time.time()
            response = llm.invoke([HumanMessage(content=f"这是第{i+1}个测试问题，请简单回复")])
            elapsed = time.time() - start_time
            
            print(f"✅ 成功！用时: {elapsed:.2f}秒")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 失败: {str(e)[:50]}...")
        
        # 短暂间隔，避免过于频繁
        if i < total_calls - 1:
            time.sleep(1)
    
    print(f"\n📊 成功率: {success_count}/{total_calls} ({success_count/total_calls*100:.1f}%)")
    
    if success_count / total_calls < 0.8:
        print("💡 建议：增加重试机制和更长的超时时间")
    else:
        print("✅ 网络连接稳定")

if __name__ == "__main__":
    test_timeout_settings()
    test_retry_mechanism() 
    test_network_stability()