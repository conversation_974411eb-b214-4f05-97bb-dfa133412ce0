"""
测试第一章《认识LangGraph》中的示例代码
验证GLM-4.5版本的代码是否正常工作
"""

import os
import pytest
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent


def get_weather(city: str) -> str:
    """获取指定城市的天气信息"""
    return f"{city}总是阳光明媚！"


class TestChapter1Examples:
    """第一章示例代码测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.api_key = "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
        
        # 设置环境变量
        os.environ["OPENAI_API_KEY"] = self.api_key
        os.environ["OPENAI_BASE_URL"] = self.base_url
    
    def test_weather_tool_function(self):
        """测试天气工具函数"""
        result = get_weather("旧金山")
        assert result == "旧金山总是阳光明媚！"
        
        result = get_weather("北京")
        assert result == "北京总是阳光明媚！"
    
    @pytest.mark.integration
    def test_book_example_complete_flow(self):
        """测试书中完整示例流程"""
        # 配置智谱AI GLM-4.5模型（书中代码）
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,  # 在实际使用中应该是 "your_zhipu_api_key"
            base_url="https://open.bigmodel.cn/api/paas/v4/",
            temperature=0.7
        )
        
        # 创建智能体（书中代码）
        agent = create_react_agent(
            model=model,
            tools=[get_weather],
            prompt="你是一个有用的助手"
        )
        
        # 运行智能体（书中代码）
        result = agent.invoke({
            "messages": [{"role": "user", "content": "旧金山的天气怎么样？"}]
        })
        
        # 验证结果结构
        assert "messages" in result
        assert len(result["messages"]) > 0
        
        # 验证对话流程
        messages = result["messages"]
        
        # 第1条：用户消息
        user_message = messages[0]
        assert "旧金山的天气怎么样？" in user_message.content
        
        # 应该包含工具调用
        tool_call_found = False
        tool_response_found = False
        final_response = None
        
        for msg in messages:
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                tool_call_found = True
                # 验证工具调用参数
                tool_call = msg.tool_calls[0]
                assert tool_call['name'] == 'get_weather'
                assert '旧金山' in str(tool_call['args'])
            
            if hasattr(msg, 'tool_call_id'):
                tool_response_found = True
                assert "旧金山总是阳光明媚！" in msg.content
            
            # 记录最终回复（最后一个AI消息且没有工具调用）
            if (msg == messages[-1] and 
                hasattr(msg, 'content') and 
                not hasattr(msg, 'tool_call_id') and 
                not (hasattr(msg, 'tool_calls') and msg.tool_calls)):
                final_response = msg.content
        
        # 验证工具调用流程
        assert tool_call_found, "应该发现智能体调用了工具"
        assert tool_response_found, "应该发现工具响应"
        assert final_response, "应该有最终回复"
        assert "旧金山" in final_response or "阳光明媚" in final_response
        
        print(f"\\n📊 完整对话流程验证:")
        print(f"1. 用户输入: {messages[0].content}")
        print(f"2. 工具调用: {'✅' if tool_call_found else '❌'}")
        print(f"3. 工具响应: {'✅' if tool_response_found else '❌'}")
        print(f"4. 最终回复: {final_response}")
    
    @pytest.mark.integration 
    def test_different_cities(self):
        """测试不同城市的查询"""
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=0.7
        )
        
        agent = create_react_agent(
            model=model,
            tools=[get_weather],
            prompt="你是一个有用的助手"
        )
        
        # 测试不同城市
        cities = ["北京", "上海", "深圳"]
        
        for city in cities:
            result = agent.invoke({
                "messages": [{"role": "user", "content": f"{city}的天气如何？"}]
            })
            
            # 验证最终回复包含城市信息
            final_content = result["messages"][-1].content
            assert city in final_content or "阳光明媚" in final_content
            print(f"✅ {city}: {final_content}")
    
    def teardown_method(self):
        """测试后清理"""
        if "OPENAI_API_KEY" in os.environ:
            del os.environ["OPENAI_API_KEY"]
        if "OPENAI_BASE_URL" in os.environ:
            del os.environ["OPENAI_BASE_URL"]


# 独立测试函数
def test_weather_function_standalone():
    """独立测试天气函数"""
    assert get_weather("测试城市") == "测试城市总是阳光明媚！"
    assert get_weather("") == "总是阳光明媚！"
    assert get_weather("New York") == "New York总是阳光明媚！"