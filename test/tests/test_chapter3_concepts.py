"""
验证第03章核心概念的代码示例
测试状态、节点、边、持久化等核心功能
使用GLM-4.5模型和Tavily搜索引擎
"""

import os
import pytest
from typing import TypedDict, Annotated, Optional
from operator import add
from tavily import TavilyClient
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, AnyMessage
from langgraph.graph.message import add_messages
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
# from langgraph_checkpoint_sqlite import SqliteSaver  # 暂时注释掉


# ============== 3.1 状态定义 ==============

class ChatState(TypedDict):
    """聊天状态定义 - 对应3.1节内容"""
    # 必需字段
    messages: Annotated[list[AnyMessage], add_messages]
    
    # 可选字段
    user_name: Optional[str]
    session_id: Optional[str]
    
    # 业务字段
    current_topic: str
    confidence_score: float
    search_results: Optional[str]
    intent: Optional[str]
    context: str
    step_count: int


class State(TypedDict):
    """基础状态定义 - 演示不同reducer类型"""
    # 默认 reducer：覆盖更新
    current_user: str  # 新值直接替换旧值
    
    # add reducer：累加更新
    scores: Annotated[list[int], add]  # 新列表与旧列表合并
    
    # add_messages reducer：智能消息管理
    messages: Annotated[list[AnyMessage], add_messages]  # 智能处理消息更新
    
    # 自定义 reducer
    metadata: Annotated[dict, lambda old, new: {**old, **new}]  # 字典合并


# ============== 3.2 节点实现 ==============

def create_llm_node():
    """创建LLM调用节点 - 使用GLM-4.5"""
    def llm_node(state: ChatState):
        """调用大语言模型生成回复"""
        llm = ChatOpenAI(
            model="glm-4.5",
            api_key="4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW",
            base_url="https://open.bigmodel.cn/api/paas/v4/",
            temperature=0.1
        )
        
        # 构造 prompt
        system_message = SystemMessage(content="你是一个有用的助手")
        messages = [system_message] + state["messages"]
        
        # 调用 LLM
        response = llm.invoke(messages)
        return {"messages": [response]}
    
    return llm_node


def create_search_node():
    """创建搜索节点 - 使用Tavily搜索引擎"""
    def search_node(state: ChatState):
        """搜索相关信息"""
        try:
            tavily_client = TavilyClient(api_key="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")
            
            # 从最后一条消息中提取搜索查询
            last_message = state["messages"][-1].content
            query = extract_search_query(last_message)
            
            if query:
                response = tavily_client.search(
                    query=query,
                    search_depth="basic",
                    max_results=3,
                    include_answer=True
                )
                
                if response.get("answer"):
                    results = f"搜索摘要: {response['answer']}"
                elif response.get("results"):
                    results_list = response["results"][:2]
                    results = "搜索结果:\n"
                    for i, result in enumerate(results_list, 1):
                        title = result.get("title", "无标题")
                        content = result.get("content", "无内容")[:200]
                        results += f"{i}. {title}: {content}...\n"
                else:
                    results = "未找到相关信息"
                
                return {
                    "search_results": results,
                    "messages": [AIMessage(content=f"我找到了相关信息：{results[:200]}...")]
                }
            
            return {"messages": [AIMessage(content="抱歉，我没有理解你的搜索需求")]}
            
        except Exception as e:
            return {"messages": [AIMessage(content=f"搜索出错: {str(e)}")]}
    
    return search_node


def extract_search_query(message: str) -> str:
    """从消息中提取搜索查询"""
    # 简单实现：寻找关键词
    search_keywords = ["搜索", "查询", "找", "天气", "新闻", "信息"]
    for keyword in search_keywords:
        if keyword in message:
            return message
    return ""


# ============== 3.3 和 3.4 边和路由实现 ==============

def create_routing_function():
    """创建路由函数 - 对应3.3和3.4节内容"""
    def routing_function(state: ChatState) -> str:
        """根据状态决定下一个节点"""
        last_message = state["messages"][-1].content.lower()
        
        # 检查是否需要搜索
        search_keywords = ["搜索", "查询", "找", "天气", "新闻", "最新"]
        need_search = any(keyword in last_message for keyword in search_keywords)
        
        if need_search:
            return "search_node"
        else:
            return "llm_node"
    
    return routing_function


def confidence_based_routing(state: ChatState) -> str:
    """基于置信度的智能路由 - 3.4节高级路由策略"""
    confidence = state.get("confidence_score", 0.0)
    intent = state.get("intent", "unknown")
    
    # 高置信度：直接处理
    if confidence > 0.8:
        return f"{intent}_handler" if intent != "unknown" else "llm_node"
    # 中等置信度：需要确认
    elif confidence > 0.5:
        return "confirmation_node"
    # 低置信度：需要澄清
    else:
        return "clarification_node"


# ============== 3.5 持久化实现 ==============

def create_memory_chatbot():
    """创建带内存的聊天机器人 - 3.5节内容"""
    # 创建状态图
    graph = StateGraph(ChatState)
    
    # 添加节点
    graph.add_node("llm_node", create_llm_node())
    graph.add_node("search_node", create_search_node())
    
    # 添加边 - 使用条件边直接从START开始
    graph.add_conditional_edges(
        START,
        create_routing_function(),
        {
            "llm_node": "llm_node",
            "search_node": "search_node"
        }
    )
    graph.add_edge("llm_node", END)
    graph.add_edge("search_node", END)
    
    # 创建内存检查点保存器
    memory = MemorySaver()
    
    # 编译图时指定检查点保存器
    app = graph.compile(checkpointer=memory)
    return app


def create_persistent_chatbot():
    """创建持久化聊天机器人 - 使用内存存储（简化版本）"""
    # 创建状态图
    graph = StateGraph(ChatState)
    
    # 添加节点
    graph.add_node("llm_node", create_llm_node())
    graph.add_node("search_node", create_search_node())
    
    # 添加边 - 使用条件边直接从START开始
    graph.add_conditional_edges(
        START,
        create_routing_function(),
        {
            "llm_node": "llm_node",
            "search_node": "search_node"
        }
    )
    graph.add_edge("llm_node", END)
    graph.add_edge("search_node", END)
    
    # 使用内存存储替代SQLite
    memory = MemorySaver()
    app = graph.compile(checkpointer=memory)
    return app


# ============== 测试类 ==============

class TestChapter3Concepts:
    """第03章核心概念测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 设置环境变量
        os.environ["OPENAI_API_KEY"] = "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW"
        os.environ["OPENAI_BASE_URL"] = "https://open.bigmodel.cn/api/paas/v4/"
    
    def test_state_lifecycle(self):
        """测试状态生命周期 - 3.1节"""
        # 初始状态
        initial_state: ChatState = {
            "messages": [],
            "user_name": None,
            "session_id": None,
            "current_topic": "",
            "confidence_score": 0.0,
            "search_results": None,
            "intent": None,
            "context": "",
            "step_count": 0
        }
        
        # 用户发送第一条消息后
        after_user_input: ChatState = {
            **initial_state,
            "messages": [HumanMessage(content="你好，我想了解天气")],
            "step_count": 1
        }
        
        # 验证状态演变
        assert len(after_user_input["messages"]) == 1
        assert after_user_input["step_count"] == 1
        assert isinstance(after_user_input["messages"][0], HumanMessage)
    
    @pytest.mark.integration
    def test_llm_node(self):
        """测试LLM节点 - 3.2节"""
        llm_node = create_llm_node()
        
        test_state: ChatState = {
            "messages": [HumanMessage(content="你好")],
            "user_name": None,
            "session_id": None,
            "current_topic": "",
            "confidence_score": 0.0,
            "search_results": None,
            "intent": None,
            "context": "",
            "step_count": 1
        }
        
        result = llm_node(test_state)
        
        # 验证返回结果
        assert "messages" in result
        assert len(result["messages"]) == 1
        assert isinstance(result["messages"][0], AIMessage)
        assert len(result["messages"][0].content) > 0
        
        print(f"LLM回复: {result['messages'][0].content}")
    
    @pytest.mark.integration
    def test_search_node(self):
        """测试搜索节点 - 3.2节"""
        search_node = create_search_node()
        
        test_state: ChatState = {
            "messages": [HumanMessage(content="搜索北京天气")],
            "user_name": None,
            "session_id": None,
            "current_topic": "",
            "confidence_score": 0.0,
            "search_results": None,
            "intent": None,
            "context": "",
            "step_count": 1
        }
        
        result = search_node(test_state)
        
        # 验证返回结果
        assert "messages" in result
        assert len(result["messages"]) == 1
        assert isinstance(result["messages"][0], AIMessage)
        
        print(f"搜索结果: {result['messages'][0].content}")
    
    def test_routing_function(self):
        """测试路由函数 - 3.3和3.4节"""
        routing_func = create_routing_function()
        
        # 测试搜索路由
        search_state: ChatState = {
            "messages": [HumanMessage(content="搜索北京天气")],
            "user_name": None,
            "session_id": None,
            "current_topic": "",
            "confidence_score": 0.0,
            "search_results": None,
            "intent": None,
            "context": "",
            "step_count": 1
        }
        
        route = routing_func(search_state)
        assert route == "search_node"
        
        # 测试LLM路由
        chat_state: ChatState = {
            "messages": [HumanMessage(content="你好，最近怎么样？")],
            "user_name": None,
            "session_id": None,
            "current_topic": "",
            "confidence_score": 0.0,
            "search_results": None,
            "intent": None,
            "context": "",
            "step_count": 1
        }
        
        route = routing_func(chat_state)
        assert route == "llm_node"
    
    @pytest.mark.integration
    def test_memory_persistence(self):
        """测试内存持久化 - 3.5节"""
        app = create_memory_chatbot()
        
        # 使用线程ID来区分不同的对话
        config = {"configurable": {"thread_id": "test_conversation"}}
        
        # 第一次对话
        result1 = app.invoke({
            "messages": [HumanMessage("你好")],
            "user_name": None,
            "session_id": None,
            "current_topic": "",
            "confidence_score": 0.0,
            "search_results": None,
            "intent": None,
            "context": "",
            "step_count": 0
        }, config=config)
        
        # 第二次对话 - 应该能记住之前的内容
        result2 = app.invoke({
            "messages": [HumanMessage("我刚才说了什么？")],
            "user_name": None,
            "session_id": None,
            "current_topic": "",
            "confidence_score": 0.0,
            "search_results": None,
            "intent": None,
            "context": "",
            "step_count": 0
        }, config=config)
        
        # 验证持久化效果
        assert len(result2["messages"]) >= 2  # 至少包含之前的对话
        
        print(f"第一次对话: {result1['messages'][-1].content}")
        print(f"第二次对话: {result2['messages'][-1].content}")
        print(f"总消息数: {len(result2['messages'])}")
    
    @pytest.mark.integration
    def test_full_workflow(self):
        """测试完整工作流程"""
        app = create_persistent_chatbot()
        config = {"configurable": {"thread_id": "full_test"}}
        
        # 测试普通对话
        result1 = app.invoke({
            "messages": [HumanMessage("你好，我是张三")],
            "user_name": None,
            "session_id": None,
            "current_topic": "",
            "confidence_score": 0.0,
            "search_results": None,
            "intent": None,
            "context": "",
            "step_count": 0
        }, config=config)
        
        # 测试搜索功能
        result2 = app.invoke({
            "messages": [HumanMessage("帮我搜索Python教程")],
            "user_name": None,
            "session_id": None,
            "current_topic": "",
            "confidence_score": 0.0,
            "search_results": None,
            "intent": None,
            "context": "",
            "step_count": 0
        }, config=config)
        
        # 验证结果
        assert len(result1["messages"]) > 0
        assert len(result2["messages"]) > 0
        
        print("=== 完整工作流程测试 ===")
        print(f"对话回复: {result1['messages'][-1].content}")
        print(f"搜索回复: {result2['messages'][-1].content}")


if __name__ == "__main__":
    # 运行简单测试
    test = TestChapter3Concepts()
    test.setup_method()
    
    print("测试状态生命周期...")
    test.test_state_lifecycle()
    print("✓ 状态生命周期测试通过")
    
    print("\n测试路由函数...")
    test.test_routing_function()
    print("✓ 路由函数测试通过")
    
    print("\n所有核心概念测试完成！")