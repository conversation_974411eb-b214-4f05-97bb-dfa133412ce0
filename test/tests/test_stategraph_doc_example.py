"""
测试更新后的StateGraph文档示例
验证LangGraph核心概念：StateGraph、状态管理、持久化等
"""

import os
import pytest
from typing import TypedDict
from langchain_openai import ChatOpenAI
from tavily import TavilyClient
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver


# 定义状态结构（与文档一致）
class AgentState(TypedDict):
    query: str
    search_results: str
    final_answer: str


def tavily_search(query: str) -> str:
    """使用Tavily搜索引擎获取实时信息（文档示例代码）"""
    tavily_client = TavilyClient(api_key="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")
    response = tavily_client.search(query=query, max_results=3, include_answer=True)
    return response.get("answer", "未找到相关信息")


class TestStateGraphDocExample:
    """测试StateGraph文档示例"""
    
    def setup_method(self):
        """测试前设置"""
        self.api_key = "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
        
        os.environ["OPENAI_API_KEY"] = self.api_key
        os.environ["OPENAI_BASE_URL"] = self.base_url
        
        # 配置GLM-4.5模型（文档示例代码）
        self.model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=0.3
        )
    
    def create_search_node(self):
        """创建搜索节点（文档示例代码）"""
        def search_node(state: AgentState) -> AgentState:
            """搜索节点：获取实时信息"""
            query = state["query"]
            results = tavily_search(query)
            return {"search_results": results}
        return search_node
    
    def create_answer_node(self):
        """创建回答节点（文档示例代码）"""
        def answer_node(state: AgentState) -> AgentState:
            """回答节点：基于搜索结果生成回答"""
            context = state["search_results"]
            query = state["query"]
            
            prompt = f"""基于以下搜索结果回答用户问题：

搜索结果：{context}

用户问题：{query}

请提供准确、有用的回答："""
            
            response = self.model.invoke(prompt)
            return {"final_answer": response.content}
        return answer_node
    
    def test_state_structure(self):
        """测试状态结构定义"""
        # 验证AgentState类型
        test_state = AgentState(
            query="测试查询",
            search_results="测试搜索结果",
            final_answer="测试回答"
        )
        
        assert "query" in test_state
        assert "search_results" in test_state
        assert "final_answer" in test_state
        assert test_state["query"] == "测试查询"
    
    def test_search_node_function(self):
        """测试搜索节点功能"""
        search_node = self.create_search_node()
        
        # 测试搜索节点
        input_state = AgentState(
            query="北京天气",
            search_results="",
            final_answer=""
        )
        
        result = search_node(input_state)
        
        # 验证返回结果
        assert "search_results" in result
        assert isinstance(result["search_results"], str)
        assert len(result["search_results"]) > 0
        
        print(f"搜索节点测试结果: {result['search_results'][:50]}...")
    
    def test_answer_node_function(self):
        """测试回答节点功能"""
        answer_node = self.create_answer_node()
        
        # 模拟有搜索结果的状态
        input_state = AgentState(
            query="北京天气如何？",
            search_results="北京今天晴朗，温度20-30度，微风",
            final_answer=""
        )
        
        result = answer_node(input_state)
        
        # 验证返回结果
        assert "final_answer" in result
        assert isinstance(result["final_answer"], str)
        assert len(result["final_answer"]) > 0
        
        print(f"回答节点测试结果: {result['final_answer'][:50]}...")
    
    @pytest.mark.integration
    def test_complete_stategraph_workflow(self):
        """测试完整的StateGraph工作流程（文档示例代码）"""
        # 创建节点
        search_node = self.create_search_node()
        answer_node = self.create_answer_node()
        
        # 构建状态图（文档示例代码）
        workflow = StateGraph(AgentState)
        
        # 添加节点
        workflow.add_node("search", search_node)
        workflow.add_node("answer", answer_node)
        
        # 定义工作流程
        workflow.set_entry_point("search")
        workflow.add_edge("search", "answer")
        workflow.add_edge("answer", END)
        
        # 编译工作流
        app = workflow.compile()
        
        # 运行工作流
        result = app.invoke({"query": "今天北京的天气怎么样？"})
        
        # 验证结果结构
        assert isinstance(result, dict)
        assert "query" in result
        assert "search_results" in result
        assert "final_answer" in result
        
        # 验证内容
        assert result["query"] == "今天北京的天气怎么样？"
        assert len(result["search_results"]) > 0
        assert len(result["final_answer"]) > 0
        
        print(f"\\n🎯 StateGraph工作流验证成功:")
        print(f"1. 输入查询: {result['query']}")
        print(f"2. 搜索结果: {result['search_results'][:50]}...")
        print(f"3. 最终回答: {result['final_answer'][:100]}...")
    
    @pytest.mark.integration 
    def test_persistent_stategraph_workflow(self):
        """测试带持久化的StateGraph工作流程（文档示例代码）"""
        # 创建节点
        search_node = self.create_search_node()
        answer_node = self.create_answer_node()
        
        # 构建状态图
        workflow = StateGraph(AgentState)
        workflow.add_node("search", search_node)
        workflow.add_node("answer", answer_node)
        workflow.set_entry_point("search")
        workflow.add_edge("search", "answer")
        workflow.add_edge("answer", END)
        
        # 添加持久化功能（文档示例代码）
        memory = MemorySaver()
        app = workflow.compile(checkpointer=memory)
        
        # 配置对话线程
        config = {"configurable": {"thread_id": "test_conversation"}}
        
        # 运行工作流
        result = app.invoke(
            {"query": "上海今天的天气怎么样？"}, 
            config=config
        )
        
        # 验证持久化功能
        assert isinstance(result, dict)
        assert "final_answer" in result
        assert len(result["final_answer"]) > 0
        
        # 验证可以继续对话（持久化状态）
        second_result = app.invoke(
            {"query": "明天呢？"},
            config=config
        )
        
        assert isinstance(second_result, dict)
        assert "final_answer" in second_result
        
        print(f"\\n💾 持久化功能验证:")
        print(f"第一次查询结果: {result['final_answer'][:50]}...")
        print(f"第二次查询结果: {second_result['final_answer'][:50]}...")
    
    def test_workflow_structure(self):
        """测试工作流结构"""
        workflow = StateGraph(AgentState)
        search_node = self.create_search_node()
        answer_node = self.create_answer_node()
        
        # 添加节点和边
        workflow.add_node("search", search_node)
        workflow.add_node("answer", answer_node)
        workflow.set_entry_point("search")
        workflow.add_edge("search", "answer")
        workflow.add_edge("answer", END)
        
        # 编译并验证
        app = workflow.compile()
        assert app is not None
        
        # 验证工作流可以处理状态
        test_state = {"query": "测试查询"}
        result = app.invoke(test_state)
        assert isinstance(result, dict)
    
    def teardown_method(self):
        """测试后清理"""
        if "OPENAI_API_KEY" in os.environ:
            del os.environ["OPENAI_API_KEY"]
        if "OPENAI_BASE_URL" in os.environ:
            del os.environ["OPENAI_BASE_URL"]