"""
测试LangGraph智能体功能
使用智谱AI的glm-4.5模型
"""

import os
import pytest
from unittest.mock import patch, MagicMock
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent


def get_weather(city: str) -> str:
    """获取指定城市的天气信息"""
    return f"{city}总是阳光明媚！"


class TestLangGraphAgent:
    """LangGraph智能体测试类"""
    
    def setup_method(self):
        """每个测试方法执行前的设置"""
        # 配置智谱AI
        self.api_key = "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
        
        # 设置环境变量
        os.environ["OPENAI_API_KEY"] = self.api_key
        os.environ["OPENAI_BASE_URL"] = self.base_url
    
    def test_weather_tool_function(self):
        """测试天气工具函数"""
        city = "北京"
        result = get_weather(city)
        assert result == "北京总是阳光明媚！"
        
        city = "上海"
        result = get_weather(city)
        assert result == "上海总是阳光明媚！"
    
    def test_create_agent_basic(self):
        """测试创建智能体的基本功能"""
        # 创建模型实例
        model = ChatOpenAI(
            model="glm-4.5",  # 智谱AI最新的GLM-4.5模型
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=0.7
        )
        
        # 创建智能体
        agent = create_react_agent(
            model=model,
            tools=[get_weather],
        )
        
        # 验证智能体创建成功
        assert agent is not None
        assert hasattr(agent, 'invoke')
    
    def test_agent_mock_response(self):
        """使用mock测试智能体响应（避免实际API调用）"""
        # 创建mock模型实例
        mock_model = MagicMock()
        mock_model.invoke.return_value = MagicMock(content="测试回复")
        
        # 创建智能体
        agent = create_react_agent(
            model=mock_model,
            tools=[get_weather],
        )
        
        # 验证智能体创建成功
        assert agent is not None
        assert hasattr(agent, 'invoke')
        
        # 可以进一步验证mock模型被正确配置
        assert mock_model.invoke is not None
    
    def test_weather_tool_with_different_cities(self):
        """测试天气工具对不同城市的响应"""
        cities = ["北京", "上海", "广州", "深圳", "杭州"]
        
        for city in cities:
            result = get_weather(city)
            expected = f"{city}总是阳光明媚！"
            assert result == expected
            assert city in result
            assert "阳光明媚" in result
    
    def test_weather_tool_empty_input(self):
        """测试天气工具的边界情况"""
        # 空字符串
        result = get_weather("")
        assert result == "总是阳光明媚！"
        
        # 包含空格的城市名
        result = get_weather("新 北 京")
        assert result == "新 北 京总是阳光明媚！"
    
    @pytest.mark.integration
    def test_agent_real_api_call(self):
        """集成测试：真实API调用（需要有效的API key）"""
        try:
            # 创建模型实例
            model = ChatOpenAI(
                model="glm-4.5",
                api_key=self.api_key,
                base_url=self.base_url,
                temperature=0.1,
                timeout=30
            )
            
            # 创建智能体
            agent = create_react_agent(
                model=model,
                tools=[get_weather],
            )
            
            # 测试简单问题
            result = agent.invoke({
                "messages": [{"role": "user", "content": "旧金山的天气怎么样？"}]
            })
            
            # 验证响应结构
            assert "messages" in result
            assert len(result["messages"]) > 0
            
            # 验证最后一条消息包含回复
            last_message = result["messages"][-1]
            assert hasattr(last_message, 'content')
            assert len(last_message.content) > 0
            
            print(f"智能体回复: {last_message.content}")
            
        except Exception as e:
            pytest.skip(f"API调用失败，可能是网络或API key问题: {e}")
    
    @pytest.mark.slow
    def test_agent_multiple_tool_calls(self):
        """测试智能体多次工具调用（标记为慢速测试）"""
        try:
            model = ChatOpenAI(
                model="glm-4.5",
                api_key=self.api_key,
                base_url=self.base_url,
                temperature=0.1
            )
            
            agent = create_react_agent(
                model=model,
                tools=[get_weather],
            )
            
            # 测试需要多次工具调用的问题
            result = agent.invoke({
                "messages": [{"role": "user", "content": "比较一下北京和上海的天气"}]
            })
            
            # 验证结果
            assert "messages" in result
            content = result["messages"][-1].content
            
            # 应该包含两个城市的信息
            assert "北京" in content or "beijing" in content.lower()
            assert "上海" in content or "shanghai" in content.lower()
            
        except Exception as e:
            pytest.skip(f"API调用失败: {e}")
    
    def teardown_method(self):
        """每个测试方法执行后的清理"""
        # 清理环境变量
        if "OPENAI_API_KEY" in os.environ:
            del os.environ["OPENAI_API_KEY"]
        if "OPENAI_BASE_URL" in os.environ:
            del os.environ["OPENAI_BASE_URL"]


def test_standalone_weather_function():
    """独立的天气函数测试"""
    assert get_weather("测试城市") == "测试城市总是阳光明媚！"
    
    # 测试包含特殊字符的城市名
    assert get_weather("Saint-Petersburg") == "Saint-Petersburg总是阳光明媚！"
    assert get_weather("纽约(New York)") == "纽约(New York)总是阳光明媚！"