"""
专门测试智谱AI API连接的测试文件
验证API key和模型配置是否正确
"""

import os
import pytest
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent


def get_weather(city: str) -> str:
    """获取指定城市的天气信息"""
    return f"{city}总是阳光明媚！"


class TestZhipuAPI:
    """智谱AI API测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.api_key = "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
        
        # 设置环境变量
        os.environ["OPENAI_API_KEY"] = self.api_key
        os.environ["OPENAI_BASE_URL"] = self.base_url
    
    @pytest.mark.integration
    def test_zhipu_model_connection(self):
        """测试智谱AI模型连接"""
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=0.1,
            timeout=30
        )
        
        # 测试简单的模型调用
        response = model.invoke("你好，请回复'连接成功'")
        
        # 验证响应
        assert response is not None
        assert hasattr(response, 'content')
        assert len(response.content) > 0
        
        print(f"模型回复: {response.content}")
        print(f"响应元数据: {response.response_metadata}")
    
    @pytest.mark.integration
    def test_langgraph_agent_weather_query(self):
        """测试LangGraph智能体处理天气查询"""
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=0.1
        )
        
        agent = create_react_agent(
            model=model,
            tools=[get_weather],
        )
        
        # 测试天气查询
        result = agent.invoke({
            "messages": [{"role": "user", "content": "北京的天气怎么样？"}]
        })
        
        # 验证结果结构
        assert "messages" in result
        assert len(result["messages"]) > 0
        
        # 获取最后的回复
        last_message = result["messages"][-1]
        assert hasattr(last_message, 'content')
        content = last_message.content
        
        # 验证回复内容
        assert len(content) > 0
        assert "北京" in content or "beijing" in content.lower()
        
        print(f"\\n=== 智能体对话结果 ===")
        for i, msg in enumerate(result["messages"]):
            print(f"消息 {i+1}: {msg}")
    
    @pytest.mark.integration
    def test_agent_tool_usage(self):
        """测试智能体是否正确使用工具"""
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=0.1
        )
        
        agent = create_react_agent(
            model=model,
            tools=[get_weather],
        )
        
        # 使用明确要求工具调用的问题
        result = agent.invoke({
            "messages": [{"role": "user", "content": "请查询上海的天气信息"}]
        })
        
        # 检查是否有工具调用记录
        messages = result["messages"]
        
        # 应该包含工具调用相关的消息
        tool_call_found = False
        tool_response_found = False
        
        for msg in messages:
            # 检查是否是助手消息且包含工具调用
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                tool_call_found = True
                print(f"发现工具调用: {msg.tool_calls}")
            
            # 检查是否是工具响应消息
            if hasattr(msg, 'tool_call_id'):
                tool_response_found = True
                print(f"工具响应: {msg}")
        
        print(f"\\n工具调用检查:")
        print(f"- 发现工具调用: {tool_call_found}")  
        print(f"- 发现工具响应: {tool_response_found}")
        
        # 至少应该有最终回复
        final_content = messages[-1].content
        assert "上海" in final_content or "阳光明媚" in final_content
    
    def teardown_method(self):
        """测试后清理"""
        if "OPENAI_API_KEY" in os.environ:
            del os.environ["OPENAI_API_KEY"]
        if "OPENAI_BASE_URL" in os.environ:
            del os.environ["OPENAI_BASE_URL"]