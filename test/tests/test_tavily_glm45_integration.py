"""
测试GLM-4.5与Tavily搜索引擎的集成
验证智能体的实时信息搜索能力
"""

import os
import pytest
from tavily import TavilyClient
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent


def tavily_search(query: str) -> str:
    """使用Tavily搜索引擎获取实时信息"""
    try:
        tavily_client = TavilyClient(api_key="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")
        
        response = tavily_client.search(
            query=query,
            search_depth="basic",
            max_results=3,
            include_answer=True,
            include_raw_content=False
        )
        
        if response.get("answer"):
            return f"搜索结果摘要: {response['answer']}"
        elif response.get("results"):
            results = response["results"][:2]
            summary = "搜索结果:\n"
            for i, result in enumerate(results, 1):
                title = result.get("title", "无标题")
                content = result.get("content", "无内容")[:200]
                summary += f"{i}. {title}: {content}...\n"
            return summary
        else:
            return "抱歉，没有找到相关信息。"
            
    except Exception as e:
        return f"搜索出错: {str(e)}"


class TestTavilyGLM45Integration:
    """GLM-4.5与Tavily搜索引擎集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.api_key = "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
        self.tavily_key = "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"
        
        os.environ["OPENAI_API_KEY"] = self.api_key
        os.environ["OPENAI_BASE_URL"] = self.base_url
    
    def test_tavily_search_function(self):
        """测试Tavily搜索函数基本功能"""
        result = tavily_search("北京天气")
        
        # 验证返回结果
        assert isinstance(result, str)
        assert len(result) > 0
        assert "搜索" in result or "error" in result.lower()
        
        print(f"搜索结果示例: {result[:100]}...")
    
    @pytest.mark.integration
    def test_glm45_tavily_agent_basic(self):
        """测试GLM-4.5与Tavily的基本集成"""
        # 创建GLM-4.5模型
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=0.3
        )
        
        # 创建智能体
        agent = create_react_agent(
            model=model,
            tools=[tavily_search],
            prompt="你是一个有用的助手，可以使用搜索工具获取最新信息。"
        )
        
        # 测试简单查询
        result = agent.invoke({
            "messages": [{"role": "user", "content": "今天北京的天气如何？"}]
        })
        
        # 验证结果结构
        assert "messages" in result
        assert len(result["messages"]) > 0
        
        # 验证对话流程
        messages = result["messages"]
        search_call_found = False
        search_response_found = False
        final_response = None
        
        for msg in messages:
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                search_call_found = True
                tool_call = msg.tool_calls[0]
                assert tool_call['name'] == 'tavily_search'
                assert '北京' in str(tool_call['args']) or '天气' in str(tool_call['args'])
            
            if hasattr(msg, 'tool_call_id'):
                search_response_found = True
                assert len(msg.content) > 0
            
            if (msg == messages[-1] and 
                hasattr(msg, 'content') and 
                not hasattr(msg, 'tool_call_id') and 
                not (hasattr(msg, 'tool_calls') and msg.tool_calls)):
                final_response = msg.content
        
        # 验证搜索流程
        assert search_call_found, "应该发现智能体调用了搜索工具"
        assert search_response_found, "应该发现搜索响应"
        assert final_response, "应该有最终回复"
        assert len(final_response) > 50, "最终回复应该有足够的内容"
        
        print(f"\\n🔍 搜索流程验证:")
        print(f"1. 搜索调用: {'✅' if search_call_found else '❌'}")
        print(f"2. 搜索响应: {'✅' if search_response_found else '❌'}")
        print(f"3. 最终回复长度: {len(final_response)} 字符")
        print(f"4. 回复预览: {final_response[:100]}...")
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_multiple_search_queries(self):
        """测试多种类型的搜索查询"""
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=0.3
        )
        
        agent = create_react_agent(
            model=model,
            tools=[tavily_search],
            prompt="你是一个有用的助手，可以使用搜索工具获取最新信息。"
        )
        
        test_queries = [
            "最新的AI技术发展",
            "今天的新闻热点",
            "北京今天的天气"
        ]
        
        for query in test_queries:
            print(f"\\n🧪 测试查询: {query}")
            
            result = agent.invoke({
                "messages": [{"role": "user", "content": query}]
            })
            
            # 验证有实际的回复
            final_message = result["messages"][-1]
            assert hasattr(final_message, 'content')
            assert len(final_message.content) > 30
            
            # 检查是否包含相关关键词
            content = final_message.content.lower()
            
            if "ai" in query.lower():
                assert any(keyword in content for keyword in ["ai", "人工智能", "技术", "发展"])
            elif "新闻" in query:
                assert any(keyword in content for keyword in ["新闻", "消息", "事件", "热点"])
            elif "天气" in query:
                assert any(keyword in content for keyword in ["天气", "温度", "气候", "北京"])
            
            print(f"✅ 查询成功，回复长度: {len(final_message.content)} 字符")
    
    @pytest.mark.integration
    def test_search_error_handling(self):
        """测试搜索错误处理"""
        # 测试搜索函数的错误处理
        result = tavily_search("")  # 空查询
        assert isinstance(result, str)
        
        # 测试包含特殊字符的查询
        result = tavily_search("@#$%^&*()")
        assert isinstance(result, str)
        assert len(result) > 0
    
    def teardown_method(self):
        """测试后清理"""
        if "OPENAI_API_KEY" in os.environ:
            del os.environ["OPENAI_API_KEY"]
        if "OPENAI_BASE_URL" in os.environ:
            del os.environ["OPENAI_BASE_URL"]


# 独立测试函数
def test_tavily_client_basic():
    """测试Tavily客户端基本功能"""
    try:
        client = TavilyClient(api_key="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")
        result = client.search("test query", max_results=1)
        assert isinstance(result, dict)
        print("✅ Tavily客户端连接正常")
    except Exception as e:
        pytest.skip(f"Tavily连接失败: {e}")


def test_search_function_standalone():
    """独立测试搜索函数"""
    result = tavily_search("hello world")
    assert isinstance(result, str)
    assert len(result) > 0
    print(f"独立搜索测试结果: {result[:50]}...")