"""
测试更新后文档中的示例代码
验证GLM-4.5 + Tavily搜索引擎的协同工作示例
"""

import os
import pytest
from langchain_openai import ChatOpenAI
from tavily import TavilyClient
from langgraph.prebuilt import create_react_agent


def tavily_search(query: str) -> str:
    """使用Tavily搜索引擎获取实时信息（文档示例代码）"""
    tavily_client = TavilyClient(api_key="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")
    response = tavily_client.search(
        query=query,
        max_results=3,
        include_answer=True
    )
    if response.get("answer"):
        return f"搜索结果: {response['answer']}"
    else:
        return "抱歉，没有找到相关信息。"


class TestUpdatedDocExample:
    """测试更新后的文档示例"""
    
    def setup_method(self):
        """测试前设置"""
        self.api_key = "4ab728278a1e4df5a85d203208c7bfe6.bbjs0V1YrPtGk9PW"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
        
        os.environ["OPENAI_API_KEY"] = self.api_key
        os.environ["OPENAI_BASE_URL"] = self.base_url
    
    def test_doc_example_components(self):
        """测试文档示例的各个组件"""
        # 1. 测试GLM-4.5模型配置（文档代码）
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,  # 实际中是 "your_zhipu_api_key"
            base_url="https://open.bigmodel.cn/api/paas/v4/",
            temperature=0.3
        )
        
        # 验证模型配置
        assert model.model_name == "glm-4.5"
        assert model.temperature == 0.3
        
        # 2. 测试搜索工具
        search_result = tavily_search("北京天气")
        assert isinstance(search_result, str)
        assert len(search_result) > 0
        assert "搜索结果" in search_result or "抱歉" in search_result
        
        print(f"搜索工具测试结果: {search_result[:50]}...")
    
    @pytest.mark.integration
    def test_doc_example_complete_workflow(self):
        """测试文档示例的完整工作流程"""
        # 配置GLM-4.5模型（文档示例代码）
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,
            base_url="https://open.bigmodel.cn/api/paas/v4/",
            temperature=0.3
        )
        
        # 创建具有搜索能力的智能体（文档示例代码）
        agent = create_react_agent(
            model=model,
            tools=[tavily_search],
            prompt="你是一个智能助手，可以搜索最新信息来回答问题。"
        )
        
        # 运行智能体（文档示例代码）
        result = agent.invoke({
            "messages": [{"role": "user", "content": "今天北京的天气怎么样？"}]
        })
        
        # 验证结果结构
        assert "messages" in result
        assert len(result["messages"]) > 0
        
        # 获取回复（文档示例代码）
        final_answer = result["messages"][-1].content
        
        # 验证最终回复
        assert isinstance(final_answer, str)
        assert len(final_answer) > 30  # 应该有足够详细的回复
        
        # 验证回复内容相关性
        final_lower = final_answer.lower()
        weather_keywords = ["天气", "温度", "气温", "晴", "阴", "雨", "雪", "风", "度"]
        beijing_keywords = ["北京", "beijing"]
        
        # 应该包含天气相关词汇
        has_weather = any(keyword in final_lower for keyword in weather_keywords)
        has_beijing = any(keyword in final_lower for keyword in beijing_keywords)
        
        assert has_weather or has_beijing, f"回复应该包含天气或北京相关信息: {final_answer}"
        
        # 验证对话流程
        messages = result["messages"]
        search_call_found = False
        search_response_found = False
        
        for msg in messages:
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                search_call_found = True
                tool_call = msg.tool_calls[0]
                assert tool_call['name'] == 'tavily_search'
            
            if hasattr(msg, 'tool_call_id'):
                search_response_found = True
        
        assert search_call_found, "应该发现智能体调用了搜索工具"
        assert search_response_found, "应该发现搜索响应"
        
        print(f"\\n🎯 文档示例验证成功:")
        print(f"1. 搜索调用: {'✅' if search_call_found else '❌'}")
        print(f"2. 搜索响应: {'✅' if search_response_found else '❌'}")
        print(f"3. 最终回复长度: {len(final_answer)} 字符")
        print(f"4. 回复预览: {final_answer[:100]}...")
    
    @pytest.mark.integration
    def test_doc_example_different_queries(self):
        """测试文档示例对不同查询的处理"""
        model = ChatOpenAI(
            model="glm-4.5",
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=0.3
        )
        
        agent = create_react_agent(
            model=model,
            tools=[tavily_search],
            prompt="你是一个智能助手，可以搜索最新信息来回答问题。"
        )
        
        # 测试不同类型的查询
        test_queries = [
            "最新的AI技术新闻",
            "上海今天的天气",
            "2024年的热点新闻"
        ]
        
        for query in test_queries:
            print(f"\\n🧪 测试查询: {query}")
            
            result = agent.invoke({
                "messages": [{"role": "user", "content": query}]
            })
            
            final_answer = result["messages"][-1].content
            assert len(final_answer) > 20
            print(f"✅ 成功，回复长度: {len(final_answer)} 字符")
    
    def teardown_method(self):
        """测试后清理"""
        if "OPENAI_API_KEY" in os.environ:
            del os.environ["OPENAI_API_KEY"]
        if "OPENAI_BASE_URL" in os.environ:
            del os.environ["OPENAI_BASE_URL"]