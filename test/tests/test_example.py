"""
示例测试文件
演示如何编写和运行测试用例
"""

import pytest


def test_basic_example():
    """基本测试示例"""
    assert 1 + 1 == 2


@pytest.mark.asyncio
async def test_async_example():
    """异步测试示例"""
    async def async_add(a, b):
        return a + b
    
    result = await async_add(2, 3)
    assert result == 5


class TestExampleClass:
    """测试类示例"""
    
    def test_string_operations(self):
        """字符串操作测试"""
        text = "LangGraph"
        assert text.lower() == "langgraph"
        assert len(text) == 9
    
    def test_list_operations(self):
        """列表操作测试"""
        items = [1, 2, 3]
        items.append(4)
        assert len(items) == 4
        assert items[-1] == 4


def test_with_fixture(tmp_path):
    """使用pytest内置fixture的测试"""
    # 创建临时文件
    test_file = tmp_path / "test.txt"
    test_file.write_text("Hello, LangGraph!")
    
    # 验证文件内容
    content = test_file.read_text()
    assert content == "Hello, <PERSON>Graph!"