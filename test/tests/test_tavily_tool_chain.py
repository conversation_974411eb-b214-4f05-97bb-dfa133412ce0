#!/usr/bin/env python3
"""
测试更新后文档中的Tavily工具链代码
验证将DuckDuckGo替换为Tavily后的功能
"""

import pytest
from typing import Optional, Type
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
from pydantic import BaseModel, Field
from tavily import TavilyClient
from langgraph.prebuilt import ToolNode, tools_condition


class TavilySearchInput(BaseModel):
    """Tavily搜索工具的输入模式"""
    query: str = Field(description="要搜索的查询内容")


class TavilySearchTool(BaseTool):
    """Tavily搜索工具 - 符合LangChain工具接口"""
    
    name: str = "tavily_search"
    description: str = "使用Tavily搜索引擎获取最新的网络信息"
    args_schema: Type[BaseModel] = TavilySearchInput
    api_key: str = Field(exclude=True)
    client: TavilyClient = Field(exclude=True)
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key=api_key, client=TavilyClient(api_key=api_key), **kwargs)
    
    def _run(self, query: str, run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """执行Tavily搜索"""
        try:
            response = self.client.search(query=query, max_results=3, include_answer=True)
            return response.get("answer", "未找到相关信息")
        except Exception as e:
            return f"搜索出错: {str(e)}"


class TestTavilyToolChain:
    """测试Tavily工具链功能"""
    
    def setup_method(self):
        """设置测试环境"""
        self.api_key = "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"
        self.tavily_tool = TavilySearchTool(api_key=self.api_key)
    
    def test_tavily_tool_creation(self):
        """测试Tavily工具创建"""
        assert self.tavily_tool.name == "tavily_search"
        assert "Tavily搜索引擎" in self.tavily_tool.description
        assert self.tavily_tool.args_schema == TavilySearchInput
    
    def test_tavily_tool_search(self):
        """测试Tavily工具搜索功能"""
        result = self.tavily_tool._run("北京天气")
        
        assert isinstance(result, str)
        assert len(result) > 0
        assert result != "未找到相关信息"
        print(f"搜索结果: {result[:100]}...")
    
    def test_tool_node_creation(self):
        """测试ToolNode创建"""
        tools = [self.tavily_tool]
        tool_node = ToolNode(tools)
        
        assert tool_node is not None
        assert len(tool_node.tools_by_name) == 1
        assert "tavily_search" in tool_node.tools_by_name
    
    def test_tool_with_different_queries(self):
        """测试不同查询的工具响应"""
        queries = [
            "今天上海天气如何",
            "2024年中国GDP增长率",
            "ChatGPT最新版本"
        ]
        
        for query in queries:
            result = self.tavily_tool._run(query)
            assert isinstance(result, str)
            assert len(result) > 0
            print(f"查询: {query}")
            print(f"结果: {result[:80]}...")
            print("---")
    
    def test_tool_error_handling(self):
        """测试工具错误处理"""
        # 创建一个有问题的工具实例来测试错误处理
        try:
            bad_tool = TavilySearchTool(api_key="invalid_key")
            result = bad_tool._run("test query")
            
            # 即使API key无效，也应该返回字符串而不是抛出异常
            assert isinstance(result, str)
            assert "搜索出错" in result or "未找到相关信息" in result
        except Exception as e:
            # 如果确实抛出异常，确保我们能够捕获
            pytest.fail(f"工具应该优雅处理错误，而不是抛出异常: {e}")
    
    @pytest.mark.integration
    def test_full_tool_chain_setup(self):
        """测试完整的工具链设置（模拟文档中的代码）"""
        # 这里模拟文档中的完整代码
        tavily_tool = TavilySearchTool(api_key=self.api_key)
        tools = [tavily_tool]
        tool_node = ToolNode(tools)
        
        # 验证工具链设置
        assert len(tools) == 1
        assert tools[0].name == "tavily_search"
        assert tool_node is not None
        
        # 验证tools_condition函数存在
        assert callable(tools_condition)
        
        print("✅ 完整工具链设置成功!")


if __name__ == "__main__":
    # 运行基本测试
    test_instance = TestTavilyToolChain()
    test_instance.setup_method()
    
    print("🧪 开始测试Tavily工具链...")
    
    try:
        test_instance.test_tavily_tool_creation()
        print("✅ 工具创建测试通过")
        
        test_instance.test_tavily_tool_search()
        print("✅ 搜索功能测试通过")
        
        test_instance.test_tool_node_creation()
        print("✅ ToolNode创建测试通过")
        
        test_instance.test_full_tool_chain_setup()
        print("✅ 完整工具链测试通过")
        
        print("\n🎉 所有测试通过！Tavily工具链替换DuckDuckGo成功！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        raise