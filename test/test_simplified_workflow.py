#!/usr/bin/env python3
"""
测试简化后的6.3章节代码
基于官方interrupt模式，使用GLM-4.5
"""
import os
import uuid
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt, Command
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class WorkflowState(TypedDict):
    messages: Annotated[list, add_messages]
    user_request: str

def create_resumable_workflow():
    """创建可恢复的工作流"""
    
    def task_execution_node(state: WorkflowState):
        """任务执行节点 - 检查是否需要人工指导"""
        import os
        user_request = state.get("user_request", state["messages"][-1].content)
        
        # 简单判断：包含这些关键词就请求人工指导
        if any(keyword in user_request for keyword in ["复杂", "困难", "不确定", "不知道", "需要帮助"]):
            # 使用interrupt请求人工指导
            guidance_request = {
                "task": user_request,
                "question": "这个任务比较复杂，您希望我如何处理？",
                "options": ["继续处理", "简化方式", "详细分析", "转交人工"]
            }
            human_guidance = interrupt(guidance_request)
            
            # 根据人工指导生成回复
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key)
                    
                    prompt = f"用户请求：{user_request}\n人工指导：{human_guidance}\n请用1-2句话简短回复。"
                    response = llm.invoke([HumanMessage(content=prompt)])
                    final_message = response.content
                except Exception as e:
                    print(f"⚠️ GLM-4.5调用失败: {e}")
                    final_message = f"根据指导'{human_guidance}'，已处理您的请求。"
            else:
                final_message = f"根据指导'{human_guidance}'，已处理您的请求。"
        else:
            # 简单任务直接处理
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key)
                    prompt = f"请简短回复：{user_request}"
                    response = llm.invoke([HumanMessage(content=prompt)])
                    final_message = response.content
                except Exception as e:
                    print(f"⚠️ GLM-4.5调用失败: {e}")
                    final_message = f"已处理您的请求：{user_request}"
            else:
                final_message = f"已处理您的请求：{user_request}"
        
        return {"messages": [AIMessage(content=final_message)]}
    
    # 构建图 - 简化版本
    from langgraph.checkpoint.memory import MemorySaver
    
    graph = StateGraph(WorkflowState)
    graph.add_node("task_execution", task_execution_node)
    graph.add_edge(START, "task_execution")
    
    # 使用checkpointer保存状态（支持interrupt）
    checkpointer = MemorySaver()
    return graph.compile(checkpointer=checkpointer)

def demo_resumable_workflow():
    """演示可恢复工作流"""
    import uuid
    from langgraph.types import Command
    
    print("🔄 可恢复工作流演示（基于官方interrupt模式）")
    print("🤖 AI会在遇到复杂情况时请求人工指导")
    print("📋 支持的指导选项：继续处理、简化方式、详细分析、转交人工\n")
    
    # 创建应用
    app = create_resumable_workflow()
    print("✅ 成功创建工作流应用")
    
    # 配置线程ID（支持interrupt必需）
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    # 测试复杂任务
    complex_request = "这是一个非常复杂的数据分析任务，需要处理大量不确定因素"
    print(f"📝 用户请求: {complex_request}")
    
    # 执行任务
    result = app.invoke({
        "messages": [HumanMessage(content=complex_request)],
        "user_request": complex_request
    }, config=config)
    
    # 检查是否触发了interrupt
    if "__interrupt__" in result:
        print("⏸️ AI请求人工指导！")
        interrupt_info = result["__interrupt__"][0]
        print(f"📋 指导请求: {interrupt_info.value}")
        
        # 模拟人工选择
        human_guidance = "简化方式"  # 可以是：继续处理、简化方式、详细分析、转交人工
        print(f"\n🤖 模拟人工选择: {human_guidance}")
        
        # 使用Command恢复执行
        final_result = app.invoke(Command(resume=human_guidance), config=config)
        print(f"\n✅ 最终回复: {final_result['messages'][-1].content}")
    else:
        # 直接完成
        print(f"✅ 直接完成: {result['messages'][-1].content}")
    
    print("\n" + "="*50)
    print("🎯 测试简单任务（不触发interrupt）")
    
    # 测试简单任务
    simple_request = "今天天气怎么样？"
    print(f"📝 用户请求: {simple_request}")
    
    config2 = {"configurable": {"thread_id": str(uuid.uuid4())}}
    result2 = app.invoke({
        "messages": [HumanMessage(content=simple_request)],
        "user_request": simple_request
    }, config=config2)
    
    print(f"✅ 直接回复: {result2['messages'][-1].content}")

def test_api_status():
    """测试API状态"""
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-4:]})")
        return True
    else:
        print("⚠️ 未配置智谱API，将使用模拟模式")
        return False

if __name__ == "__main__":
    print("🚀 测试简化后的6.3章节代码")
    print("=" * 60)
    
    # 检查API配置
    has_api = test_api_status()
    print()
    
    # 运行演示
    try:
        demo_resumable_workflow()
        print("\n🎉 测试完成！")
        print("📝 总结:")
        print("• 简化版本基于官方interrupt模式")
        print("• 复杂任务正确触发interrupt请求人工指导")
        print("• 使用Command(resume=...)恢复执行")
        print("• GLM-4.5生成高质量回复")
        print("• 代码简洁易懂，符合官方最佳实践")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()