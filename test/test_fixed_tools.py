#!/usr/bin/env python3
"""
测试修复后的工具代码
验证所有修复的功能都能正常工作
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, Mock
from langchain_core.tools import tool
import requests
import os
import math


# 复制修复后的工具实现
@tool
def secure_file_reader_tool(file_path: str) -> str:
    """
    安全的文件读取工具（修复后）
    """
    try:
        # 安全的路径检查
        def is_safe_path(file_path: str, allowed_dir: str = "./data") -> tuple[bool, Path]:
            try:
                allowed_dir = Path(allowed_dir).resolve()
                requested_path = Path(file_path).resolve()
                
                if not requested_path.is_relative_to(allowed_dir):
                    return False, None
                    
                return True, requested_path
            except (ValueError, OSError, RuntimeError):
                return False, None
        
        is_safe, resolved_path = is_safe_path(file_path)
        if not is_safe:
            return "错误：不允许访问该路径"
        
        if not resolved_path.exists():
            return f"错误：文件 {file_path} 不存在"
        
        with open(resolved_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if len(content) > 1000:
            content = content[:1000] + "...(内容过长，已截断)"
        
        return f"文件内容：\n{content}"
    except Exception as e:
        return f"读取文件失败：{str(e)}"


@tool
def enhanced_api_call_tool(url: str, method: str = "GET", data: str = None, headers: str = None) -> str:
    """
    增强的API调用工具（修复后）
    """
    try:
        import json
        
        headers_dict = {}
        if headers:
            try:
                headers_dict = json.loads(headers)
            except json.JSONDecodeError:
                return "错误：请求头格式无效，必须是有效的JSON字符串"
        
        json_data = {}
        if data:
            try:
                json_data = json.loads(data)
            except json.JSONDecodeError:
                return "错误：请求数据格式无效，必须是有效的JSON字符串"
        
        method = method.upper()
        
        if method == "GET":
            response = requests.get(url, headers=headers_dict, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=json_data, headers=headers_dict, timeout=10)
        elif method == "PUT":
            response = requests.put(url, json=json_data, headers=headers_dict, timeout=10)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers_dict, timeout=10)
        elif method == "PATCH":
            response = requests.patch(url, json=json_data, headers=headers_dict, timeout=10)
        else:
            return f"不支持的 HTTP 方法：{method}。支持的方法：GET, POST, PUT, DELETE, PATCH"
        
        response.raise_for_status()
        
        try:
            return f"API 调用成功：{response.json()}"
        except json.JSONDecodeError:
            return f"API 调用成功：{response.text[:500]}..."
        
    except requests.exceptions.Timeout:
        return "错误：请求超时，请检查网络连接或增加超时时间"
    except requests.exceptions.ConnectionError:
        return "错误：无法连接到目标服务器，请检查URL和网络"
    except requests.exceptions.HTTPError as e:
        return f"错误：HTTP错误 {e.response.status_code} - {e.response.reason}"
    except requests.exceptions.RequestException as e:
        return f"API 调用失败：{str(e)}"
    except Exception as e:
        return f"处理错误：{str(e)}"


@tool
def secure_math_calculator(expression: str) -> str:
    """
    安全的数学计算器（已修复）
    """
    try:
        allowed_names = {
            k: v for k, v in math.__dict__.items() if not k.startswith("__")
        }
        allowed_names.update({"abs": abs, "round": round})
        allowed_names["math"] = math
        
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return f"计算结果：{result}"
    except Exception as e:
        return f"计算错误：{str(e)}"


class TestFixedTools:
    """测试修复后的工具"""
    
    def setup_method(self):
        """测试前准备"""
        self.test_dir = Path("./data")
        self.test_dir.mkdir(exist_ok=True)
        
        self.test_file = self.test_dir / "test.txt"
        self.test_file.write_text("Hello, World!\n这是测试内容。", encoding='utf-8')
    
    def teardown_method(self):
        """测试后清理"""
        if self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def test_secure_file_reader_blocks_path_traversal(self):
        """测试修复后的文件读取工具能阻止路径遍历"""
        dangerous_paths = [
            "../../etc/passwd",
            "../../../sensitive.txt", 
            "data/../../../etc/hosts",
            "data/./../../secret.txt",
            "/etc/passwd",
            "C:\\Windows\\System32\\config\\SAM"
        ]
        
        for path in dangerous_paths:
            result = secure_file_reader_tool.invoke({"file_path": path})
            assert "错误：不允许访问该路径" in result, f"路径 {path} 应该被阻止"
    
    def test_secure_file_reader_normal_access(self):
        """测试正常文件访问"""
        result = secure_file_reader_tool.invoke({"file_path": "data/test.txt"})
        assert "文件内容：" in result
        assert "Hello, World!" in result
    
    @patch('requests.put')
    def test_enhanced_api_supports_put(self, mock_put):
        """测试增强API工具支持PUT方法"""
        mock_response = Mock()
        mock_response.json.return_value = {"status": "updated"}
        mock_response.raise_for_status.return_value = None
        mock_put.return_value = mock_response
        
        result = enhanced_api_call_tool.invoke({
            "url": "https://api.example.com/resource/1",
            "method": "PUT",
            "data": '{"name": "updated_name"}'
        })
        
        assert "API 调用成功" in result
        assert "updated" in result
        mock_put.assert_called_once()
    
    @patch('requests.delete')
    def test_enhanced_api_supports_delete(self, mock_delete):
        """测试增强API工具支持DELETE方法"""
        mock_response = Mock()
        mock_response.json.return_value = {"status": "deleted"}
        mock_response.raise_for_status.return_value = None
        mock_delete.return_value = mock_response
        
        result = enhanced_api_call_tool.invoke({
            "url": "https://api.example.com/resource/1",
            "method": "DELETE"
        })
        
        assert "API 调用成功" in result
        mock_delete.assert_called_once()
    
    @patch('requests.patch')
    def test_enhanced_api_supports_patch(self, mock_patch):
        """测试增强API工具支持PATCH方法"""
        mock_response = Mock()
        mock_response.json.return_value = {"status": "patched"}
        mock_response.raise_for_status.return_value = None
        mock_patch.return_value = mock_response
        
        result = enhanced_api_call_tool.invoke({
            "url": "https://api.example.com/resource/1",
            "method": "PATCH",
            "data": '{"field": "new_value"}'
        })
        
        assert "API 调用成功" in result
        mock_patch.assert_called_once()
    
    def test_enhanced_api_with_headers(self):
        """测试API工具支持自定义请求头"""
        result = enhanced_api_call_tool.invoke({
            "url": "https://api.example.com/test",
            "method": "GET",
            "headers": '{"Authorization": "Bearer token", "Content-Type": "application/json"}'
        })
        
        # 应该不会因为请求头格式而报错
        assert "错误：请求头格式无效" not in result
    
    def test_enhanced_api_invalid_json_data(self):
        """测试API工具对无效JSON数据的处理"""
        result = enhanced_api_call_tool.invoke({
            "url": "https://api.example.com/test",
            "method": "POST",
            "data": "invalid json"
        })
        
        assert "错误：请求数据格式无效" in result
    
    def test_enhanced_api_invalid_headers(self):
        """测试API工具对无效请求头的处理"""
        result = enhanced_api_call_tool.invoke({
            "url": "https://api.example.com/test",
            "method": "GET",
            "headers": "invalid json"
        })
        
        assert "错误：请求头格式无效" in result
    
    def test_math_calculator_with_math_module(self):
        """测试修复后的计算器支持math模块调用"""
        result = secure_math_calculator.invoke({"expression": "math.sqrt(16)"})
        assert "计算结果：4.0" in result
        
        result = secure_math_calculator.invoke({"expression": "math.pi * 2"})
        assert "计算结果：6.283" in result
        
        result = secure_math_calculator.invoke({"expression": "math.sin(math.pi/2)"})
        assert "计算结果：1.0" in result
    
    def test_tool_invoke_method_works(self):
        """测试工具的invoke方法调用正常工作"""
        # 测试计算器
        result = secure_math_calculator.invoke({"expression": "2 + 3 * 4"})
        assert "计算结果：14" in result
        
        # 测试文件读取
        result = secure_file_reader_tool.invoke({"file_path": "data/test.txt"})
        assert "文件内容：" in result
    
    def test_tool_metadata(self):
        """测试工具元数据正确"""
        assert secure_math_calculator.name == "secure_math_calculator"
        assert secure_file_reader_tool.name == "secure_file_reader_tool"
        assert enhanced_api_call_tool.name == "enhanced_api_call_tool"
        
        # 检查描述
        assert "数学计算器" in secure_math_calculator.description
        assert "文件读取工具" in secure_file_reader_tool.description
        assert "API调用工具" in enhanced_api_call_tool.description


def test_comprehensive_security():
    """综合安全测试"""
    print("\n" + "="*50)
    print("运行综合安全测试")
    print("="*50)
    
    # 测试路径遍历攻击
    attack_paths = [
        "../../etc/passwd",
        "../../../Windows/System32/config/SAM",
        "data/../../../etc/shadow",
        "/etc/hosts",
        "C:\\autoexec.bat"
    ]
    
    print("\n路径遍历攻击测试：")
    for path in attack_paths:
        result = secure_file_reader_tool.invoke({"file_path": path})
        status = "✅ 阻止" if "错误：不允许访问该路径" in result else "❌ 允许"
        print(f"{status} {path}")
    
    # 测试计算器安全
    print("\n计算器安全测试：")
    dangerous_expressions = [
        "__import__('os').system('ls')",
        "exec('print(1)')",
        "eval('1+1')",
        "globals()",
        "locals()"
    ]
    
    for expr in dangerous_expressions:
        result = secure_math_calculator.invoke({"expression": expr})
        status = "✅ 阻止" if "计算错误" in result else "❌ 允许"
        print(f"{status} {expr}")


if __name__ == "__main__":
    # 运行所有测试
    pytest.main([__file__, "-v"])
    
    # 运行安全测试
    test_comprehensive_security()