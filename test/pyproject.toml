[project]
name = "test-suite"
version = "0.1.0"
description = "LangGraph 测试套件"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "langchain-community>=0.3.27",
    "langchain-openai>=0.3.28",
    "langgraph>=0.6.3",
    "langgraph-checkpoint-sqlite>=2.0.11",
    "pyjwt>=2.10.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "python-dotenv>=1.1.1",
    "tavily-python>=0.7.10",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=html",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]
