#!/usr/bin/env python3
"""
6.3章节 - 修复后的可恢复工作流
真正展现GLM-4.5的分析能力
"""
import os
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class WorkflowState(TypedDict):
    messages: Annotated[list, add_messages]
    workflow_step: str
    human_input: str
    approval_status: str
    retry_count: int
    max_retries: int
    original_request: str  # 添加原始请求字段

def create_resumable_workflow():
    """创建可恢复的工作流"""
    
    def task_execution_node(state: WorkflowState):
        """任务执行节点"""
        current_step = state.get("workflow_step", "start")
        print(f"📍 当前步骤: {current_step}")
        
        if current_step == "start":
            # 保存用户的原始请求
            original_request = state["messages"][-1].content
            print(f"🚀 开始处理任务: {original_request}")
            return {
                "workflow_step": "processing",
                "original_request": original_request,
                "messages": [AIMessage(content="开始处理您的请求...")]
            }
        elif current_step == "processing":
            # 分析用户的原始请求，而不是AI的回复
            original_request = state.get("original_request", "")
            print(f"🔍 分析用户请求: {original_request}")
            
            # 使用GLM-4.5进行复杂度分析
            api_key = os.getenv("ZHIPUAI_API_KEY")
            complexity_analysis = None
            
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(
                        model="glm-4.5",
                        temperature=0.1,
                        api_key=api_key
                    )
                    
                    analysis_prompt = f"""
                    分析以下用户请求的复杂度和风险：
                    用户请求：{original_request}
                    
                    请评估：
                    1. 任务复杂度（简单/中等/复杂/困难）
                    2. 是否需要人工指导（是/否）
                    3. 简要说明原因
                    
                    格式：复杂度|需要指导|原因
                    """
                    
                    response = llm.invoke([HumanMessage(content=analysis_prompt)])
                    complexity_analysis = response.content.strip()
                    print(f"🧠 GLM-4.5分析结果: {complexity_analysis}")
                    
                except Exception as e:
                    print(f"⚠️ GLM-4.5分析失败: {e}")
            
            # 解析分析结果或使用关键词检测
            needs_guidance = False
            complexity_level = "简单"
            reason = "自动判断"
            
            if complexity_analysis:
                try:
                    parts = complexity_analysis.split('|')
                    complexity_level = parts[0].strip()
                    needs_guidance = "是" in parts[1].strip()
                    reason = parts[2].strip() if len(parts) > 2 else "AI分析"
                except:
                    # 如果解析失败，回退到关键词检测
                    pass
            
            # 关键词检测（补充或备用方案）
            if ("复杂" in original_request or "困难" in original_request or 
                "不确定" in original_request or "不知道" in original_request or
                complexity_level in ["复杂", "困难"]):
                needs_guidance = True
                reason = f"检测到复杂任务指标: {complexity_level}"
            
            print(f"📊 分析结果: 复杂度={complexity_level}, 需要指导={needs_guidance}, 原因={reason}")
            
            if needs_guidance:
                print("⏸️ 遇到复杂任务，请求人工指导...")
                guidance_request = {
                    "type": "guidance_needed",
                    "original_task": original_request,
                    "complexity_level": complexity_level,
                    "ai_analysis": complexity_analysis or "基于关键词检测",
                    "specific_question": f"这个任务被评估为'{complexity_level}'，您希望我如何处理？",
                    "options": [
                        "继续尝试自动处理",
                        "简化处理方式",
                        "暂停等待更多信息",
                        "转交人工处理"
                    ]
                }
                return interrupt(guidance_request)
            else:
                print("✅ 任务复杂度适中，自动处理完成")
                
                # 使用GLM-4.5生成真实回复
                if api_key:
                    try:
                        from langchain_community.chat_models import ChatZhipuAI
                        llm = ChatZhipuAI(
                            model="glm-4.5",
                            temperature=0.7,
                            api_key=api_key
                        )
                        response = llm.invoke([HumanMessage(content=original_request)])
                        final_message = response.content
                    except Exception as e:
                        print(f"⚠️ GLM-4.5回复失败: {e}")
                        final_message = f"任务'{original_request}'处理完成！"
                else:
                    final_message = f"任务'{original_request}'处理完成！"
                
                return {
                    "workflow_step": "completed",
                    "messages": [AIMessage(content=final_message)]
                }
        
        return {"workflow_step": "error"}
    
    def feedback_processing_node(state: WorkflowState):
        """反馈处理节点"""
        human_input = state.get("human_input", "")
        retry_count = state.get("retry_count", 0)
        max_retries = state.get("max_retries", 3)
        
        print(f"🤖 处理人工反馈: '{human_input}'")
        
        if not human_input:
            print("🔄 自动继续")
            return {"approval_status": "auto_continue"}
        
        # 解析人工指令
        if "继续" in human_input:
            print("✅ 收到继续指令")
            return {
                "approval_status": "continue",
                "workflow_step": "processing"
            }
        elif "简化" in human_input:
            print("🔧 收到简化指令")
            original_request = state.get("original_request", "")
            
            # 使用GLM-4.5生成简化回复
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(
                        model="glm-4.5",
                        temperature=0.7,
                        api_key=api_key
                    )
                    simplified_prompt = f"""
                    用户请求：{original_request}
                    
                    请用简化的方式回复这个请求，提供基础的、直接的回答，避免复杂的分析。
                    """
                    response = llm.invoke([HumanMessage(content=simplified_prompt)])
                    simplified_reply = response.content
                except Exception as e:
                    print(f"⚠️ GLM-4.5简化回复失败: {e}")
                    simplified_reply = "好的，我将采用简化的处理方式。"
            else:
                simplified_reply = "好的，我将采用简化的处理方式。"
            
            return {
                "approval_status": "simplify",
                "workflow_step": "completed",
                "messages": [AIMessage(content=simplified_reply)]
            }
        elif "暂停" in human_input:
            print("⏸️ 收到暂停指令")
            return {
                "approval_status": "pause",
                "workflow_step": "waiting"
            }
        elif "转交" in human_input:
            print("👤 收到转交指令")
            return {
                "approval_status": "escalate",
                "workflow_step": "human_takeover",
                "messages": [AIMessage(content="好的，我将把这个任务转交给人工处理。")]
            }
        elif "重试" in human_input:
            if retry_count < max_retries:
                print(f"🔄 收到重试指令 (第{retry_count + 1}次)")
                return {
                    "approval_status": "retry",
                    "workflow_step": "processing",
                    "retry_count": retry_count + 1
                }
            else:
                print(f"❌ 已达到最大重试次数 ({max_retries})")
                return {
                    "approval_status": "max_retries_reached",
                    "workflow_step": "failed",
                    "messages": [AIMessage(content="已达到最大重试次数，任务失败。")]
                }
        else:
            # 自定义指令 - 让GLM-4.5处理
            print(f"📝 收到自定义指令: {human_input}")
            original_request = state.get("original_request", "")
            
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(
                        model="glm-4.5",
                        temperature=0.7,
                        api_key=api_key
                    )
                    custom_prompt = f"""
                    原始用户请求：{original_request}
                    人工指导：{human_input}
                    
                    请根据人工指导来处理原始请求。
                    """
                    response = llm.invoke([HumanMessage(content=custom_prompt)])
                    custom_reply = response.content
                except Exception as e:
                    print(f"⚠️ GLM-4.5自定义回复失败: {e}")
                    custom_reply = f"收到指令：{human_input}，我将按此执行。"
            else:
                custom_reply = f"收到指令：{human_input}，我将按此执行。"
            
            return {
                "approval_status": "custom_instruction",
                "workflow_step": "completed",
                "messages": [AIMessage(content=custom_reply)]
            }
    
    def workflow_router(state: WorkflowState) -> str:
        """工作流路由"""
        workflow_step = state.get("workflow_step", "start")
        approval_status = state.get("approval_status", "")
        
        print(f"🧭 路由决策: step={workflow_step}, status={approval_status}")
        
        if workflow_step == "completed":
            return "end"
        elif workflow_step == "failed":
            return "end"
        elif workflow_step == "human_takeover":
            return "end"
        elif workflow_step == "waiting":
            return "pause"
        else:
            return "continue_processing"
    
    # 构建图
    graph = StateGraph(WorkflowState)
    graph.add_node("task_execution", task_execution_node)
    graph.add_node("feedback_processing", feedback_processing_node)
    graph.add_node("pause", lambda state: {"workflow_step": "paused"})
    graph.add_node("continue_processing", task_execution_node)
    
    # 设置流程
    graph.add_edge(START, "task_execution")
    graph.add_edge("task_execution", "feedback_processing")
    
    # 条件路由
    graph.add_conditional_edges(
        "feedback_processing",
        workflow_router,
        {
            "end": END,
            "pause": "pause", 
            "continue_processing": "continue_processing"
        }
    )
    
    graph.add_edge("continue_processing", "feedback_processing")
    graph.add_edge("pause", END)
    
    return graph.compile()

def interactive_resumable_workflow():
    """交互式可恢复工作流演示"""
    print("🚀 6.3章节：修复后的可恢复工作流演示")
    print("=" * 60)
    print("🧠 真正展现GLM-4.5的分析和回复能力")
    print("🤖 AI会智能分析任务复杂度，遇到复杂情况时请求人工指导")
    print("📋 支持多种人工指令：继续、简化、暂停、转交、重试")
    print()
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-4:]})")
        print("🚀 将使用真实GLM-4.5进行分析和回复")
    else:
        print("⚠️ 未配置智谱API，将使用模拟模式")
    
    try:
        app = create_resumable_workflow()
        print("✅ 可恢复工作流已创建")
    except Exception as e:
        print(f"❌ 创建工作流失败: {e}")
        return
    
    print("\n📝 建议测试的任务类型:")
    print("• 简单任务: '今天天气怎么样' '你好'")
    print("• 复杂任务: '这是一个复杂的数据分析任务' '这个困难的问题需要处理'") 
    print("• 不确定任务: '我不知道该怎么办' '这个问题我不太确定'")
    print("• 输入 'quit' 退出")
    print()
    
    while True:
        print("-" * 50)
        user_request = input("👤 请输入您的任务请求: ").strip()
        
        if user_request.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
        
        if not user_request:
            continue
            
        print(f"\n🔄 开始处理任务: {user_request}")
        print("=" * 50)
        
        # 初始状态
        initial_state = {
            "messages": [HumanMessage(content=user_request)],
            "workflow_step": "start",
            "human_input": "",
            "approval_status": "",
            "retry_count": 0,
            "max_retries": 3,
            "original_request": user_request
        }
        
        current_state = initial_state
        
        while True:
            try:
                # 执行工作流
                result = app.invoke(current_state)
                
                # 检查是否完成
                final_step = result.get("workflow_step", "")
                if final_step in ["completed", "failed", "human_takeover", "paused"]:
                    print(f"\n🎯 工作流结束: {final_step}")
                    if "messages" in result and result["messages"]:
                        print(f"💬 GLM-4.5最终回复: {result['messages'][-1].content}")
                    break
                else:
                    print("🤔 工作流状态异常")
                    break
                    
            except Exception as e:
                error_str = str(e)
                if "interrupt" in error_str.lower():
                    print("\n" + "🔴" * 20 + " INTERRUPT触发 " + "🔴" * 20)
                    print("⏸️ GLM-4.5请求人工指导！")
                    
                    # 显示分析信息
                    print("📋 GLM-4.5分析详情:")
                    try:
                        # 简单显示interrupt信息
                        if "guidance_needed" in error_str:
                            print("   • 类型: 需要人工指导")
                            print(f"   • 任务: {user_request}")
                            print("   • 原因: GLM-4.5检测到复杂任务")
                    except:
                        print(f"   • 详情: {error_str}")
                    
                    print("\n💡 可用指令:")
                    instructions = [
                        "继续尝试自动处理",
                        "简化处理方式",
                        "暂停等待更多信息", 
                        "转交人工处理",
                        "重试一次"
                    ]
                    for i, instruction in enumerate(instructions, 1):
                        print(f"   {i}. {instruction}")
                    
                    # 获取人工指导
                    print()
                    human_guidance = input("👤 请输入您的指导 (或输入对应数字): ").strip()
                    
                    # 处理数字选择
                    if human_guidance.isdigit():
                        idx = int(human_guidance) - 1
                        if 0 <= idx < len(instructions):
                            human_guidance = instructions[idx]
                    
                    print(f"📝 收到指导: {human_guidance}")
                    
                    # 更新状态并继续
                    current_state = {
                        **result,
                        "human_input": human_guidance
                    }
                    continue
                    
                else:
                    print(f"❌ 其他错误: {error_str}")
                    break
        
        print("=" * 50)

if __name__ == "__main__":
    interactive_resumable_workflow()