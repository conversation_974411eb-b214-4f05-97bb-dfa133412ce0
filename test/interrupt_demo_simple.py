#!/usr/bin/env python3
"""
简化的interrupt机制演示
重点展示6.2章节的核心概念
"""
import os
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class ReviewState(TypedDict):
    messages: Annotated[list, add_messages]
    risk_level: str
    requires_review: bool
    human_decision: str

def create_interrupt_demo():
    """创建interrupt演示应用"""
    
    def analyze_request(state: ReviewState):
        """分析用户请求的风险"""
        user_request = state["messages"][-1].content
        print(f"🔍 分析请求: {user_request}")
        
        # 简单的风险评估
        if any(word in user_request.lower() for word in ["删除", "删掉", "清空"]):
            risk = "高"
            needs_review = True
        elif any(word in user_request.lower() for word in ["发送", "推送", "群发"]):
            risk = "中"
            needs_review = True
        else:
            risk = "低"
            needs_review = False
            
        print(f"📊 风险评估: {risk}风险, 需要审核: {needs_review}")
        
        return {
            "risk_level": risk,
            "requires_review": needs_review
        }
    
    def human_review(state: ReviewState):
        """人工审核节点 - 这里会使用interrupt"""
        if state.get("requires_review", False):
            review_data = {
                "request": state["messages"][-1].content,
                "risk": state.get("risk_level", "未知"),
                "需要决策": "approved/rejected/修改"
            }
            
            print(f"⏸️ 触发INTERRUPT - 等待人工决策")
            print(f"📋 审核信息: {review_data}")
            
            # 这里是interrupt的关键 - 暂停执行等待人工输入
            return interrupt(review_data)
        
        # 低风险直接通过
        print("✅ 低风险，自动通过")
        return {"human_decision": "auto_approved"}
    
    def execute_action(state: ReviewState):
        """执行最终动作"""
        decision = state.get("human_decision", "auto_approved")
        user_request = state["messages"][-1].content
        
        print(f"⚡ 执行阶段，人工决策: {decision}")
        
        if decision == "rejected":
            response = AIMessage(content="❌ 抱歉，该操作已被人工审核拒绝。")
        elif decision == "auto_approved" or decision == "approved":
            # 这里可以接入真实的GLM-4.5
            if "天气" in user_request:
                response = AIMessage(content="🌤️ 今天天气不错，适合外出活动！")
            elif "删除" in user_request:
                response = AIMessage(content="⚠️ 我理解您想清理文件，但建议先备份重要数据。")
            else:
                response = AIMessage(content="✅ 我已经处理了您的请求。")
        else:
            # 自定义反馈
            response = AIMessage(content=f"📝 根据您的指导「{decision}」，我调整了处理方式。")
        
        return {"messages": [response]}
    
    # 构建状态图
    graph = StateGraph(ReviewState)
    graph.add_node("analyze", analyze_request)
    graph.add_node("review", human_review)
    graph.add_node("execute", execute_action)
    
    # 设置流程
    graph.add_edge(START, "analyze")
    graph.add_edge("analyze", "review")
    graph.add_edge("review", "execute")
    graph.add_edge("execute", END)
    
    return graph.compile()

def demo_interrupt_mechanism():
    """演示interrupt机制"""
    print("🚀 6.2章节 - Interrupt机制演示")
    print("=" * 50)
    
    app = create_interrupt_demo()
    
    # 测试用例
    test_cases = [
        "你好！",  # 低风险
        "帮我删除所有文件",  # 高风险 
        "发送通知给团队"  # 中风险
    ]
    
    for i, request in enumerate(test_cases, 1):
        print(f"\n📝 测试案例 {i}: {request}")
        print("-" * 30)
        
        try:
            # 第一次调用 - 可能遇到interrupt
            result = app.invoke({
                "messages": [HumanMessage(content=request)],
                "risk_level": "",
                "requires_review": False,
                "human_decision": ""
            })
            
            # 如果没有interrupt，直接显示结果
            if "messages" in result and result["messages"]:
                print(f"✅ 最终回复: {result['messages'][-1].content}")
            else:
                print("🤔 程序执行完成，但没有消息输出")
                
        except Exception as e:
            error_msg = str(e)
            if "interrupt" in error_msg.lower():
                print(f"🔴 遇到INTERRUPT: {error_msg}")
                print("💡 在真实场景中，这里会等待人工输入...")
                
                # 模拟人工决策
                if "删除" in request:
                    mock_decision = "rejected"
                    print(f"🤖 模拟人工决策: {mock_decision}")
                elif "发送" in request:
                    mock_decision = "请先确认内容再发送"
                    print(f"🤖 模拟人工决策: {mock_decision}")
                else:
                    mock_decision = "approved"
                    print(f"🤖 模拟人工决策: {mock_decision}")
                
                # 继续执行（传入人工决策）
                try:
                    final_result = app.invoke({
                        "messages": [HumanMessage(content=request)],
                        "risk_level": "",
                        "requires_review": False,
                        "human_decision": mock_decision
                    })
                    print(f"✅ 最终回复: {final_result['messages'][-1].content}")
                except Exception as e2:
                    print(f"❌ 继续执行失败: {e2}")
            else:
                print(f"❌ 其他错误: {error_msg}")
        
        print("=" * 50)

if __name__ == "__main__":
    demo_interrupt_mechanism()