#!/usr/bin/env python3
"""
测试第7章：让对话更聪明
验证系统提示词、结构化输出和Token优化功能
"""
import os
import uuid
from typing_extensions import TypedDict
from typing import Annotated, List, Optional
from enum import Enum
from pydantic import BaseModel, Field

from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

# 检查是否有智谱API密钥
def check_api_key():
    """检查API密钥配置"""
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-6:]})")
        return True
    else:
        print("⚠️ 未配置智谱API密钥，将使用模拟模式")
        return False

class SmartChatState(TypedDict):
    messages: Annotated[list, add_messages]
    role: str
    context: dict
    response_format: str

# 角色定义
ROLES = {
    "技术专家": {
        "system_prompt": """
你是一位资深的技术专家，拥有15年的软件开发经验。
专业领域：Python、JavaScript、Go等编程语言
回答风格：技术准确，逻辑清晰，提供最佳实践建议
""",
        "response_format": "technical"
    },
    
    "产品经理": {
        "system_prompt": """
你是一位经验丰富的产品经理，擅长将技术转化为商业价值。
核心能力：用户需求分析、产品规划和路线图
沟通风格：以用户价值为中心，用商业语言解释技术概念
""",
        "response_format": "business"
    },
    
    "教学助手": {
        "system_prompt": """
你是一位耐心的编程教学助手，专门帮助初学者学习编程。
教学理念：循序渐进，由浅入深，用生活化的比喻解释抽象概念
教学方法：先解释概念，再给出示例，将复杂问题分解为简单步骤
""",
        "response_format": "educational"
    }
}

def create_test_role_based_chatbot():
    """创建测试版角色聊天机器人"""
    
    def role_selection_node(state: SmartChatState):
        """角色选择节点"""
        last_message = state["messages"][-1].content.lower()
        
        if any(keyword in last_message for keyword in ["代码", "编程", "bug", "算法"]):
            selected_role = "技术专家"
        elif any(keyword in last_message for keyword in ["产品", "用户", "需求", "商业"]):
            selected_role = "产品经理"
        elif any(keyword in last_message for keyword in ["学习", "教", "不懂", "初学"]):
            selected_role = "教学助手"
        else:
            selected_role = "技术专家"
            
        return {
            "role": selected_role,
            "response_format": ROLES[selected_role]["response_format"]
        }
    
    def smart_response_node(state: SmartChatState):
        """智能回复节点"""
        role = state.get("role", "技术专家")
        role_config = ROLES[role]
        
        # 构造角色化的系统提示
        system_message = SystemMessage(content=role_config["system_prompt"])
        messages = [system_message] + state["messages"]
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
                response = llm.invoke(messages)
                role_tagged_response = f"[{role}] {response.content}"
                response.content = role_tagged_response
                return {"messages": [response]}
            except Exception as e:
                print(f"⚠️ GLM-4.5调用失败: {e}")
                # 返回模拟回复
                return {"messages": [AIMessage(content=f"[{role}] 模拟回复：我是{role}，正在处理您的问题...")]}
        else:
            # 模拟回复
            mock_responses = {
                "技术专家": "这是一个技术问题，建议使用Python解决，具体方案如下...",
                "产品经理": "从产品角度来看，我们需要考虑用户价值和商业目标...",
                "教学助手": "让我来帮您理解这个概念，我们先从基础开始..."
            }
            return {"messages": [AIMessage(content=f"[{role}] 模拟回复：{mock_responses[role]}")]}
    
    # 构建图
    graph = StateGraph(SmartChatState)
    graph.add_node("role_selection", role_selection_node)
    graph.add_node("smart_response", smart_response_node)
    
    graph.add_edge(START, "role_selection")
    graph.add_edge("role_selection", "smart_response")
    graph.add_edge("smart_response", END)
    
    return graph.compile()

# 结构化输出模型
class Priority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class TaskItem(BaseModel):
    """任务项目模型"""
    title: str = Field(description="任务标题")
    description: str = Field(description="任务描述")
    priority: Priority = Field(description="优先级")
    estimated_hours: Optional[float] = Field(description="预估工时", ge=0)
    tags: List[str] = Field(default=[], description="标签列表")

class TaskList(BaseModel):
    """任务列表模型"""
    project_name: str = Field(description="项目名称")
    tasks: List[TaskItem] = Field(description="任务列表")
    total_estimated_hours: float = Field(description="总预估工时")

def create_test_structured_chatbot():
    """创建测试版结构化输出聊天机器人"""
    
    def task_extraction_node(state: SmartChatState):
        """任务提取节点"""
        user_message = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if api_key:
            try:
                from langchain_core.output_parsers import PydanticOutputParser
                from langchain_community.chat_models import ChatZhipuAI
                
                parser = PydanticOutputParser(pydantic_object=TaskList)
                format_instructions = parser.get_format_instructions()
                
                system_prompt = f"""
你是一个项目管理助手，请将用户需求转换为结构化的任务列表。
{format_instructions}
"""
                
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
                full_prompt = f"{system_prompt}\n\n用户需求：{user_message}"
                
                response = llm.invoke([SystemMessage(content=full_prompt)])
                
                # 清理响应内容中的markdown格式
                cleaned_content = response.content.strip()
                if cleaned_content.startswith("```json"):
                    cleaned_content = cleaned_content.replace("```json", "").replace("```", "").strip()
                elif cleaned_content.startswith("```"):
                    cleaned_content = cleaned_content.replace("```", "").strip()
                
                parsed_result = parser.parse(cleaned_content)
                formatted_output = format_task_list(parsed_result)
                
                return {
                    "messages": [AIMessage(content=formatted_output)],
                    "structured_data": parsed_result.dict()
                }
                
            except Exception as e:
                print(f"⚠️ 结构化解析失败: {e}")
                # 返回模拟结构化输出
                return {"messages": [AIMessage(content=create_mock_task_list(user_message))]}
        else:
            # 模拟结构化输出
            return {"messages": [AIMessage(content=create_mock_task_list(user_message))]}
    
    def format_task_list(task_list: TaskList) -> str:
        """格式化任务列表"""
        output = f"📋 项目：{task_list.project_name}\n"
        output += f"⏱️ 总预估工时：{task_list.total_estimated_hours} 小时\n\n"
        
        for i, task in enumerate(task_list.tasks, 1):
            priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}
            emoji = priority_emoji.get(task.priority.value, "")
            
            output += f"{i}. {emoji} {task.title}\n"
            output += f"   📝 {task.description}\n"
            output += f"   ⏱️ 预估：{task.estimated_hours or 0} 小时\n"
            
            if task.tags:
                output += f"   🏷️ 标签：{', '.join(task.tags)}\n"
            output += "\n"
        
        return output
    
    def create_mock_task_list(user_message: str) -> str:
        """创建模拟任务列表"""
        if "商城" in user_message or "电商" in user_message:
            return """📋 项目：在线商城开发

⏱️ 总预估工时：120 小时

1. 🟡 用户注册系统
   📝 实现用户注册、登录、密码重置功能
   ⏱️ 预估：20 小时
   🏷️ 标签：用户管理, 认证

2. 🔴 商品展示模块
   📝 商品列表、详情页、分类筛选功能
   ⏱️ 预估：30 小时
   🏷️ 标签：前端, 商品管理

3. 🟡 购物车功能
   📝 添加商品到购物车、数量修改、删除
   ⏱️ 预估：25 小时
   🏷️ 标签：前端, 状态管理

4. 🔴 支付系统
   📝 集成支付网关，订单处理流程
   ⏱️ 预估：45 小时
   🏷️ 标签：支付, 安全, 后端

模拟结构化输出 - 实际使用时将调用GLM-4.5生成
"""
        else:
            return f"📋 项目：{user_message}\n\n模拟任务分解结果\n需要配置API密钥以获得完整功能"
    
    # 构建图
    graph = StateGraph(SmartChatState)
    graph.add_node("task_extraction", task_extraction_node)
    
    graph.add_edge(START, "task_extraction")
    graph.add_edge("task_extraction", END)
    
    return graph.compile()

def test_role_based_system():
    """测试角色扮演系统"""
    print("\n🎭 测试角色扮演系统")
    print("=" * 60)
    
    bot = create_test_role_based_chatbot()
    
    test_questions = [
        "我是编程新手，如何开始学习Python？",
        "我们的产品需要添加用户认证功能，有什么建议？",
        "这段代码的时间复杂度是多少？def find_max(arr): return max(arr)"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. 问题: {question}")
        try:
            result = bot.invoke({
                "messages": [HumanMessage(content=question)],
                "role": "",
                "context": {},
                "response_format": ""
            })
            response = result['messages'][-1].content
            print(f"💬 回答: {response[:150]}{'...' if len(response) > 150 else ''}")
        except Exception as e:
            print(f"❌ 错误: {e}")

def test_structured_output():
    """测试结构化输出"""
    print("\n📋 测试结构化输出系统")
    print("=" * 60)
    
    bot = create_test_structured_chatbot()
    
    task_request = "我需要开发一个在线商城，包括用户注册、商品展示、购物车和支付功能"
    print(f"📝 需求: {task_request}")
    
    try:
        result = bot.invoke({
            "messages": [HumanMessage(content=task_request)],
            "role": "",
            "context": {},
            "response_format": ""
        })
        print(f"\n📊 任务分解结果:\n{result['messages'][-1].content}")
    except Exception as e:
        print(f"❌ 错误: {e}")

def test_context_compression():
    """测试上下文压缩"""
    print("\n🗜️ 测试上下文压缩")
    print("=" * 60)
    
    # 创建长对话历史
    long_messages = []
    for i in range(10):
        long_messages.append(HumanMessage(content=f"这是第{i+1}个用户消息，内容比较长，包含了很多详细的信息和技术细节..."))
        long_messages.append(AIMessage(content=f"这是第{i+1}个AI回复，提供了详细的解答和建议..."))
    
    class SimpleContextManager:
        def __init__(self, max_tokens=1000):
            self.max_tokens = max_tokens
        
        def estimate_tokens(self, text: str) -> int:
            return len(text) // 4
        
        def compress_messages(self, messages: list) -> list:
            total_tokens = sum(self.estimate_tokens(msg.content) for msg in messages)
            print(f"📊 原始消息数: {len(messages)}")
            print(f"📊 估算token数: {total_tokens}")
            
            if total_tokens <= self.max_tokens:
                print("✅ 无需压缩")
                return messages
            
            # 保留最近的消息
            compressed = messages[-5:]  # 保留最后5条
            print(f"🗜️ 压缩后消息数: {len(compressed)}")
            return compressed
    
    manager = SimpleContextManager()
    compressed = manager.compress_messages(long_messages)
    
    print("📈 压缩效果:")
    print(f"  • 原始: {len(long_messages)} 条消息")
    print(f"  • 压缩后: {len(compressed)} 条消息")
    print(f"  • 压缩比: {len(compressed)/len(long_messages)*100:.1f}%")

def main():
    """主测试函数"""
    print("🤖 第7章代码测试：让对话更聪明")
    print("=" * 80)
    
    # 检查API配置
    has_api_key = check_api_key()
    
    if not has_api_key:
        print("\n💡 提示：设置 ZHIPUAI_API_KEY 环境变量可体验完整功能")
    
    # 运行测试
    test_role_based_system()
    test_structured_output()
    test_context_compression()
    
    print("\n" + "=" * 80)
    print("✅ 第7章测试完成！")
    
    if has_api_key:
        print("🎯 所有功能均已验证通过")
    else:
        print("🎯 模拟模式测试通过，配置API密钥可获得完整体验")

if __name__ == "__main__":
    main()