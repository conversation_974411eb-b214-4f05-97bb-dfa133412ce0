#!/usr/bin/env python3
"""
交互式interrupt演示 - 6.2章节
让用户亲自体验interrupt机制的完整流程
"""
import os
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class ReviewState(TypedDict):
    messages: Annotated[list, add_messages]
    pending_action: str
    confidence_score: float
    requires_approval: bool
    human_feedback: str

def create_interactive_chatbot():
    """创建真正交互的人机协作机器人"""
    
    def ai_analysis_node(state: ReviewState):
        """AI 分析节点"""
        last_message = state["messages"][-1].content
        print(f"🤖 AI正在分析: {last_message}")
        
        # 智谱GLM-4.5分析（如果有API key）
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(
                    model="glm-4.5",
                    temperature=0.1,
                    api_key=api_key
                )
                
                analysis_prompt = f"""
                分析以下用户请求，评估其风险等级和你的置信度：
                用户请求：{last_message}
                
                请返回：
                1. 建议的操作
                2. 风险等级（低/中/高）
                3. 置信度（0-1之间的数字）
                
                格式：操作|风险等级|置信度
                """
                
                response = llm.invoke([HumanMessage(content=analysis_prompt)])
                analysis = response.content.strip()
                print(f"🧠 GLM-4.5分析结果: {analysis}")
                
            except Exception as e:
                print(f"⚠️ API调用失败: {e}")
                analysis = None
        else:
            analysis = None
        
        # 解析或使用默认分析
        if analysis:
            try:
                parts = analysis.split('|')
                action = parts[0].strip()
                risk_level = parts[1].strip()
                confidence = float(parts[2].strip())
            except:
                action, risk_level, confidence = "默认回复", "中", 0.5
        else:
            # 基于关键词的分析
            if any(word in last_message.lower() for word in ["删除", "删掉", "清空", "清理"]):
                action, risk_level, confidence = "删除操作", "高", 0.9
            elif any(word in last_message.lower() for word in ["发送", "群发", "推送", "邮件"]):
                action, risk_level, confidence = "发送操作", "高", 0.8
            elif any(word in last_message.lower() for word in ["复杂", "不确定", "不知道"]):
                action, risk_level, confidence = "咨询回复", "中", 0.4
            else:
                action, risk_level, confidence = "常规回复", "低", 0.9
        
        # 判断是否需要人工审核
        needs_approval = (
            confidence < 0.7 or 
            risk_level == "高" or
            any(word in last_message.lower() for word in ["删除", "发送", "支付", "转账"])
        )
        
        print(f"📊 分析结果:")
        print(f"   • 建议操作: {action}")
        print(f"   • 风险等级: {risk_level}")
        print(f"   • 置信度: {confidence:.2f}")
        print(f"   • 需要审核: {'是' if needs_approval else '否'}")
        
        return {
            "pending_action": action,
            "confidence_score": confidence,
            "requires_approval": needs_approval
        }
    
    def human_review_node(state: ReviewState):
        """人工审核节点 - 真正的interrupt"""
        if state.get("requires_approval", False):
            print("\n" + "="*50)
            print("⏸️  INTERRUPT 触发！AI请求人工干预")
            print("="*50)
            
            review_info = {
                "用户请求": state["messages"][-1].content,
                "AI建议": state.get("pending_action", "未知"),
                "置信度": state.get("confidence_score", 0),
                "原因": "高风险或低置信度操作需要人工确认"
            }
            
            print("📋 需要审核的信息:")
            for key, value in review_info.items():
                print(f"   • {key}: {value}")
            print("\n💡 请做出决策:")
            print("   • 输入 'approved' - 批准执行")
            print("   • 输入 'rejected' - 拒绝执行")
            print("   • 输入其他内容 - 自定义指导意见")
            
            # 这里是真正的interrupt - 暂停并等待输入
            return interrupt(review_info)
        
        # 低风险，自动通过
        print("✅ 低风险操作，自动批准")
        return {"human_feedback": "auto_approved"}
    
    def execution_node(state: ReviewState):
        """执行节点"""
        human_feedback = state.get("human_feedback", "auto_approved")
        user_request = state["messages"][-1].content
        
        print(f"\n⚡ 执行阶段")
        print(f"👤 人工决策: {human_feedback}")
        
        if human_feedback == "rejected":
            response = AIMessage(content="❌ 抱歉，该操作已被人工审核拒绝，为了安全考虑暂时无法执行。")
        elif human_feedback in ["auto_approved", "approved"]:
            # 正常执行
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(
                        model="glm-4.5",
                        temperature=0.7,
                        api_key=api_key
                    )
                    response = llm.invoke(state["messages"])
                except Exception as e:
                    print(f"⚠️ API调用失败: {e}")
                    response = AIMessage(content="✅ 我已经处理了您的请求（模拟回复）。")
            else:
                # 模拟回复
                if "天气" in user_request:
                    response = AIMessage(content="🌤️ 今天天气晴朗，适合外出活动！")
                elif "删除" in user_request:
                    response = AIMessage(content="⚠️ 我理解您的需求，但删除操作需要格外小心，建议先备份重要数据。")
                else:
                    response = AIMessage(content="✅ 我已经处理了您的请求。")
        else:
            # 根据人工指导调整
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(
                        model="glm-4.5",
                        temperature=0.7,
                        api_key=api_key
                    )
                    adjusted_prompt = f"""
                    用户原始请求：{user_request}
                    人工指导意见：{human_feedback}
                    请根据人工指导调整你的回复。
                    """
                    response = llm.invoke([HumanMessage(content=adjusted_prompt)])
                except Exception as e:
                    print(f"⚠️ API调用失败: {e}")
                    response = AIMessage(content=f"📝 根据您的指导「{human_feedback}」，我已经调整了处理方式。")
            else:
                response = AIMessage(content=f"📝 根据您的指导「{human_feedback}」，我已经调整了处理方式。")
        
        print(f"🎯 最终回复: {response.content}")
        return {"messages": [response]}
    
    # 构建图
    graph = StateGraph(ReviewState)
    graph.add_node("analysis", ai_analysis_node)
    graph.add_node("review", human_review_node) 
    graph.add_node("execution", execution_node)
    
    graph.add_edge(START, "analysis")
    graph.add_edge("analysis", "review")
    graph.add_edge("review", "execution")
    graph.add_edge("execution", END)
    
    return graph.compile()

def interactive_demo():
    """交互式演示"""
    print("🚀 6.2章节交互式演示：真正的interrupt体验")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-4:]})")
    else:
        print("⚠️ 未配置智谱API，将使用模拟分析")
    
    app = create_interactive_chatbot()
    print("✅ 人机协作机器人已启动\n")
    
    print("📝 建议测试的请求类型:")
    print("• 低风险: '你好' '今天天气怎么样'")
    print("• 高风险: '删除所有文件' '发送邮件给所有客户'")
    print("• 低置信度: '这个复杂问题我不确定'")
    print("• 输入 'quit' 退出")
    
    while True:
        print("\n" + "-" * 50)
        user_input = input("👤 请输入您的请求: ").strip()
        
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
        
        if not user_input:
            continue
            
        try:
            print(f"\n🔄 处理请求: {user_input}")
            
            # 执行工作流
            result = app.invoke({
                "messages": [HumanMessage(content=user_input)],
                "pending_action": "",
                "confidence_score": 0.0,
                "requires_approval": False,
                "human_feedback": ""
            })
            
            if "messages" in result and result["messages"]:
                print(f"\n✅ 处理完成!")
            else:
                print("🤔 执行完成，但未生成回复")
                
        except Exception as e:
            error_str = str(e)
            if "interrupt" in error_str.lower():
                print("\n⏸️ 遇到INTERRUPT - 需要人工决策!")
                
                # 获取人工决策
                decision = input("\n👤 请输入您的决策: ").strip()
                
                if decision:
                    try:
                        # 继续执行，传入人工决策
                        final_result = app.invoke({
                            "messages": [HumanMessage(content=user_input)],
                            "pending_action": "",
                            "confidence_score": 0.0,
                            "requires_approval": False,
                            "human_feedback": decision
                        })
                        print(f"\n✅ 根据您的决策，处理完成!")
                    except Exception as e2:
                        print(f"❌ 继续执行失败: {e2}")
                else:
                    print("⚠️ 未输入决策，操作取消")
            else:
                print(f"❌ 执行错误: {error_str}")

if __name__ == "__main__":
    interactive_demo()