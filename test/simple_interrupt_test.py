#!/usr/bin/env python3
"""
简单的interrupt功能测试
验证人机协作的核心机制
"""
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class SimpleState(TypedDict):
    messages: Annotated[list, add_messages]
    needs_review: bool
    decision: str

def create_simple_interrupt_test():
    """创建简单的interrupt测试"""
    
    def check_node(state: SimpleState):
        """检查节点"""
        message = state["messages"][-1].content
        print(f"🔍 检查消息: {message}")
        
        # 判断是否需要审核
        needs_review = any(keyword in message for keyword in ["删除", "发送", "支付"])
        
        if needs_review:
            print("⚠️ 需要人工审核，触发interrupt")
            # 这里应该触发interrupt
            return interrupt({
                "message": message,
                "reason": "需要人工审核",
                "options": ["approved", "rejected"]
            })
        else:
            print("✅ 无需审核")
            return {"needs_review": False}
    
    def response_node(state: SimpleState):
        """回复节点"""
        decision = state.get("decision", "auto_approved")
        message = state["messages"][-1].content
        
        print(f"💬 生成回复，决策: {decision}")
        
        if decision == "rejected":
            response = AIMessage(content="操作被拒绝")
        else:
            response = AIMessage(content=f"已处理: {message}")
        
        return {"messages": [response]}
    
    # 构建图
    graph = StateGraph(SimpleState)
    graph.add_node("check", check_node)
    graph.add_node("respond", response_node)
    
    graph.add_edge(START, "check")
    graph.add_edge("check", "respond")
    graph.add_edge("respond", END)
    
    return graph.compile()

def test_interrupt_mechanism():
    """测试interrupt机制"""
    print("🧪 测试interrupt机制")
    print("=" * 50)
    
    app = create_simple_interrupt_test()
    
    # 测试用例
    test_cases = [
        ("普通消息", "你好，今天天气怎么样？"),
        ("需要审核", "帮我删除所有文件"),
        ("发送操作", "发送邮件给所有用户")
    ]
    
    for test_name, message in test_cases:
        print(f"\n📝 测试: {test_name}")
        print(f"💬 消息: {message}")
        
        try:
            result = app.invoke({
                "messages": [HumanMessage(content=message)],
                "needs_review": False,
                "decision": ""
            })
            
            print(f"✅ 结果: {result['messages'][-1].content}")
            
        except Exception as e:
            if "interrupt" in str(e).lower():
                print(f"⏸️ 触发interrupt: {e}")
                
                # 模拟人工决策
                if "删除" in message:
                    decision = "rejected"
                else:
                    decision = "approved"
                
                print(f"👤 人工决策: {decision}")
                
                # 继续执行（这里需要正确的状态传递）
                try:
                    # 注意：这里的状态传递可能需要调整
                    final_result = app.invoke({
                        "messages": [HumanMessage(content=message)],
                        "needs_review": True,
                        "decision": decision
                    })
                    print(f"✅ 最终结果: {final_result['messages'][-1].content}")
                except Exception as e2:
                    print(f"❌ 恢复执行失败: {e2}")
            else:
                print(f"❌ 其他错误: {e}")
        
        print("-" * 30)

if __name__ == "__main__":
    test_interrupt_mechanism()