#!/usr/bin/env python3
"""
基于官方示例的简洁版人机协作演示
纯粹的interrupt机制，无复杂业务逻辑
"""
import os
import uuid
from typing_extensions import TypedDict
from langgraph.types import interrupt, Command
from langgraph.graph import StateGraph, START
from langchain_core.messages import HumanMessage

class WorkflowState(TypedDict):
    user_text: str

def create_resumable_workflow():
    """创建可恢复的工作流（基于官方示例）"""
    
    def human_node(state: WorkflowState):
        """人工干预节点 - 基于官方interrupt模式"""
        import os
        
        # 使用interrupt请求人工修改
        revised_text = interrupt({
            "text_to_revise": state["user_text"],
            "instruction": "请修改以下文本"
        })
        
        # 可选：使用GLM-4.5处理修改后的文本
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key)
                
                prompt = f"请优化以下文本：{revised_text}"
                response = llm.invoke([HumanMessage(content=prompt)])
                final_text = response.content
            except Exception as e:
                print(f"⚠️ GLM-4.5调用失败: {e}")
                final_text = revised_text
        else:
            final_text = revised_text
        
        return {"user_text": final_text}
    
    # 构建图 - 基于官方示例
    from langgraph.checkpoint.memory import MemorySaver
    
    graph = StateGraph(WorkflowState)
    graph.add_node("human_node", human_node)
    graph.add_edge(START, "human_node")
    
    # checkpointer是interrupt机制的必需组件
    checkpointer = MemorySaver()
    return graph.compile(checkpointer=checkpointer)

def demo_resumable_workflow():
    """演示人工干预工作流（基于官方示例）"""
    import uuid
    from langgraph.types import Command
    
    print("🔄 Human-in-the-Loop 演示（基于官方interrupt模式）")
    print("📝 演示文本修改和人工干预流程\n")
    
    # 创建应用
    app = create_resumable_workflow()
    print("✅ 成功创建工作流应用")
    
    # 配置线程ID（interrupt机制必需）
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    # 初始文本
    original_text = "今天是个好天气，适合出去走走。"
    print(f"📝 原始文本: {original_text}")
    
    # 运行工作流直到interrupt
    print("\n🚀 运行工作流...")
    result = app.invoke({"user_text": original_text}, config=config)
    
    # 检查是否触发了interrupt
    if "__interrupt__" in result:
        print("⏸️ 触发interrupt，请求人工干预！")
        interrupt_info = result["__interrupt__"][0]
        print(f"📋 请求内容: {interrupt_info.value}")
        
        # 模拟人工修改
        human_revision = "今天阳光明媚，天气非常好，很适合外出散步和运动。"
        print(f"\n✏️ 人工修改为: {human_revision}")
        
        # 使用Command恢复执行
        print("🔄 恢复工作流执行...")
        final_result = app.invoke(Command(resume=human_revision), config=config)
        print(f"\n✅ 最终文本: {final_result['user_text']}")
    else:
        # 直接完成（不应该发生）
        print(f"✅ 直接完成: {result['user_text']}")
    
    print("\n🎉 演示完成！展示了官方interrupt模式的核心机制。")

if __name__ == "__main__":
    print("🚀 测试官方风格的简洁版人机协作")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置")
    else:
        print("⚠️ 未配置智谱API，将使用基础模式")
    print()
    
    demo_resumable_workflow()
    
    print("\n🎯 总结:")
    print("• 完全基于官方interrupt示例 ✅")
    print("• 纯粹的interrupt机制演示 ✅")
    print("• 无复杂业务逻辑干扰 ✅")
    print("• 简洁清晰的代码结构 ✅")
    print("• 可选的GLM-4.5集成 ✅")