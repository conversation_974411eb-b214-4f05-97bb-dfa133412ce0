#!/usr/bin/env python3
"""
测试第8章：多智能体协作
验证多专家系统、内容创作流水线和客服质检协作功能
"""
import os
import time
from datetime import datetime
from typing_extensions import TypedDict
from typing import Annotated, Dict, List, Any

from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

# 检查API密钥
def check_api_key():
    """检查API密钥配置"""
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-6:]})")
        return True
    else:
        print("⚠️ 未配置智谱API密钥，将使用模拟模式")
        return False

# ===== 多专家协作系统 =====
class MultiAgentState(TypedDict):
    messages: Annotated[list, add_messages]
    current_agent: str
    task_type: str
    agent_outputs: dict
    collaboration_history: list

def create_test_multi_expert_system():
    """创建测试版多专家协作系统"""
    
    def task_router_node(state: MultiAgentState):
        """任务路由节点"""
        last_message = state["messages"][-1].content.lower()
        
        if any(keyword in last_message for keyword in ["代码", "编程", "bug", "算法"]):
            task_type = "technical"
        elif any(keyword in last_message for keyword in ["数据", "分析", "统计", "图表"]):
            task_type = "analytics"
        elif any(keyword in last_message for keyword in ["翻译", "语言", "英文", "中文"]):
            task_type = "translation"
        elif any(keyword in last_message for keyword in ["项目", "计划", "管理", "进度"]):
            task_type = "project"
        else:
            task_type = "general"
            
        return {
            "task_type": task_type,
            "current_agent": f"{task_type}_expert"
        }
    
    def call_expert_with_retry(state: MultiAgentState, system_prompt: str, expert_name: str, temperature: float = 0.5):
        """带重试的专家调用"""
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=temperature, api_key=api_key, timeout=35)
                messages = [SystemMessage(content=system_prompt)] + state["messages"]
                response = llm.invoke(messages)
                
                agent_outputs = state.get("agent_outputs", {})
                agent_outputs[expert_name] = response.content
                
                return {
                    "messages": [response],
                    "agent_outputs": agent_outputs,
                    "current_agent": expert_name
                }
            except Exception as e:
                print(f"⚠️ {expert_name}调用失败: {e}")
                # 模拟回复
                mock_responses = {
                    "technical_expert": "这是一个技术问题，我建议使用Python来解决...",
                    "analytics_expert": "从数据分析角度来看，我们需要先收集基础数据...",
                    "translation_expert": "这段文本的翻译如下：...",
                    "project_manager": "项目计划建议如下：1. 需求分析 2. 设计阶段 3. 开发实施...",
                    "general_assistant": "我来帮您解答这个问题..."
                }
                mock_content = mock_responses.get(expert_name, "我正在处理您的问题...")
                
                return {
                    "messages": [AIMessage(content=f"[{expert_name}] 模拟回复：{mock_content}")],
                    "agent_outputs": {**state.get("agent_outputs", {}), expert_name: mock_content},
                    "current_agent": expert_name
                }
        else:
            # 纯模拟模式
            mock_responses = {
                "technical_expert": "技术专家建议：优化算法，提升性能，确保代码质量",
                "analytics_expert": "数据分析师建议：收集数据，建立模型，生成可视化报告",
                "translation_expert": "翻译专家建议：保持原意，语言地道，文化适应",
                "project_manager": "项目经理建议：制定计划，分配资源，控制进度和质量",
                "general_assistant": "通用助手：我来提供全面的帮助和建议"
            }
            mock_content = mock_responses.get(expert_name, "专家回复")
            
            return {
                "messages": [AIMessage(content=f"[{expert_name}] 模拟回复：{mock_content}")],
                "agent_outputs": {**state.get("agent_outputs", {}), expert_name: mock_content},
                "current_agent": expert_name
            }
    
    def technical_expert_node(state: MultiAgentState):
        system_prompt = "你是技术专家，专门解决编程和技术问题。提供准确、实用的技术解决方案。"
        return call_expert_with_retry(state, system_prompt, "technical_expert", 0.3)
    
    def analytics_expert_node(state: MultiAgentState):
        system_prompt = "你是数据分析专家，专门处理数据分析和统计问题。提供基于数据的洞察。"
        return call_expert_with_retry(state, system_prompt, "analytics_expert", 0.4)
    
    def translation_expert_node(state: MultiAgentState):
        system_prompt = "你是翻译专家，精通多种语言翻译。提供准确、地道的翻译服务。"
        return call_expert_with_retry(state, system_prompt, "translation_expert", 0.2)
    
    def project_manager_node(state: MultiAgentState):
        system_prompt = "你是项目经理，专门负责项目规划和管理。提供结构化的项目建议。"
        return call_expert_with_retry(state, system_prompt, "project_manager", 0.5)
    
    def general_assistant_node(state: MultiAgentState):
        system_prompt = "你是通用AI助手，能够处理各种常见问题。提供友好、全面的帮助。"
        return call_expert_with_retry(state, system_prompt, "general_assistant", 0.7)
    
    def agent_router(state: MultiAgentState) -> str:
        task_type = state.get("task_type", "general")
        routing_map = {
            "technical": "technical_expert",
            "analytics": "analytics_expert",
            "translation": "translation_expert",
            "project": "project_manager",
            "general": "general_assistant"
        }
        return routing_map.get(task_type, "general_assistant")
    
    # 构建图
    graph = StateGraph(MultiAgentState)
    
    graph.add_node("task_router", task_router_node)
    graph.add_node("technical_expert", technical_expert_node)
    graph.add_node("analytics_expert", analytics_expert_node)
    graph.add_node("translation_expert", translation_expert_node)
    graph.add_node("project_manager", project_manager_node)
    graph.add_node("general_assistant", general_assistant_node)
    
    graph.add_edge(START, "task_router")
    graph.add_conditional_edges(
        "task_router", agent_router,
        {
            "technical_expert": "technical_expert",
            "analytics_expert": "analytics_expert",
            "translation_expert": "translation_expert",
            "project_manager": "project_manager",
            "general_assistant": "general_assistant"
        }
    )
    
    for expert in ["technical_expert", "analytics_expert", "translation_expert", "project_manager", "general_assistant"]:
        graph.add_edge(expert, END)
    
    return graph.compile()

# ===== 客服质检系统 =====
class CustomerServiceState(TypedDict):
    messages: Annotated[list, add_messages]
    customer_info: Dict[str, Any]
    service_category: str
    urgency_level: str
    service_response: str
    quality_score: float
    quality_feedback: str
    escalation_needed: bool
    resolution_status: str

def create_test_customer_service_system():
    """创建测试版客服质检系统"""
    
    def customer_classifier_node(state: CustomerServiceState):
        """客户分类节点"""
        user_message = state["messages"][-1].content
        
        # 简化的分类逻辑
        category = "general"
        urgency = "medium"
        
        if any(word in user_message.lower() for word in ["紧急", "急", "马上", "立即"]):
            urgency = "high"
        if any(word in user_message.lower() for word in ["投诉", "不满", "问题", "错误"]):
            urgency = "high"
            category = "complaint"
        elif any(word in user_message.lower() for word in ["技术", "bug", "故障", "不能用"]):
            category = "technical"
        elif any(word in user_message.lower() for word in ["账单", "付款", "费用", "价格"]):
            category = "billing"
        
        return {
            "service_category": category,
            "urgency_level": urgency,
            "customer_info": {"classification": f"分类为{category}，紧急度{urgency}"}
        }
    
    def customer_service_agent_node(state: CustomerServiceState):
        """客服代表节点"""
        category = state.get("service_category", "general")
        urgency = state.get("urgency_level", "medium")
        user_message = state["messages"][-1].content
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                
                service_prompts = {
                    "technical": "你是技术支持专家，快速解决技术问题",
                    "billing": "你是账务专员，处理账单和付款问题",
                    "complaint": "你是客户关系专员，耐心处理投诉",
                    "general": "你是客服代表，提供友好专业的服务"
                }
                
                prompt = service_prompts.get(category, service_prompts["general"])
                if urgency == "high":
                    prompt += "，这是高优先级问题，请快速响应。"
                
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.4, api_key=api_key, timeout=35)
                messages = [
                    SystemMessage(content=prompt),
                    HumanMessage(content=user_message)
                ]
                response = llm.invoke(messages)
                service_response = response.content
                
            except Exception as e:
                print(f"⚠️ 客服调用失败: {e}")
                service_response = f"抱歉，系统繁忙，我们已记录您的问题，会尽快为您解决。"
        else:
            # 模拟客服回复
            mock_responses = {
                "technical": "感谢您联系技术支持。我已了解您的问题，建议您尝试重启应用，如问题仍存在请联系我们。",
                "billing": "您好，我来帮您查看账单问题。请提供您的账户信息，我会详细为您核实费用明细。",
                "complaint": "非常抱歉给您带来不便。我已记录您的反馈，会立即转交相关部门处理，并在24小时内回复您。",
                "general": "您好！很高兴为您服务。我已了解您的需求，会尽力帮您解决问题。"
            }
            service_response = mock_responses.get(category, "感谢您的咨询，我们会认真处理您的问题。")
        
        return {
            "service_response": service_response,
            "messages": [AIMessage(content=f"[客服代表] {service_response}")]
        }
    
    def quality_inspector_node(state: CustomerServiceState):
        """质检员节点"""
        service_response = state.get("service_response", "")
        category = state.get("service_category", "general")
        urgency = state.get("urgency_level", "medium")
        
        # 简化的质检逻辑
        quality_score = 8.5
        if urgency == "high" and len(service_response) < 50:
            quality_score = 6.5  # 高优先级问题回复太简短
        elif "抱歉" in service_response and "解决" in service_response:
            quality_score = 9.0  # 体现了道歉和解决意愿
        
        escalation_needed = (
            quality_score < 7.0 or 
            urgency == "high" or 
            category == "complaint"
        )
        
        quality_feedback = f"专业性：{quality_score}/10，友好性：良好，完整性：充分"
        
        return {
            "quality_score": quality_score,
            "quality_feedback": quality_feedback,
            "escalation_needed": escalation_needed,
            "messages": [AIMessage(content=f"[质检员] 质量评分：{quality_score}/10")]
        }
    
    def escalation_handler_node(state: CustomerServiceState):
        """升级处理节点"""
        if not state.get("escalation_needed", False):
            return {
                "resolution_status": "resolved",
                "messages": [AIMessage(content="[系统] 问题已正常处理完成")]
            }
        
        return {
            "resolution_status": "escalated",
            "messages": [AIMessage(content="[客服主管] 我来为您处理这个问题，确保您得到满意的解决方案。")]
        }
    
    def quality_router(state: CustomerServiceState) -> str:
        return "escalation" if state.get("escalation_needed", False) else "complete"
    
    # 构建客服系统图
    graph = StateGraph(CustomerServiceState)
    
    graph.add_node("classifier", customer_classifier_node)
    graph.add_node("service_agent", customer_service_agent_node)
    graph.add_node("quality_inspector", quality_inspector_node)
    graph.add_node("escalation_handler", escalation_handler_node)
    graph.add_node("complete", lambda state: {"resolution_status": "completed"})
    
    graph.add_edge(START, "classifier")
    graph.add_edge("classifier", "service_agent")
    graph.add_edge("service_agent", "quality_inspector")
    graph.add_conditional_edges(
        "quality_inspector", quality_router,
        {"escalation": "escalation_handler", "complete": "complete"}
    )
    graph.add_edge("escalation_handler", END)
    graph.add_edge("complete", END)
    
    return graph.compile()

def test_multi_expert_system():
    """测试多专家协作系统"""
    print("\n🎯 测试多专家协作系统")
    print("=" * 60)
    
    system = create_test_multi_expert_system()
    
    test_questions = [
        "我需要优化这段Python代码的性能",
        "帮我分析一下用户数据的趋势",
        "请将这段中文翻译成英文：人工智能改变世界",
        "制定一个新产品开发的项目计划",
        "今天天气怎么样？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n--- 测试 {i} ---")
        print(f"❓ 问题: {question}")
        
        try:
            result = system.invoke({
                "messages": [HumanMessage(content=question)],
                "current_agent": "",
                "task_type": "",
                "agent_outputs": {},
                "collaboration_history": []
            })
            
            print(f"🎯 专家: {result.get('current_agent', 'unknown').replace('_', ' ')}")
            print(f"📝 回复: {result['messages'][-1].content[:100]}...")
            
        except Exception as e:
            print(f"❌ 错误: {e}")

def test_customer_service_system():
    """测试客服质检系统"""
    print("\n📞 测试客服质检协作系统")
    print("=" * 60)
    
    system = create_test_customer_service_system()
    
    customer_issues = [
        "我的账单有问题，费用比平时高了很多",
        "软件一直崩溃，急需解决！",
        "我要投诉你们的服务态度",
        "请问如何使用新功能？"
    ]
    
    for i, issue in enumerate(customer_issues, 1):
        print(f"\n--- 客服案例 {i} ---")
        print(f"👤 客户: {issue}")
        
        try:
            result = system.invoke({
                "messages": [HumanMessage(content=issue)],
                "customer_info": {},
                "service_category": "",
                "urgency_level": "",
                "service_response": "",
                "quality_score": 0.0,
                "quality_feedback": "",
                "escalation_needed": False,
                "resolution_status": ""
            })
            
            print(f"📋 分类: {result.get('service_category', 'unknown')}")
            print(f"⚡ 紧急度: {result.get('urgency_level', 'unknown')}")
            print(f"⭐ 质量评分: {result.get('quality_score', 0)}/10")
            print(f"📊 状态: {result.get('resolution_status', 'unknown')}")
            
            # 显示客服回复
            for msg in result.get('messages', []):
                if isinstance(msg, AIMessage) and '[客服代表]' in msg.content:
                    print(f"💬 {msg.content[:80]}...")
                    break
                    
        except Exception as e:
            print(f"❌ 处理失败: {e}")

def test_collaboration_features():
    """测试协作特性"""
    print("\n🤝 测试协作特性")
    print("=" * 60)
    
    # 测试任务路由准确性
    test_cases = [
        ("我的Python代码有bug", "technical"),
        ("分析销售数据趋势", "analytics"),
        ("翻译这个文档", "translation"),
        ("制定项目计划", "project"),
        ("你好", "general")
    ]
    
    print("📍 任务路由测试:")
    for question, expected_type in test_cases:
        # 简化的路由逻辑测试
        actual_type = "general"
        if any(keyword in question.lower() for keyword in ["代码", "编程", "bug"]):
            actual_type = "technical"
        elif any(keyword in question.lower() for keyword in ["数据", "分析"]):
            actual_type = "analytics"
        elif any(keyword in question.lower() for keyword in ["翻译"]):
            actual_type = "translation"
        elif any(keyword in question.lower() for keyword in ["项目", "计划"]):
            actual_type = "project"
        
        status = "✅" if actual_type == expected_type else "❌"
        print(f"  {status} \"{question}\" → {actual_type} (期望: {expected_type})")
    
    # 测试客服分类准确性
    print("\n📞 客服分类测试:")
    service_cases = [
        ("账单有问题", "billing"),
        ("软件崩溃", "technical"),
        ("投诉服务", "complaint"),
        ("使用方法", "general")
    ]
    
    for issue, expected_category in service_cases:
        # 简化的分类逻辑测试
        actual_category = "general"
        if any(word in issue for word in ["账单", "费用"]):
            actual_category = "billing"
        elif any(word in issue for word in ["技术", "bug", "崩溃"]):
            actual_category = "technical"
        elif any(word in issue for word in ["投诉", "不满"]):
            actual_category = "complaint"
        
        status = "✅" if actual_category == expected_category else "❌"
        print(f"  {status} \"{issue}\" → {actual_category} (期望: {expected_category})")

def main():
    """主测试函数"""
    print("🤖 第8章代码测试：多智能体协作")
    print("=" * 80)
    
    # 检查API配置
    has_api_key = check_api_key()
    
    if not has_api_key:
        print("\n💡 提示：设置 ZHIPUAI_API_KEY 环境变量可体验完整功能")
    
    # 运行测试
    test_multi_expert_system()
    test_customer_service_system()
    test_collaboration_features()
    
    print("\n" + "=" * 80)
    print("✅ 第8章测试完成！")
    
    if has_api_key:
        print("🎯 多智能体协作系统验证通过")
    else:
        print("🎯 模拟模式测试通过，配置API密钥可获得完整体验")

if __name__ == "__main__":
    main()