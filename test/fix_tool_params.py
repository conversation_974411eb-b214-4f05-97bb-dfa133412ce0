#!/usr/bin/env python3
"""
修复工具参数传递问题的解决方案
"""

from langchain_core.tools import tool
from langchain_core.messages import ToolMessage
import json

def enhanced_robust_tool_node(state):
    """
    增强的带错误处理的工具节点
    修复参数传递问题
    """
    messages = state["messages"]
    last_message = messages[-1]
    
    if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
        return {"messages": []}
    
    tool_results = []
    
    for tool_call in last_message.tool_calls:
        try:
            # 查找对应的工具
            tool_func = None
            for tool in tools:
                if tool.name == tool_call["name"]:
                    tool_func = tool
                    break
            
            if tool_func is None:
                result = f"错误：未找到工具 {tool_call['name']}"
            else:
                # 获取工具调用参数
                tool_args = tool_call.get("args", {})
                
                # 修复：检查参数是否为空，如果是则尝试智能推断
                if not tool_args or tool_args == {}:
                    result = f"错误：工具 {tool_call['name']} 缺少必要参数"
                    
                    # 智能参数修复（针对常见情况）
                    if tool_call['name'] == 'calculator':
                        # 尝试从AI消息内容中提取数学表达式
                        ai_content = last_message.content.lower()
                        if '100' in ai_content and '7' in ai_content and ('除以' in ai_content or '/' in ai_content):
                            tool_args = {"expression": "100 / 7"}
                            result = tool_func.invoke(tool_args)
                        else:
                            result = "错误：无法识别要计算的数学表达式，请明确指定"
                    else:
                        result = f"错误：工具 {tool_call['name']} 参数为空，无法执行"
                else:
                    # 正常执行工具
                    result = tool_func.invoke(tool_args)
            
            # 创建工具消息
            tool_message = ToolMessage(
                content=result,
                tool_call_id=tool_call["id"],
                name=tool_call["name"]
            )
            tool_results.append(tool_message)
            
        except Exception as e:
            # 错误处理
            error_message = ToolMessage(
                content=f"工具执行失败：{str(e)}",
                tool_call_id=tool_call["id"], 
                name=tool_call["name"]
            )
            tool_results.append(error_message)
    
    return {"messages": tool_results}

def create_robust_tool_prompt():
    """
    创建更强健的工具提示，帮助LLM正确构造工具调用
    """
    return """
你是一个智能助手，可以使用以下工具：

1. calculator(expression: str) - 数学计算器
   参数: expression - 数学表达式，如 "2+3", "100/7", "math.sqrt(16)"
   
2. web_search(query: str) - 网络搜索  
   参数: query - 搜索关键词
   
3. get_weather(city: str) - 天气查询
   参数: city - 城市名称
   
4. current_time() - 获取当前时间
   无需参数

重要提示：
- 调用工具时必须提供正确的参数格式
- 计算器需要具体的数学表达式
- 如果用户要求多个操作，可以按顺序调用多个工具
- 确保每个工具调用都有必需的参数

示例：
用户说"算一下100除以7" -> 调用 calculator(expression="100/7")
用户说"北京天气" -> 调用 get_weather(city="北京")
    """

print("修复建议已生成！")
print("\n关键修复点：")
print("1. 在robust_tool_node中添加参数检查和智能修复")
print("2. 为LLM提供更明确的工具使用指南")
print("3. 添加常见错误的自动修复逻辑")