#!/usr/bin/env python3
"""
测试修正后的文档代码
验证GLM-4.5集成和智能分析功能
"""
import os
from typing import Annotated
from typing_extensions import TypedDict
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage

class WorkflowState(TypedDict):
    messages: Annotated[list, add_messages]
    workflow_step: str
    human_input: str
    approval_status: str
    retry_count: int
    max_retries: int
    original_request: str  # 保存用户的原始请求

def create_resumable_workflow():
    """创建可恢复的工作流"""
    
    def task_execution_node(state: WorkflowState):
        """任务执行节点"""
        import os
        current_step = state.get("workflow_step", "start")
        
        if current_step == "start":
            # 保存用户的原始请求
            original_request = state["messages"][-1].content
            return {
                "workflow_step": "processing",
                "original_request": original_request,
                "messages": [AIMessage(content="开始处理您的请求...")]
            }
        elif current_step == "processing":
            # 分析用户的原始请求，而不是AI的回复
            original_request = state.get("original_request", "")
            
            # 使用GLM-4.5进行复杂度分析
            api_key = os.getenv("ZHIPUAI_API_KEY")
            complexity_analysis = None
            
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(
                        model="glm-4.5",
                        temperature=0.1,
                        api_key=api_key
                    )
                    
                    analysis_prompt = f"""
                    分析以下用户请求的复杂度和风险：
                    用户请求：{original_request}
                    
                    请评估：
                    1. 任务复杂度（简单/中等/复杂/困难）
                    2. 是否需要人工指导（是/否）
                    3. 简要说明原因
                    
                    格式：复杂度|需要指导|原因
                    """
                    
                    response = llm.invoke([HumanMessage(content=analysis_prompt)])
                    complexity_analysis = response.content.strip()
                    
                except Exception as e:
                    print(f"⚠️ GLM-4.5分析失败: {e}")
            
            # 解析分析结果或使用关键词检测
            needs_guidance = False
            complexity_level = "简单"
            
            if complexity_analysis:
                try:
                    parts = complexity_analysis.split('|')
                    complexity_level = parts[0].strip()
                    needs_guidance = "是" in parts[1].strip()
                except:
                    # 如果解析失败，回退到关键词检测
                    pass
            
            # 关键词检测（补充或备用方案）
            if ("复杂" in original_request or "困难" in original_request or 
                "不确定" in original_request or "不知道" in original_request or
                complexity_level in ["复杂", "困难"]):
                needs_guidance = True
            
            print(f"🧠 GLM-4.5分析: {complexity_analysis}")
            print(f"📊 评估结果: 复杂度={complexity_level}, 需要指导={needs_guidance}")
            
            if needs_guidance:
                guidance_request = {
                    "type": "guidance_needed",
                    "original_task": original_request,
                    "complexity_level": complexity_level,
                    "ai_analysis": complexity_analysis or "基于关键词检测",
                    "specific_question": f"这个任务被评估为'{complexity_level}'，您希望我如何处理？",
                    "options": [
                        "继续尝试自动处理",
                        "简化处理方式",
                        "暂停等待更多信息",
                        "转交人工处理"
                    ]
                }
                return interrupt(guidance_request)
            else:
                # 使用GLM-4.5生成真实回复
                if api_key:
                    try:
                        from langchain_community.chat_models import ChatZhipuAI
                        llm = ChatZhipuAI(
                            model="glm-4.5",
                            temperature=0.7,
                            api_key=api_key
                        )
                        response = llm.invoke([HumanMessage(content=original_request)])
                        final_message = response.content
                    except Exception as e:
                        print(f"⚠️ GLM-4.5回复失败: {e}")
                        final_message = f"任务'{original_request}'处理完成！"
                else:
                    final_message = f"任务'{original_request}'处理完成！"
                
                return {
                    "workflow_step": "completed",
                    "messages": [AIMessage(content=final_message)]
                }
        
        return {"workflow_step": "error"}
    
    def feedback_processing_node(state: WorkflowState):
        """反馈处理节点"""
        import os
        human_input = state.get("human_input", "")
        retry_count = state.get("retry_count", 0)
        max_retries = state.get("max_retries", 3)
        original_request = state.get("original_request", "")
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if not human_input:
            # 没有人工输入，可能是自动恢复
            return {"approval_status": "auto_continue"}
        
        # 解析人工指令
        if "继续" in human_input:
            return {
                "approval_status": "continue",
                "workflow_step": "processing"
            }
        elif "简化" in human_input:
            # 使用GLM-4.5生成简化回复
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(
                        model="glm-4.5",
                        temperature=0.7,
                        api_key=api_key
                    )
                    simplified_prompt = f"""
                    用户请求：{original_request}
                    
                    请用简化的方式回复这个请求，提供基础的、直接的回答，避免复杂的分析。
                    """
                    response = llm.invoke([HumanMessage(content=simplified_prompt)])
                    simplified_reply = response.content
                except Exception as e:
                    print(f"⚠️ GLM-4.5简化回复失败: {e}")
                    simplified_reply = "好的，我将采用简化的处理方式。"
            else:
                simplified_reply = "好的，我将采用简化的处理方式。"
            
            return {
                "approval_status": "simplify",
                "workflow_step": "completed",
                "messages": [AIMessage(content=simplified_reply)]
            }
        elif "暂停" in human_input:
            return {
                "approval_status": "pause",
                "workflow_step": "waiting"
            }
        elif "转交" in human_input:
            return {
                "approval_status": "escalate",
                "workflow_step": "human_takeover",
                "messages": [AIMessage(content="好的，我将把这个任务转交给人工处理。")]
            }
        elif "重试" in human_input:
            if retry_count < max_retries:
                return {
                    "approval_status": "retry",
                    "workflow_step": "processing",
                    "retry_count": retry_count + 1
                }
            else:
                return {
                    "approval_status": "max_retries_reached",
                    "workflow_step": "failed",
                    "messages": [AIMessage(content="已达到最大重试次数，任务失败。")]
                }
        else:
            # 自定义指令 - 让GLM-4.5处理
            if api_key:
                try:
                    from langchain_community.chat_models import ChatZhipuAI
                    llm = ChatZhipuAI(
                        model="glm-4.5",
                        temperature=0.7,
                        api_key=api_key
                    )
                    custom_prompt = f"""
                    原始用户请求：{original_request}
                    人工指导：{human_input}
                    
                    请根据人工指导来处理原始请求。
                    """
                    response = llm.invoke([HumanMessage(content=custom_prompt)])
                    custom_reply = response.content
                except Exception as e:
                    print(f"⚠️ GLM-4.5自定义回复失败: {e}")
                    custom_reply = f"收到指令：{human_input}，我将按此执行。"
            else:
                custom_reply = f"收到指令：{human_input}，我将按此执行。"
            
            return {
                "approval_status": "custom_instruction",
                "workflow_step": "completed",
                "messages": [AIMessage(content=custom_reply)]
            }
    
    def workflow_router(state: WorkflowState) -> str:
        """工作流路由"""
        workflow_step = state.get("workflow_step", "start")
        approval_status = state.get("approval_status", "")
        
        if workflow_step == "completed":
            return "end"
        elif workflow_step == "failed":
            return "end"
        elif workflow_step == "human_takeover":
            return "end"
        elif workflow_step == "waiting":
            return "pause"
        else:
            return "continue_processing"
    
    # 构建图
    graph = StateGraph(WorkflowState)
    graph.add_node("task_execution", task_execution_node)
    graph.add_node("feedback_processing", feedback_processing_node)
    graph.add_node("pause", lambda state: {"workflow_step": "paused"})
    graph.add_node("continue_processing", task_execution_node)
    
    # 设置流程
    graph.add_edge(START, "task_execution")
    graph.add_edge("task_execution", "feedback_processing")
    
    # 条件路由
    graph.add_conditional_edges(
        "feedback_processing",
        workflow_router,
        {
            "end": END,
            "pause": "pause",
            "continue_processing": "continue_processing"
        }
    )
    
    graph.add_edge("continue_processing", "feedback_processing")
    graph.add_edge("pause", END)
    
    return graph.compile()

def test_fixed_document():
    """测试修正后的文档代码"""
    print("🚀 测试修正后的6.3章节文档代码")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置 (Key: {api_key[:10]}...{api_key[-4:]})")
    else:
        print("⚠️ 未配置智谱API，将使用模拟模式")
    
    try:
        app = create_resumable_workflow()
        print("✅ 成功创建可恢复工作流")
    except Exception as e:
        print(f"❌ 创建应用失败: {e}")
        return
    
    # 测试用例
    test_cases = [
        {
            "request": "今天天气怎么样？",
            "expected": "简单任务，应该自动完成",
            "should_interrupt": False
        },
        {
            "request": "这是一个非常复杂的数据分析任务，需要处理大量不确定因素",
            "expected": "复杂任务，应该触发interrupt",
            "should_interrupt": True,
            "mock_guidance": "简化处理方式"
        },
        {
            "request": "我不知道该怎么办，这个问题我不太确定",
            "expected": "不确定任务，应该触发interrupt",
            "should_interrupt": True,
            "mock_guidance": "继续尝试自动处理"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*20} 测试案例 {i} {'='*20}")
        print(f"📝 用户请求: {case['request']}")
        print(f"🎯 预期: {case['expected']}")
        print("-" * 50)
        
        try:
            # 执行测试
            result = app.invoke({
                "messages": [HumanMessage(content=case['request'])],
                "workflow_step": "start",
                "human_input": "",
                "approval_status": "",
                "retry_count": 0,
                "max_retries": 3,
                "original_request": case['request']
            })
            
            # 如果没有interrupt，直接显示结果
            if not case['should_interrupt']:
                print(f"✅ 自动完成!")
                if "messages" in result and result["messages"]:
                    print(f"💬 GLM-4.5回复: {result['messages'][-1].content}")
            else:
                print("🤔 预期应该触发interrupt，但没有")
                
        except Exception as e:
            error_str = str(e)
            if "interrupt" in error_str.lower():
                print("✅ 成功触发INTERRUPT!")
                print("📋 GLM-4.5请求人工指导")
                
                if case.get('mock_guidance'):
                    print(f"🤖 模拟人工指导: {case['mock_guidance']}")
                    
                    # 继续执行
                    try:
                        final_result = app.invoke({
                            **result,
                            "human_input": case['mock_guidance']
                        })
                        print(f"✅ 根据指导完成!")
                        if "messages" in final_result and final_result["messages"]:
                            print(f"💬 GLM-4.5最终回复: {final_result['messages'][-1].content}")
                    except Exception as e2:
                        print(f"❌ 恢复执行失败: {e2}")
            else:
                print(f"❌ 其他错误: {error_str}")
        
        print("=" * 60)
    
    print("\n🎉 测试完成!")
    print("📝 总结:")
    print("• 修正后的代码能正确分析用户的原始请求")
    print("• GLM-4.5智能分析任务复杂度和风险")
    print("• 复杂任务正确触发interrupt请求人工指导")
    print("• 人工反馈能生成个性化的GLM-4.5回复")
    print("• 文档代码修正成功！")

if __name__ == "__main__":
    test_fixed_document()