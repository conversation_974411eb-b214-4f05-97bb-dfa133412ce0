# 第8章 多智能体协作

在现实世界中，复杂的任务往往需要多个专家协作完成：医生、护士、技师共同治疗病人；产品经理、设计师、工程师共同开发产品。AI 世界也是如此——有时候一个智能体无法胜任所有工作，我们需要让多个专业化的 AI 智能体协同工作。

在这一章中，我们将学习如何在 LangGraph 中构建多智能体协作系统。

## 8.1 何时用多个节点？何时用多个图？

### 单智能体 vs 多智能体

首先，我们需要理解什么时候需要多智能体：

```python
# 单智能体：一个万能助手
单智能体 = "我能回答问题、写代码、分析数据、翻译文档..."

# 多智能体：专业分工
多智能体 = {
    "技术专家": "专门解决编程问题",
    "数据分析师": "专门处理数据分析", 
    "翻译专家": "专门处理多语言翻译",
    "项目经理": "专门进行项目规划"
}
```

### 多节点 vs 多图的选择

**多节点（单图内）：**
- 适合：流程化的协作，有明确的先后顺序
- 特点：共享状态，紧密耦合
- 例子：内容创作流水线（写作→审核→发布）

**多图（子图）：**
- 适合：独立的专业领域，松散耦合
- 特点：独立状态，通过消息通信
- 例子：客服系统（技术支持图 + 销售咨询图）

```python
import os
import time
from datetime import datetime
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict
from typing import Annotated, Literal, Dict, List, Any
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_community.chat_models import ChatZhipuAI

class MultiAgentState(TypedDict):
    messages: Annotated[list, add_messages]
    current_agent: str
    task_type: str
    agent_outputs: dict
    collaboration_history: list

def create_multi_node_system():
    """创建多节点协作系统（单图内）"""
    
    def task_router_node(state: MultiAgentState):
        """任务路由节点"""
        last_message = state["messages"][-1].content.lower()
        
        # 根据任务类型路由到不同的专家节点
        if any(keyword in last_message for keyword in ["代码", "编程", "bug", "算法"]):
            task_type = "technical"
        elif any(keyword in last_message for keyword in ["数据", "分析", "统计", "图表"]):
            task_type = "analytics"
        elif any(keyword in last_message for keyword in ["翻译", "语言", "英文", "中文"]):
            task_type = "translation"
        elif any(keyword in last_message for keyword in ["项目", "计划", "管理", "进度"]):
            task_type = "project"
        else:
            task_type = "general"
            
        return {
            "task_type": task_type,
            "current_agent": f"{task_type}_expert"
        }
    
    def technical_expert_node(state: MultiAgentState):
        """技术专家节点"""
        system_prompt = """
你是一位资深的技术专家，专门解决编程和技术问题。

专业领域：
- 代码审查和优化
- 算法设计和分析
- 系统架构建议
- 技术问题诊断

回答风格：
- 技术准确，逻辑清晰
- 提供可执行的代码示例
- 解释技术原理和最佳实践
- 考虑性能和安全因素
"""
        
        return call_agent_with_retry(state, system_prompt, "technical_expert", temperature=0.3)
    
    def analytics_expert_node(state: MultiAgentState):
        """数据分析专家节点"""
        system_prompt = """
你是一位数据分析专家，专门处理数据相关问题。

专业领域：
- 数据清洗和预处理
- 统计分析和建模
- 数据可视化建议
- 业务洞察提取

回答风格：
- 数据驱动，逻辑严谨
- 提供分析方法和工具建议
- 解释统计概念和意义
- 关注业务价值和可操作性
"""
        
        return call_agent_with_retry(state, system_prompt, "analytics_expert", temperature=0.4)
    
    def translation_expert_node(state: MultiAgentState):
        """翻译专家节点"""
        system_prompt = """
你是一位专业的翻译专家，精通多种语言。

专业领域：
- 中英文互译
- 技术文档翻译
- 商务文件翻译
- 本地化建议

回答风格：
- 翻译准确，语言地道
- 保持原文的语气和风格
- 解释文化差异和语言特点
- 提供多种翻译选项
"""
        
        return call_agent_with_retry(state, system_prompt, "translation_expert", temperature=0.2)
    
    def project_manager_node(state: MultiAgentState):
        """项目经理节点"""
        system_prompt = """
你是一位经验丰富的项目经理，专门负责项目规划和管理。

专业领域：
- 项目计划制定
- 资源分配和时间管理
- 风险识别和控制
- 团队协调和沟通

回答风格：
- 结构化思维，条理清晰
- 关注可执行性和时间节点
- 考虑资源约束和风险因素
- 提供具体的行动计划
"""
        
        return call_agent_with_retry(state, system_prompt, "project_manager", temperature=0.5)
    
    def general_assistant_node(state: MultiAgentState):
        """通用助手节点"""
        system_prompt = """
你是一位通用AI助手，能够处理各种常见问题。

特点：
- 友好耐心，乐于助人
- 知识面广，回答全面
- 能够引导用户找到合适的专家
- 提供基础的信息和建议
"""
        
        return call_agent_with_retry(state, system_prompt, "general_assistant", temperature=0.7)
    
    def call_agent_with_retry(state: MultiAgentState, system_prompt: str, agent_name: str, temperature: float = 0.5, max_retries: int = 2):
        """带重试的智能体调用"""
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {
                "messages": [AIMessage(content="⚠️ 未配置智谱API密钥")],
                "current_agent": agent_name,
                "agent_outputs": {**state.get("agent_outputs", {}), agent_name: "API密钥未配置"}
            }
        
        messages = [SystemMessage(content=system_prompt)] + state["messages"]
        
        for attempt in range(max_retries):
            try:
                llm = ChatZhipuAI(model="glm-4.5", temperature=temperature, api_key=api_key, timeout=35)
                response = llm.invoke(messages)
                
                # 记录专家输出
                agent_outputs = state.get("agent_outputs", {})
                agent_outputs[agent_name] = response.content
                
                return {
                    "messages": [response],
                    "agent_outputs": agent_outputs,
                    "current_agent": agent_name
                }
                
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(1)
                    continue
                else:
                    return {
                        "messages": [AIMessage(content=f"[{agent_name}] ⚠️ 网络繁忙，请稍后重试")],
                        "current_agent": agent_name,
                        "agent_outputs": {**state.get("agent_outputs", {}), agent_name: f"调用失败: {str(e)[:50]}"}
                    }
    
    def agent_router(state: MultiAgentState) -> str:
        """智能体路由函数"""
        task_type = state.get("task_type", "general")
        routing_map = {
            "technical": "technical_expert",
            "analytics": "analytics_expert", 
            "translation": "translation_expert",
            "project": "project_manager",
            "general": "general_assistant"
        }
        return routing_map.get(task_type, "general_assistant")
    
    # 构建图
    graph = StateGraph(MultiAgentState)
    
    # 添加所有节点
    graph.add_node("task_router", task_router_node)
    graph.add_node("technical_expert", technical_expert_node)
    graph.add_node("analytics_expert", analytics_expert_node)
    graph.add_node("translation_expert", translation_expert_node)
    graph.add_node("project_manager", project_manager_node)
    graph.add_node("general_assistant", general_assistant_node)
    
    # 设置路由
    graph.add_edge(START, "task_router")
    
    # 条件路由到不同的专家
    graph.add_conditional_edges(
        "task_router",
        agent_router,
        {
            "technical_expert": "technical_expert",
            "analytics_expert": "analytics_expert",
            "translation_expert": "translation_expert", 
            "project_manager": "project_manager",
            "general_assistant": "general_assistant"
        }
    )
    
    # 所有专家都连接到结束
    for expert in ["technical_expert", "analytics_expert", "translation_expert", "project_manager", "general_assistant"]:
        graph.add_edge(expert, END)
    
    return graph.compile()
```

### 协作流水线系统

有些任务需要多个智能体按顺序协作：

```python
class ContentCreationState(TypedDict):
    messages: Annotated[list, add_messages]
    topic: str
    outline: str
    draft_content: str
    reviewed_content: str
    final_content: str
    feedback_history: list
    current_stage: str

def create_content_pipeline():
    """创建内容创作流水线"""
    
    def topic_analyzer_node(state: ContentCreationState):
        """主题分析节点"""
        user_request = state["messages"][-1].content
        system_prompt = """
你是内容策划专家，负责分析用户需求并提取核心主题。

任务：
1. 理解用户的内容需求
2. 提取核心主题和关键词
3. 分析目标受众
4. 确定内容类型和风格

请简洁地总结主题要点。
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"topic": "API密钥未配置", "current_stage": "error"}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.4, api_key=api_key, timeout=35)
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"用户需求：{user_request}")
            ]
            response = llm.invoke(messages)
            
            return {
                "topic": response.content,
                "current_stage": "topic_analyzed",
                "messages": [AIMessage(content=f"📋 主题分析完成：{response.content}")]
            }
        except Exception as e:
            return {
                "topic": f"分析失败: {str(e)[:50]}",
                "current_stage": "error",
                "messages": [AIMessage(content="⚠️ 主题分析失败，请重试")]
            }
    
    def outline_creator_node(state: ContentCreationState):
        """大纲创建节点"""
        topic = state.get("topic", "")
        system_prompt = """
你是内容架构师，负责根据主题创建详细的内容大纲。

任务：
1. 基于主题分析创建逻辑清晰的大纲
2. 确保内容结构合理，层次分明
3. 包含引言、主体、结论部分
4. 标注每部分的重点内容

请创建一个结构化的大纲。
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"outline": "API密钥未配置", "current_stage": "error"}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"主题分析：{topic}")
            ]
            response = llm.invoke(messages)
            
            return {
                "outline": response.content,
                "current_stage": "outline_created",
                "messages": [AIMessage(content=f"📝 大纲创建完成：\n{response.content}")]
            }
        except Exception as e:
            return {
                "outline": f"创建失败: {str(e)[:50]}",
                "current_stage": "error",
                "messages": [AIMessage(content="⚠️ 大纲创建失败，请重试")]
            }
    
    def content_writer_node(state: ContentCreationState):
        """内容写作节点"""
        topic = state.get("topic", "")
        outline = state.get("outline", "")
        system_prompt = """
你是专业的内容写作者，负责根据大纲创作高质量的内容。

写作要求：
1. 内容要有吸引力和可读性
2. 逻辑清晰，论证充分
3. 语言流畅，表达准确
4. 符合目标受众的阅读习惯

请基于大纲创作完整的内容。
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"draft_content": "API密钥未配置", "current_stage": "error"}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.6, api_key=api_key, timeout=40)
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"主题：{topic}\n\n大纲：{outline}")
            ]
            response = llm.invoke(messages)
            
            return {
                "draft_content": response.content,
                "current_stage": "content_drafted",
                "messages": [AIMessage(content=f"✍️ 内容创作完成，字数：{len(response.content)} 字")]
            }
        except Exception as e:
            return {
                "draft_content": f"写作失败: {str(e)[:50]}",
                "current_stage": "error",
                "messages": [AIMessage(content="⚠️ 内容写作失败，请重试")]
            }
    
    def content_reviewer_node(state: ContentCreationState):
        """内容审核节点"""
        draft_content = state.get("draft_content", "")
        system_prompt = """
你是内容审核专家，负责审核和改进内容质量。

审核标准：
1. 内容准确性和逻辑性
2. 语言表达和文字质量
3. 结构完整性和可读性
4. 是否符合主题要求

请提供具体的修改建议和改进后的内容。
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"reviewed_content": "API密钥未配置", "current_stage": "error"}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=40)
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"待审核内容：\n{draft_content}")
            ]
            response = llm.invoke(messages)
            
            feedback_history = state.get("feedback_history", [])
            feedback_history.append({
                "stage": "content_review",
                "feedback": response.content,
                "timestamp": datetime.now().isoformat()
            })
            
            return {
                "reviewed_content": response.content,
                "feedback_history": feedback_history,
                "current_stage": "content_reviewed",
                "messages": [AIMessage(content=f"🔍 内容审核完成，已提供修改建议")]
            }
        except Exception as e:
            return {
                "reviewed_content": f"审核失败: {str(e)[:50]}",
                "current_stage": "error",
                "messages": [AIMessage(content="⚠️ 内容审核失败，请重试")]
            }
    
    def content_finalizer_node(state: ContentCreationState):
        """内容定稿节点"""
        reviewed_content = state.get("reviewed_content", "")
        system_prompt = """
你是内容编辑，负责最终定稿和格式化。

任务：
1. 整合审核意见，完善内容
2. 统一格式和风格
3. 添加必要的标题和段落
4. 确保内容完整和专业

请输出最终版本的内容。
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"final_content": "API密钥未配置", "current_stage": "error"}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.2, api_key=api_key, timeout=35)
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"审核后内容：\n{reviewed_content}")
            ]
            response = llm.invoke(messages)
            
            return {
                "final_content": response.content,
                "current_stage": "content_finalized",
                "messages": [AIMessage(content=f"✅ 内容定稿完成！\n\n{response.content}")]
            }
        except Exception as e:
            return {
                "final_content": f"定稿失败: {str(e)[:50]}",
                "current_stage": "error",
                "messages": [AIMessage(content="⚠️ 内容定稿失败，请重试")]
            }
    
    # 构建流水线图
    graph = StateGraph(ContentCreationState)
    
    # 添加流水线节点
    graph.add_node("topic_analyzer", topic_analyzer_node)
    graph.add_node("outline_creator", outline_creator_node)
    graph.add_node("content_writer", content_writer_node)
    graph.add_node("content_reviewer", content_reviewer_node)
    graph.add_node("content_finalizer", content_finalizer_node)
    
    # 设置线性流水线
    graph.add_edge(START, "topic_analyzer")
    graph.add_edge("topic_analyzer", "outline_creator")
    graph.add_edge("outline_creator", "content_writer")
    graph.add_edge("content_writer", "content_reviewer")
    graph.add_edge("content_reviewer", "content_finalizer")
    graph.add_edge("content_finalizer", END)
    
    return graph.compile()
```

## 8.2 消息总线与事件流

### 智能体间的通信机制

在多智能体系统中，智能体之间需要有效的通信机制：

```python
import json

class MessageBus:
    """消息总线"""
    
    def __init__(self):
        self.subscribers = {}  # 订阅者
        self.message_history = []  # 消息历史
        self.event_handlers = {}  # 事件处理器
    
    def subscribe(self, agent_id: str, message_types: List[str]):
        """订阅消息类型"""
        if agent_id not in self.subscribers:
            self.subscribers[agent_id] = []
        self.subscribers[agent_id].extend(message_types)
    
    def publish(self, message_type: str, sender: str, data: Dict[str, Any]):
        """发布消息"""
        message = {
            "id": f"msg_{len(self.message_history)}",
            "type": message_type,
            "sender": sender,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        self.message_history.append(message)
        
        # 通知订阅者
        for agent_id, subscribed_types in self.subscribers.items():
            if message_type in subscribed_types and agent_id != sender:
                self.notify_agent(agent_id, message)
        
        return message["id"]
    
    def notify_agent(self, agent_id: str, message: Dict[str, Any]):
        """通知智能体"""
        if agent_id in self.event_handlers:
            try:
                self.event_handlers[agent_id](message)
            except Exception as e:
                print(f"通知智能体 {agent_id} 失败: {e}")
    
    def register_handler(self, agent_id: str, handler_func):
        """注册事件处理器"""
        self.event_handlers[agent_id] = handler_func
    
    def get_messages_for_agent(self, agent_id: str, message_types: List[str] = None) -> List[Dict]:
        """获取智能体相关的消息"""
        relevant_messages = []
        for msg in self.message_history:
            if message_types and msg["type"] not in message_types:
                continue
            
            # 包含发送给该智能体的消息或该智能体发送的消息
            if (agent_id in self.subscribers and 
                msg["type"] in self.subscribers[agent_id]) or msg["sender"] == agent_id:
                relevant_messages.append(msg)
        
        return relevant_messages

class CollaborativeState(TypedDict):
    messages: Annotated[list, add_messages]
    message_bus: MessageBus
    active_agents: List[str]
    collaboration_context: Dict[str, Any]
    task_assignments: Dict[str, str]

def create_collaborative_system():
    """创建协作系统"""
    message_bus = MessageBus()
    
    def coordinator_node(state: CollaborativeState):
        """协调器节点"""
        user_request = state["messages"][-1].content
        
        # 分析任务并分配给合适的智能体
        task_analysis = analyze_task_requirements(user_request)
        
        # 发布任务分配消息
        message_bus.publish(
            message_type="task_assignment",
            sender="coordinator",
            data={
                "task": user_request,
                "analysis": task_analysis,
                "assigned_agents": task_analysis["required_agents"]
            }
        )
        
        return {
            "message_bus": message_bus,
            "active_agents": task_analysis["required_agents"],
            "task_assignments": task_analysis["assignments"],
            "collaboration_context": {"task_analysis": task_analysis}
        }
    
    def analyze_task_requirements(task: str) -> Dict[str, Any]:
        """分析任务需求"""
        task_lower = task.lower()
        required_agents = []
        assignments = {}
        
        if any(keyword in task_lower for keyword in ["代码", "编程", "开发"]):
            required_agents.append("developer")
            assignments["developer"] = "负责代码实现和技术方案"
        
        if any(keyword in task_lower for keyword in ["测试", "质量", "bug"]):
            required_agents.append("tester")
            assignments["tester"] = "负责质量保证和测试"
        
        if any(keyword in task_lower for keyword in ["设计", "界面", "用户体验"]):
            required_agents.append("designer")
            assignments["designer"] = "负责设计和用户体验"
        
        if any(keyword in task_lower for keyword in ["项目", "计划", "管理"]):
            required_agents.append("project_manager")
            assignments["project_manager"] = "负责项目管理和协调"
        
        if not required_agents:
            required_agents = ["general_assistant"]
            assignments["general_assistant"] = "提供通用帮助"
        
        return {
            "required_agents": required_agents,
            "assignments": assignments,
            "complexity": len(required_agents),
            "collaboration_needed": len(required_agents) > 1
        }
    
    def developer_agent_node(state: CollaborativeState):
        """开发者智能体"""
        bus = state.get("message_bus")
        task_assignment = state.get("task_assignments", {}).get("developer", "")
        
        system_prompt = f"""
你是开发团队的程序员，当前任务：{task_assignment}

你的职责：
- 编写高质量的代码
- 提供技术解决方案
- 与其他团队成员协作
- 响应技术相关的问题和需求

协作方式：
- 主动分享技术进展
- 向测试团队提供测试要点
- 与设计师确认技术可行性
- 向项目经理汇报进度
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            response_content = "[开发者] ⚠️ 未配置API密钥"
        else:
            try:
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
                messages = [SystemMessage(content=system_prompt)] + state["messages"]
                response = llm.invoke(messages)
                response_content = f"[开发者] {response.content}"
            except Exception as e:
                response_content = f"[开发者] ⚠️ 调用失败: {str(e)[:50]}"
        
        # 发布开发进展消息
        if bus:
            bus.publish(
                message_type="development_update",
                sender="developer",
                data={
                    "status": "in_progress",
                    "output": response_content,
                    "next_steps": "等待测试反馈"
                }
            )
        
        return {
            "messages": [AIMessage(content=response_content)],
            "message_bus": bus
        }
    
    def tester_agent_node(state: CollaborativeState):
        """测试智能体"""
        bus = state.get("message_bus")
        task_assignment = state.get("task_assignments", {}).get("tester", "")
        
        system_prompt = f"""
你是质量保证工程师，当前任务：{task_assignment}

你的职责：
- 设计测试用例
- 执行功能测试
- 发现和报告问题
- 确保质量标准

协作方式：
- 与开发者讨论测试要点
- 向项目经理报告质量状态
- 提供测试结果和建议
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            response_content = "[测试工程师] ⚠️ 未配置API密钥"
        else:
            try:
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.4, api_key=api_key, timeout=35)
                messages = [SystemMessage(content=system_prompt)] + state["messages"]
                response = llm.invoke(messages)
                response_content = f"[测试工程师] {response.content}"
            except Exception as e:
                response_content = f"[测试工程师] ⚠️ 调用失败: {str(e)[:50]}"
        
        # 发布测试结果
        if bus:
            bus.publish(
                message_type="test_result",
                sender="tester",
                data={
                    "status": "completed",
                    "output": response_content,
                    "issues_found": 0  # 简化示例
                }
            )
        
        return {
            "messages": [AIMessage(content=response_content)],
            "message_bus": bus
        }
    
    def project_manager_node(state: CollaborativeState):
        """项目经理智能体"""
        bus = state.get("message_bus")
        
        system_prompt = """
你是项目经理，负责协调整个团队的工作。

你的职责：
- 跟踪项目进度
- 协调团队协作
- 识别和解决问题
- 向客户汇报状态

协作方式：
- 收集各团队的进展报告
- 协调资源和时间安排
- 解决团队间的协作问题
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            response_content = "[项目经理] ⚠️ 未配置API密钥"
        else:
            try:
                # 收集所有团队的更新
                all_updates = []
                if bus:
                    all_updates = bus.get_messages_for_agent("project_manager", ["development_update", "test_result"])
                
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.5, api_key=api_key, timeout=35)
                context = f"团队更新：{json.dumps([msg['data'] for msg in all_updates], ensure_ascii=False, indent=2)}"
                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=context)
                ] + state["messages"]
                response = llm.invoke(messages)
                response_content = f"[项目经理] {response.content}"
            except Exception as e:
                response_content = f"[项目经理] ⚠️ 调用失败: {str(e)[:50]}"
        
        # 发布项目状态
        if bus:
            bus.publish(
                message_type="project_status",
                sender="project_manager",
                data={
                    "status": "on_track",
                    "summary": response_content,
                    "team_updates": len(all_updates) if 'all_updates' in locals() else 0
                }
            )
        
        return {
            "messages": [AIMessage(content=response_content)],
            "message_bus": bus
        }
    
    # 构建协作图
    graph = StateGraph(CollaborativeState)
    
    # 添加节点
    graph.add_node("coordinator", coordinator_node)
    graph.add_node("developer", developer_agent_node)
    graph.add_node("tester", tester_agent_node)
    graph.add_node("project_manager", project_manager_node)
    
    # 设置协作流程
    graph.add_edge(START, "coordinator")
    
    # 并行执行分配的智能体
    for agent in ["developer", "tester", "project_manager"]:
        graph.add_edge("coordinator", agent)
        graph.add_edge(agent, END)
    
    return graph.compile()
```

## 8.3 实战案例：客服机器人 + 质检机器人协作

让我们构建一个完整的客服质检协作系统：

```python
class CustomerServiceState(TypedDict):
    messages: Annotated[list, add_messages]
    customer_info: Dict[str, Any]
    service_category: str
    urgency_level: str
    service_response: str
    quality_score: float
    quality_feedback: str
    escalation_needed: bool
    resolution_status: str

def create_customer_service_system():
    """创建客服质检协作系统"""
    
    def customer_classifier_node(state: CustomerServiceState):
        """客户分类节点"""
        user_message = state["messages"][-1].content
        system_prompt = """
你是客服分类专家，负责分析客户问题并进行分类。

分析维度：
1. 服务类型：技术支持/账单咨询/产品咨询/投诉建议
2. 紧急程度：低/中/高/紧急
3. 客户情绪：平静/不满/愤怒/焦急
4. 复杂程度：简单/中等/复杂

请分析客户问题并返回分类结果。
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"service_category": "general", "urgency_level": "medium", "customer_info": {"error": "API密钥未配置"}}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"客户问题：{user_message}")
            ]
            response = llm.invoke(messages)
            
            # 简化的分类逻辑
            category = "general"
            urgency = "medium"
            
            if any(word in user_message.lower() for word in ["紧急", "急", "马上", "立即"]):
                urgency = "high"
            elif any(word in user_message.lower() for word in ["投诉", "不满", "问题", "错误"]):
                urgency = "high"
                category = "complaint"
            elif any(word in user_message.lower() for word in ["技术", "bug", "故障", "不能用"]):
                category = "technical"
            elif any(word in user_message.lower() for word in ["账单", "付款", "费用", "价格"]):
                category = "billing"
            
            return {
                "service_category": category,
                "urgency_level": urgency,
                "customer_info": {"classification": response.content}
            }
        except Exception as e:
            return {
                "service_category": "general",
                "urgency_level": "medium", 
                "customer_info": {"error": f"分类失败: {str(e)[:50]}"}
            }
    
    def customer_service_agent_node(state: CustomerServiceState):
        """客服代表节点"""
        category = state.get("service_category", "general")
        urgency = state.get("urgency_level", "medium")
        user_message = state["messages"][-1].content
        
        # 根据分类选择专业化的系统提示
        service_prompts = {
            "technical": """
你是技术支持专家，专门解决客户的技术问题。

服务标准：
- 快速定位问题根源
- 提供清晰的解决步骤
- 确认客户理解操作方法
- 必要时提供替代方案
""",
            "billing": """
你是账务专员，专门处理账单和付款相关问题。

服务标准：
- 准确核实账单信息
- 清楚解释费用明细
- 协助解决付款问题
- 保护客户隐私信息
""",
            "complaint": """
你是客户关系专员，专门处理客户投诉和建议。

服务标准：
- 耐心倾听客户问题
- 表达理解和歉意
- 提供具体解决方案
- 跟进处理结果
""",
            "general": """
你是客服代表，提供全面的客户服务。

服务标准：
- 友好专业的服务态度
- 准确理解客户需求
- 提供有效的帮助
- 确保客户满意
"""
        }
        
        base_prompt = service_prompts.get(category, service_prompts["general"])
        if urgency == "high":
            base_prompt += "\n\n⚡ 注意：这是高优先级问题，请优先快速响应。"
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            service_response = "⚠️ 系统暂时不可用，请稍后重试"
        else:
            try:
                model = "glm-4.5" if urgency == "high" else "glm-4.5"
                llm = ChatZhipuAI(model=model, temperature=0.4, api_key=api_key, timeout=35)
                messages = [
                    SystemMessage(content=base_prompt),
                    HumanMessage(content=user_message)
                ]
                response = llm.invoke(messages)
                service_response = response.content
            except Exception as e:
                service_response = f"抱歉，系统繁忙，请稍后重试。（{str(e)[:30]}）"
        
        return {
            "service_response": service_response,
            "messages": [AIMessage(content=f"[客服代表] {service_response}")]
        }
    
    def quality_inspector_node(state: CustomerServiceState):
        """质检员节点"""
        service_response = state.get("service_response", "")
        category = state.get("service_category", "general")
        urgency = state.get("urgency_level", "medium")
        customer_message = state["messages"][0].content if state["messages"] else ""
        
        system_prompt = """
你是客服质检专员，负责评估客服回复的质量。

评估标准：
1. 专业性 (1-10分)：回复是否专业、准确
2. 友好性 (1-10分)：语言是否友好、礼貌
3. 完整性 (1-10分)：是否完整回答了客户问题
4. 实用性 (1-10分)：提供的解决方案是否实用
5. 及时性 (1-10分)：响应是否及时、高效

请对客服回复进行评分并提供改进建议。
"""
        
        evaluation_context = f"""
客户问题：{customer_message}
问题分类：{category}
紧急程度：{urgency}
客服回复：{service_response}
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            quality_score = 5.0
            quality_feedback = "⚠️ 无法进行质检，API密钥未配置"
        else:
            try:
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.2, api_key=api_key, timeout=35)
                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=evaluation_context)
                ]
                response = llm.invoke(messages)
                quality_feedback = response.content
                
                # 简化的评分逻辑（实际应用中会从LLM响应中解析）
                quality_score = 8.5 if "专业" in response.content and "友好" in response.content else 7.0
            except Exception as e:
                quality_score = 6.0
                quality_feedback = f"质检失败：{str(e)[:50]}"
        
        escalation_needed = (
            quality_score < 7.0 or 
            urgency == "high" or 
            "投诉" in customer_message
        )
        
        return {
            "quality_score": quality_score,
            "quality_feedback": quality_feedback,
            "escalation_needed": escalation_needed,
            "messages": [AIMessage(content=f"[质检员] 质量评分：{quality_score}/10\n{quality_feedback}")]
        }
    
    def escalation_handler_node(state: CustomerServiceState):
        """升级处理节点"""
        if not state.get("escalation_needed", False):
            return {
                "resolution_status": "resolved",
                "messages": [AIMessage(content="[系统] 问题已正常处理完成")]
            }
        
        quality_score = state.get("quality_score", 0)
        urgency = state.get("urgency_level", "medium")
        
        system_prompt = """
你是客服主管，负责处理升级的客户问题。

处理原则：
- 快速响应升级问题
- 提供更高级别的解决方案
- 确保客户满意度
- 必要时提供补偿措施
"""
        
        escalation_reason = []
        if quality_score < 7.0:
            escalation_reason.append(f"服务质量不达标 ({quality_score}/10)")
        if urgency == "high":
            escalation_reason.append("高优先级问题")
        
        context = f"升级原因：{'; '.join(escalation_reason)}"
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            response_content = "⚠️ 主管暂时不可用，问题已记录，会尽快处理"
        else:
            try:
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=context)
                ] + state["messages"]
                response = llm.invoke(messages)
                response_content = response.content
            except Exception as e:
                response_content = f"主管处理失败：{str(e)[:50]}"
        
        return {
            "resolution_status": "escalated",
            "messages": [AIMessage(content=f"[客服主管] {response_content}")]
        }
    
    def quality_router(state: CustomerServiceState) -> str:
        """质检路由"""
        escalation_needed = state.get("escalation_needed", False)
        if escalation_needed:
            return "escalation"
        else:
            return "complete"
    
    # 构建客服质检系统
    graph = StateGraph(CustomerServiceState)
    
    # 添加节点
    graph.add_node("classifier", customer_classifier_node)
    graph.add_node("service_agent", customer_service_agent_node)
    graph.add_node("quality_inspector", quality_inspector_node)
    graph.add_node("escalation_handler", escalation_handler_node)
    graph.add_node("complete", lambda state: {"resolution_status": "completed"})
    
    # 设置流程
    graph.add_edge(START, "classifier")
    graph.add_edge("classifier", "service_agent")
    graph.add_edge("service_agent", "quality_inspector")
    
    # 质检后的条件路由
    graph.add_conditional_edges(
        "quality_inspector",
        quality_router,
        {
            "escalation": "escalation_handler",
            "complete": "complete"
        }
    )
    
    graph.add_edge("escalation_handler", END)
    graph.add_edge("complete", END)
    
    return graph.compile()
```

## 环境准备

在运行本章的示例代码之前，请确保已安装必要的依赖：

```bash
# 使用uv安装依赖
uv add langchain-community langgraph langchain-core typing-extensions
```

并设置智谱AI API密钥：

```bash
export ZHIPUAI_API_KEY="your_zhipu_api_key_here"
```

## 运行示例

```python
def demo_multi_agent_collaboration():
    """演示多智能体协作"""
    print("🤖 多智能体协作系统演示")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置")
    else:
        print("⚠️ 未配置智谱API密钥，演示将使用模拟模式")
    
    # 1. 多专家协作系统演示
    print("\n1. 🎯 多专家协作系统")
    multi_expert_system = create_multi_node_system()
    
    test_questions = [
        "我需要优化这段Python代码的性能",
        "帮我分析一下用户数据的趋势",
        "请将这段中文翻译成英文：人工智能改变世界",
        "制定一个新产品开发的项目计划"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n--- 测试 {i} ---")
        print(f"❓ 问题: {question}")
        
        try:
            result = multi_expert_system.invoke({
                "messages": [HumanMessage(content=question)],
                "current_agent": "",
                "task_type": "",
                "agent_outputs": {},
                "collaboration_history": []
            })
            
            print(f"🎯 分配给: {result.get('current_agent', 'unknown')}")
            print(f"📝 回复: {result['messages'][-1].content[:150]}...")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    # 2. 客服质检协作系统演示
    print(f"\n{'='*60}")
    print("2. 📞 客服质检协作系统")
    
    customer_service = create_customer_service_system()
    
    customer_issues = [
        "我的账单有问题，费用比平时高了很多",
        "软件一直崩溃，急需解决！",
        "我要投诉你们的服务态度",
        "请问如何使用新功能？"
    ]
    
    for i, issue in enumerate(customer_issues, 1):
        print(f"\n--- 客服案例 {i} ---")
        print(f"👤 客户: {issue}")
        
        try:
            result = customer_service.invoke({
                "messages": [HumanMessage(content=issue)],
                "customer_info": {},
                "service_category": "",
                "urgency_level": "",
                "service_response": "",
                "quality_score": 0.0,
                "quality_feedback": "",
                "escalation_needed": False,
                "resolution_status": ""
            })
            
            print(f"📋 分类: {result.get('service_category', 'unknown')}")
            print(f"⚡ 紧急度: {result.get('urgency_level', 'unknown')}")
            print(f"⭐ 质量评分: {result.get('quality_score', 0)}/10")
            print(f"📊 状态: {result.get('resolution_status', 'unknown')}")
            
            # 显示主要对话
            for msg in result.get('messages', []):
                if isinstance(msg, AIMessage) and '[客服代表]' in msg.content:
                    print(f"💬 {msg.content[:100]}...")
                    break
                    
        except Exception as e:
            print(f"❌ 处理失败: {e}")
    
    print(f"\n{'='*60}")
    print("🎉 多智能体协作演示完成！")

if __name__ == "__main__":
    demo_multi_agent_collaboration()
```

---

## 本章小结

在这一章中，我们深入探讨了多智能体协作的核心概念和实现方法：

1. **协作架构设计**：学会了何时使用多节点、何时使用多图的决策原则
2. **消息总线机制**：实现了智能体间的高效通信和事件驱动协作
3. **实战案例**：构建了内容创作流水线和客服质检协作系统
4. **智能路由**：根据任务类型自动分配给最合适的专家智能体

多智能体协作让我们能够构建更加专业化、高效率的 AI 系统，每个智能体专注于自己的领域，通过协作完成复杂任务。

## 关键特性

- **专业分工** - 每个智能体专注特定领域
- **智能路由** - 自动识别任务类型并分配合适的专家
- **错误恢复** - 完善的重试机制和降级策略
- **质量保证** - 多层次的质检和升级机制

## 下一步预告

在第9章中，我们将学习**流式输出与实时反馈**，让用户能够实时看到 AI 的思考过程，提升用户体验和系统的透明度。准备好探索实时交互的魅力了吗？