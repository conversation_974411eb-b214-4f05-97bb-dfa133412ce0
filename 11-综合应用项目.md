# 第11章 综合应用项目

学了这么多理论和技巧，是时候来一场真正的实战了！在这一章中，我们将通过2个完整的项目来综合运用前面学到的所有知识。这些项目从简单到复杂，循序渐进，让你在实践中真正掌握 LangGraph 的精髓。

每个项目都是一个完整的应用，包含需求分析、架构设计、代码实现和部署指南。你可以根据自己的水平选择合适的项目开始，也可以按顺序完成所有项目。

## 项目概览

| 项目 | 难度 | 主要技术点 | 预计时间 |
|------|------|------------|----------|
| 天气问答小助手 | ⭐⭐ | 状态管理、工具调用 | 2-4小时 |
| 智能搜索-总结-汇报 Agent | ⭐⭐⭐ | 工具链、条件边、流式输出 | 4-8小时 |


## 项目1：天气问答小助手

### 项目需求

构建一个智能天气助手，能够：
- 理解用户的天气查询需求
- 调用天气 API 获取实时数据
- 提供友好的天气信息和建议
- 记住用户的常用城市
- 支持多轮对话

### 技术架构

```
用户输入 → 意图识别 → 城市提取 → 天气查询 → 结果整理 → 友好回复
   ↓         ↓         ↓         ↓         ↓         ↓
状态管理   条件路由   工具调用   API集成   数据处理   自然语言生成
```

### 项目实现

#### 1. 状态定义

```python
import os
import time
import json
import requests
from typing import Annotated, Optional, List, Dict
from typing_extensions import TypedDict
from datetime import datetime

from langchain_core.messages import AnyMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langgraph.graph.message import add_messages
from langgraph.graph import StateGraph, START, END
from langgraph.prebuilt import ToolNode, tools_condition

class WeatherState(TypedDict):
    messages: Annotated[List[AnyMessage], add_messages]
    user_location: Optional[str]
    query_city: Optional[str]
    weather_data: Optional[Dict]
    user_preferences: Dict[str, str]
    conversation_history: List[Dict]
    current_intent: str
```

#### 2. 工具定义

```python
@tool
def get_weather_info(city: str, country: str = "CN") -> str:
    """
    获取指定城市的天气信息
    
    Args:
        city: 城市名称，如"北京"、"上海"
        country: 国家代码，默认为"CN"
    
    Returns:
        包含天气信息的JSON字符串
    """
    # 模拟天气API调用（实际应用中使用真实的天气API）
    try:
        # 模拟天气数据
        weather_conditions = [
            "晴天", "多云", "小雨", "中雨", "阴天", "雪天"
        ]
        
        import random
        random.seed(hash(city))  # 确保同一城市返回相同结果
        
        result = {
            "city": city,
            "temperature": random.randint(15, 30),
            "feels_like": random.randint(15, 30),
            "humidity": random.randint(40, 80),
            "description": random.choice(weather_conditions),
            "wind_speed": round(random.uniform(1.0, 10.0), 1),
            "timestamp": datetime.now().isoformat()
        }
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        return f"获取{city}天气信息时出错：{str(e)}"

@tool
def get_weather_forecast(city: str, days: int = 3) -> str:
    """
    获取指定城市的天气预报
    
    Args:
        city: 城市名称
        days: 预报天数，1-5天
    
    Returns:
        天气预报信息
    """
    try:
        forecasts = []
        for i in range(min(days, 5)):
            date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            date = date.replace(day=date.day + i)
            
            forecast = {
                "date": date.isoformat()[:10],
                "temperature_high": 25 + i,
                "temperature_low": 15 + i,
                "description": "晴转多云" if i % 2 == 0 else "多云转阴"
            }
            forecasts.append(forecast)
        
        result = {
            "city": city,
            "forecasts": forecasts
        }
        
        return json.dumps(result, ensure_ascii=False)
        
    except Exception as e:
        return f"获取{city}天气预报时出错：{str(e)}"

@tool
def save_user_city(city: str, user_id: str = "default") -> str:
    """
    保存用户常用城市
    
    Args:
        city: 城市名称
        user_id: 用户ID
    
    Returns:
        保存结果
    """
    try:
        # 简化实现，实际应用中应该保存到数据库
        print(f"💾 保存用户常用城市: {city}")
        return f"已将{city}设为您的常用城市"
    except Exception as e:
        return f"保存城市设置时出错：{str(e)}"
```

#### 3. 核心节点实现

```python
def create_weather_assistant():
    """创建天气助手"""
    
    # 工具列表
    tools = [get_weather_info, get_weather_forecast, save_user_city]
    tool_node = ToolNode(tools)
    
    def intent_analysis_node(state: WeatherState):
        """意图分析节点"""
        last_message = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        system_prompt = """
你是天气助手的意图分析模块。分析用户输入，识别以下意图：
1. weather_query - 查询天气
2. forecast_query - 查询天气预报  
3. location_setting - 设置常用城市
4. general_chat - 一般聊天

同时提取城市名称（如果有的话）。
请返回格式：意图|城市名称
如果没有城市名称，返回：意图|None
"""
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
                
                response = llm.invoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=last_message)
                ])
                
                try:
                    intent, city = response.content.strip().split('|')
                    city = city.strip() if city.strip() != "None" else None
                except:
                    intent, city = "general_chat", None
                    
            except Exception as e:
                print(f"⚠️ 意图分析失败: {e}")
                # 简化的意图识别
                intent, city = simple_intent_analysis(last_message)
        else:
            # 模拟模式的简化意图识别
            intent, city = simple_intent_analysis(last_message)
        
        return {
            "current_intent": intent.strip(),
            "query_city": city
        }
    
    def simple_intent_analysis(text: str):
        """简化的意图识别"""
        text_lower = text.lower()
        
        # 提取城市名称（简化版）
        cities = ["北京", "上海", "深圳", "广州", "杭州", "成都", "武汉", "西安", "南京", "天津"]
        city = None
        for c in cities:
            if c in text:
                city = c
                break
        
        # 识别意图
        if any(word in text_lower for word in ["天气", "温度", "今天", "现在"]):
            intent = "weather_query"
        elif any(word in text_lower for word in ["预报", "明天", "未来", "几天"]):
            intent = "forecast_query"
        elif any(word in text_lower for word in ["设置", "常用", "默认", "保存"]):
            intent = "location_setting"
        else:
            intent = "general_chat"
            
        return intent, city
    
    def weather_agent_node(state: WeatherState):
        """天气智能体节点"""
        intent = state.get("current_intent", "general_chat")
        city = state.get("query_city")
        user_location = state.get("user_location")
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        # 构建系统提示
        system_prompt = f"""
你是一个专业的天气助手，当前用户意图：{intent}

你的能力：
- 查询实时天气信息 (使用get_weather_info工具)
- 提供天气预报 (使用get_weather_forecast工具)
- 保存用户常用城市 (使用save_user_city工具)
- 给出天气相关建议

用户信息：
- 常用城市：{user_location or "未设置"}
- 查询城市：{city or "未指定"}

请根据用户需求选择合适的工具，并提供友好的回复。
如果用户没有指定城市且没有常用城市，请询问城市名称。
"""
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key, timeout=35)
                llm_with_tools = llm.bind_tools(tools)
                
                messages = [SystemMessage(content=system_prompt)] + state["messages"]
                response = llm_with_tools.invoke(messages)
                
                return {"messages": [response]}
                
            except Exception as e:
                print(f"⚠️ 天气智能体调用失败: {e}")
                # 降级到简单回复
                return {"messages": [AIMessage(content="抱歉，天气服务暂时不可用，请稍后重试。")]}
        else:
            # 模拟模式
            if intent == "weather_query" and city:
                mock_response = f"根据最新数据，{city}当前天气：晴天，气温25°C，适合出行！"
            elif intent == "forecast_query" and city:
                mock_response = f"{city}未来3天天气预报：明天晴，后天多云，大后天小雨。"
            elif intent == "location_setting" and city:
                mock_response = f"好的，我已将{city}设为您的常用城市。"
            else:
                mock_response = "请告诉我您想查询哪个城市的天气？"
            
            return {"messages": [AIMessage(content=mock_response)]}
    
    def response_formatter_node(state: WeatherState):
        """回复格式化节点"""
        weather_data = state.get("weather_data")
        
        if not weather_data:
            return {}
        
        # 格式化天气信息为友好的回复
        try:
            data = json.loads(weather_data) if isinstance(weather_data, str) else weather_data
            
            formatted_response = f"""
🌤️ {data['city']}当前天气：

🌡️ 温度：{data['temperature']}°C（体感{data['feels_like']}°C）
☁️ 天况：{data['description']}
💧 湿度：{data['humidity']}%
💨 风速：{data['wind_speed']} m/s

💡 小贴士：{'记得带伞哦！' if '雨' in data['description'] else '天气不错，适合出行！'}
"""
            
            return {"messages": [AIMessage(content=formatted_response)]}
            
        except Exception as e:
            return {"messages": [AIMessage(content="天气信息格式化失败，请重试")]}
    
    # 路由函数
    def weather_router(state: WeatherState) -> str:
        """天气路由"""
        intent = state.get("current_intent", "general_chat")
        city = state.get("query_city")
        user_location = state.get("user_location")
        
        if intent in ["weather_query", "forecast_query"]:
            if city or user_location:
                return "weather_agent"
            else:
                return "ask_location"
        elif intent == "location_setting":
            return "weather_agent"
        else:
            return "general_chat"
    
    def ask_location_node(state: WeatherState):
        """询问位置节点"""
        return {
            "messages": [AIMessage(content="请告诉我您想查询哪个城市的天气？")]
        }
    
    def general_chat_node(state: WeatherState):
        """一般聊天节点"""
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        system_prompt = """
你是一个友好的天气助手。用户的问题不是关于天气的，
请礼貌地回应，并引导用户询问天气相关问题。
"""
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.8, api_key=api_key, timeout=35)
                
                messages = [SystemMessage(content=system_prompt)] + state["messages"]
                response = llm.invoke(messages)
                
                return {"messages": [response]}
                
            except Exception as e:
                return {"messages": [AIMessage(content="我是天气助手，很高兴为您查询天气信息！请问您需要查询哪个城市的天气？")]}
        else:
            return {"messages": [AIMessage(content="您好！我是天气助手，可以为您查询各个城市的天气信息。请问您需要查询哪里的天气？")]}
    
    # 构建图
    graph = StateGraph(WeatherState)
    
    # 添加节点
    graph.add_node("intent_analysis", intent_analysis_node)
    graph.add_node("weather_agent", weather_agent_node)
    graph.add_node("tools", tool_node)
    graph.add_node("response_formatter", response_formatter_node)
    graph.add_node("ask_location", ask_location_node)
    graph.add_node("general_chat", general_chat_node)
    
    # 设置流程
    graph.add_edge(START, "intent_analysis")
    
    # 条件路由
    graph.add_conditional_edges(
        "intent_analysis",
        weather_router,
        {
            "weather_agent": "weather_agent",
            "ask_location": "ask_location",
            "general_chat": "general_chat"
        }
    )
    
    # 工具调用路由
    graph.add_conditional_edges(
        "weather_agent",
        tools_condition,
        {
            "tools": "tools",
            "__end__": END
        }
    )
    
    # 工具执行后直接结束（简化版）
    graph.add_edge("tools", END)
    graph.add_edge("ask_location", END)
    graph.add_edge("general_chat", END)
    
    return graph.compile()
```

#### 4. 测试和运行

```python
def demo_weather_assistant():
    """演示天气助手"""
    # 创建天气助手
    weather_app = create_weather_assistant()
    
    print("🌤️ 天气小助手启动成功！")
    print("💬 我可以帮您查询天气、设置常用城市等")
    print("输入 'quit' 退出\n")
    
    # 初始状态
    conversation_state = {
        "messages": [],
        "user_location": None,
        "query_city": None,
        "weather_data": None,
        "user_preferences": {},
        "conversation_history": [],
        "current_intent": ""
    }
    
    test_questions = [
        "北京今天天气怎么样？",
        "上海未来三天的天气预报",
        "帮我设置深圳为常用城市",
        "你好，今天过得怎么样？"
    ]
    
    print("🧪 自动测试模式：")
    for i, question in enumerate(test_questions, 1):
        print(f"\n--- 测试 {i} ---")
        print(f"👤 用户: {question}")
        
        try:
            # 更新对话状态
            conversation_state["messages"].append(HumanMessage(content=question))
            
            # 调用天气助手
            result = weather_app.invoke(conversation_state)
            
            # 获取AI回复
            ai_response = result["messages"][-1].content
            print(f"🤖 助手: {ai_response}")
            
            # 更新状态
            conversation_state = result
            
        except Exception as e:
            print(f"❌ 出错了: {e}")
    
    print(f"\n{'='*50}")
    print("🎯 交互模式（输入quit退出）：")
    
    while True:
        user_input = input("\n👤 您: ").strip()
        if user_input.lower() == 'quit':
            print("👋 再见！随时为您提供天气服务！")
            break
        
        if not user_input:
            continue
        
        try:
            # 更新对话状态
            conversation_state["messages"].append(HumanMessage(content=user_input))
            
            # 调用天气助手
            result = weather_app.invoke(conversation_state)
            
            # 获取AI回复
            ai_response = result["messages"][-1].content
            print(f"🤖 助手: {ai_response}")
            
            # 更新状态
            conversation_state = result
            
        except Exception as e:
            print(f"❌ 出错了: {e}")

def test_weather_tools():
    """测试天气工具"""
    print("🧪 测试天气工具...")
    
    # 测试天气查询
    result = get_weather_info.invoke({"city": "北京"})
    print(f"📊 天气查询结果: {result}")
    
    # 测试预报查询
    forecast = get_weather_forecast.invoke({"city": "上海", "days": 3})
    print(f"📅 天气预报结果: {forecast}")
    
    # 测试城市保存
    save_result = save_user_city.invoke({"city": "深圳", "user_id": "test_user"})
    print(f"💾 城市保存结果: {save_result}")

if __name__ == "__main__":
    # 运行测试
    test_weather_tools()
    print("\n" + "="*50)
    
    # 启动演示
    demo_weather_assistant()
```

### 项目1总结

通过这个天气助手项目，我们实践了：

**✅ 核心技能**
- **状态管理**：定义复杂的状态结构，管理对话上下文
- **工具集成**：创建和使用外部API工具
- **意图识别**：使用LLM进行自然语言理解
- **条件路由**：根据不同情况选择不同的处理路径
- **错误处理**：优雅地处理各种异常情况
- **用户体验**：提供友好的交互界面

**🚀 扩展建议**
- 集成真实的天气API（如OpenWeatherMap）
- 添加更多城市支持和国际化
- 实现语音输入输出
- 添加天气预警和推送功能
- 支持历史天气查询

---

## 项目2：智能搜索-总结-汇报 Agent

### 项目需求

构建一个智能研究助手，能够：
- 根据用户查询自动搜索相关信息
- 对搜索结果进行智能筛选和去重
- 生成结构化的研究报告
- 支持多轮深入研究
- 提供引用来源和可信度评估

### 技术架构

```
用户查询 → 查询优化 → 多源搜索 → 结果筛选 → 内容总结 → 报告生成
   ↓         ↓         ↓         ↓         ↓         ↓
NLP处理   搜索策略   并行调用   去重排序   文本摘要   结构化输出
```

### 项目实现

#### 1. 状态定义和搜索工具

```python
import hashlib
from collections import defaultdict

class ResearchState(TypedDict):
    messages: Annotated[List[AnyMessage], add_messages]
    original_query: str
    optimized_queries: List[str]
    search_results: List[Dict]
    filtered_results: List[Dict]
    summary_content: str
    final_report: str
    research_depth: int
    sources: List[Dict]
    confidence_score: float

@tool
def web_search(query: str, num_results: int = 10) -> str:
    """
    网络搜索工具
    
    Args:
        query: 搜索查询
        num_results: 返回结果数量
    
    Returns:
        搜索结果JSON字符串
    """
    try:
        # 模拟搜索结果（实际应用中接入真实搜索API）
        results = []
        for i in range(num_results):
            result = {
                "title": f"关于{query}的搜索结果 {i+1}",
                "snippet": f"这是关于{query}的详细信息，包含了相关的背景知识和最新发展动态...",
                "url": f"https://example.com/search/{hashlib.md5((query + str(i)).encode()).hexdigest()[:8]}",
                "timestamp": datetime.now().isoformat(),
                "relevance_score": 0.9 - (i * 0.1)
            }
            results.append(result)
        
        return json.dumps(results, ensure_ascii=False)
        
    except Exception as e:
        return f"搜索失败: {str(e)}"

@tool
def academic_search(query: str, num_results: int = 5) -> str:
    """
    学术搜索工具
    
    Args:
        query: 学术查询
        num_results: 返回结果数量
    
    Returns:
        学术搜索结果
    """
    try:
        results = []
        for i in range(num_results):
            result = {
                "title": f"学术论文：{query}的研究进展",
                "authors": ["张三", "李四", "王五"],
                "abstract": f"本文深入研究了{query}的相关理论和实践应用，提出了新的观点和方法...",
                "url": f"https://academic.example.com/paper/{hashlib.md5((query + str(i)).encode()).hexdigest()[:8]}",
                "citation_count": 42 - i * 5,
                "year": 2023 - i,
                "relevance_score": 0.95 - (i * 0.05)
            }
            results.append(result)
        
        return json.dumps(results, ensure_ascii=False)
        
    except Exception as e:
        return f"学术搜索失败: {str(e)}"

@tool
def news_search(query: str, days: int = 7) -> str:
    """
    新闻搜索工具
    
    Args:
        query: 新闻查询
        days: 搜索最近几天的新闻
    
    Returns:
        新闻搜索结果
    """
    try:
        results = []
        for i in range(5):  # 返回5条新闻
            result = {
                "title": f"最新报道：{query}的重要进展",
                "content": f"据最新消息，{query}领域出现了重要突破，这将对相关行业产生深远影响...",
                "source": f"科技日报{i+1}",
                "published_date": (datetime.now() - datetime.timedelta(days=i)).isoformat(),
                "url": f"https://news.example.com/{hashlib.md5((query + str(i)).encode()).hexdigest()[:8]}",
                "relevance_score": 0.8 - (i * 0.1)
            }
            results.append(result)
        
        return json.dumps(results, ensure_ascii=False)
        
    except Exception as e:
        return f"新闻搜索失败: {str(e)}"
```

#### 2. 核心节点实现

```python
def create_research_assistant():
    """创建研究助手"""
    
    # 工具列表
    tools = [web_search, academic_search, news_search]
    tool_node = ToolNode(tools)
    
    def query_optimizer_node(state: ResearchState):
        """查询优化节点"""
        original_query = state["messages"][-1].content
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        system_prompt = """
你是查询优化专家。将用户的原始查询转换为多个优化的搜索查询，以获得更全面的信息。

优化策略：
1. 分解复合查询为多个简单查询
2. 添加相关的同义词和术语
3. 考虑不同的角度和方面
4. 生成中文和英文查询

请返回3-5个优化后的查询，每行一个。
"""
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.4, api_key=api_key, timeout=35)
                
                response = llm.invoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=f"原始查询：{original_query}")
                ])
                
                optimized_queries = [q.strip() for q in response.content.strip().split('\n') if q.strip()]
                
            except Exception as e:
                print(f"⚠️ 查询优化失败: {e}")
                # 简化的查询优化
                optimized_queries = [
                    original_query,
                    f"{original_query} 技术",
                    f"{original_query} 应用",
                    f"{original_query} 发展趋势"
                ]
        else:
            # 模拟查询优化
            optimized_queries = [
                original_query,
                f"{original_query} 技术发展",
                f"{original_query} 实际应用",
                f"{original_query} 未来趋势"
            ]
        
        return {
            "original_query": original_query,
            "optimized_queries": optimized_queries
        }
    
    def search_coordinator_node(state: ResearchState):
        """搜索协调节点"""
        queries = state.get("optimized_queries", [])
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        system_prompt = """
你是搜索协调员。根据查询内容选择最合适的搜索工具组合。

可用工具：
- web_search: 通用网络搜索，适合一般信息查询
- academic_search: 学术论文搜索，适合深度研究
- news_search: 新闻搜索，适合最新动态

请为每个查询选择合适的搜索工具并执行搜索。
"""
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
                llm_with_tools = llm.bind_tools(tools)
                
                # 构造搜索指令
                search_instructions = f"""
请对以下查询执行搜索：
{chr(10).join([f"{i+1}. {q}" for i, q in enumerate(queries)])}

为每个查询选择最合适的搜索工具。
"""
                
                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=search_instructions)
                ]
                response = llm_with_tools.invoke(messages)
                
                return {"messages": [response]}
                
            except Exception as e:
                print(f"⚠️ 搜索协调失败: {e}")
                # 简化的搜索执行
                return {"messages": [AIMessage(content="开始搜索相关信息...")]}
        else:
            # 模拟搜索协调
            return {"messages": [AIMessage(content="正在协调多个搜索引擎进行信息收集...")]}
    
    def result_processor_node(state: ResearchState):
        """结果处理节点"""
        # 模拟搜索结果处理
        queries = state.get("optimized_queries", [])
        search_results = []
        
        # 为每个查询生成模拟搜索结果
        for query in queries:
            web_result = json.loads(web_search.invoke({"query": query, "num_results": 3}))
            academic_result = json.loads(academic_search.invoke({"query": query, "num_results": 2}))
            news_result = json.loads(news_search.invoke({"query": query, "days": 7}))
            
            search_results.extend(web_result)
            search_results.extend(academic_result)
            search_results.extend(news_result)
        
        # 去重和筛选
        filtered_results = deduplicate_results(search_results)
        
        return {
            "search_results": search_results,
            "filtered_results": filtered_results
        }
    
    def content_summarizer_node(state: ResearchState):
        """内容总结节点"""
        filtered_results = state.get("filtered_results", [])
        original_query = state.get("original_query", "")
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        system_prompt = f"""
你是内容总结专家。基于搜索结果为用户查询"{original_query}"生成综合总结。

总结要求：
1. 客观准确，基于事实
2. 结构清晰，逻辑连贯
3. 突出重点信息
4. 注明信息来源
5. 评估信息可信度

请生成一个结构化的总结报告。
"""
        
        # 构造搜索结果摘要
        results_summary = "\n".join([
            f"来源{i+1}: {result.get('title', 'unknown')} - {str(result.get('snippet', result.get('abstract', result.get('content', ''))))[:200]}..."
            for i, result in enumerate(filtered_results[:10])  # 限制处理的结果数量
        ])
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=40)
                
                response = llm.invoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=f"搜索结果：\n{results_summary}")
                ])
                
                summary_content = response.content
                
            except Exception as e:
                print(f"⚠️ 内容总结失败: {e}")
                summary_content = f"关于{original_query}的研究总结：基于{len(filtered_results)}个信息源的综合分析。"
        else:
            # 模拟总结
            summary_content = f"""
关于"{original_query}"的研究总结：

## 主要发现
基于{len(filtered_results)}个信息源的分析，{original_query}是一个重要且活跃的研究领域。

## 核心观点
1. 技术发展迅速，应用前景广阔
2. 学术界和产业界都高度关注
3. 存在一些挑战和机遇

## 信息来源
- 网络搜索：{len([r for r in filtered_results if 'example.com' in r.get('url', '')])}个结果
- 学术论文：{len([r for r in filtered_results if 'academic' in r.get('url', '')])}篇
- 新闻报道：{len([r for r in filtered_results if 'news' in r.get('url', '')])}条
"""
        
        return {"summary_content": summary_content}
    
    def report_generator_node(state: ResearchState):
        """报告生成节点"""
        summary_content = state.get("summary_content", "")
        original_query = state.get("original_query", "")
        sources = state.get("sources", [])
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        system_prompt = """
你是报告生成专家。将总结内容转换为专业的研究报告格式。

报告结构：
1. 执行摘要
2. 研究背景
3. 主要发现
4. 详细分析
5. 结论和建议
6. 参考来源

请生成一份完整的研究报告。
"""
        
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.4, api_key=api_key, timeout=40)
                
                response = llm.invoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=f"""
研究主题：{original_query}
总结内容：{summary_content}
信息来源数量：{len(sources)}

请生成正式的研究报告。
""")
                ])
                
                final_report = response.content
                
            except Exception as e:
                print(f"⚠️ 报告生成失败: {e}")
                final_report = f"# {original_query} 研究报告\n\n{summary_content}"
        else:
            # 模拟报告生成
            final_report = f"""
# {original_query} 研究报告

## 执行摘要
本报告深入分析了{original_query}的相关情况，基于多个信息源进行综合研究。

## 研究背景
{original_query}是当前备受关注的重要主题，具有重要的理论和实践价值。

## 主要发现
{summary_content}

## 结论和建议
1. 继续关注该领域的发展动态
2. 深入研究相关技术和应用
3. 加强产学研合作

## 参考来源
本报告基于{len(state.get('filtered_results', []))}个信息源的综合分析。

---
报告生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 计算置信度分数
        confidence_score = calculate_confidence_score(state)
        
        return {
            "final_report": final_report,
            "confidence_score": confidence_score
        }
    
    # 构建图
    graph = StateGraph(ResearchState)
    
    # 添加节点
    graph.add_node("query_optimizer", query_optimizer_node)
    graph.add_node("search_coordinator", search_coordinator_node)
    graph.add_node("tools", tool_node)
    graph.add_node("result_processor", result_processor_node)
    graph.add_node("content_summarizer", content_summarizer_node)
    graph.add_node("report_generator", report_generator_node)
    
    # 设置流程
    graph.add_edge(START, "query_optimizer")
    graph.add_edge("query_optimizer", "search_coordinator")
    
    # 工具调用路由
    graph.add_conditional_edges(
        "search_coordinator",
        tools_condition,
        {
            "tools": "tools",
            "__end__": "result_processor"  # 如果没有工具调用，直接处理结果
        }
    )
    
    graph.add_edge("tools", "result_processor")
    graph.add_edge("result_processor", "content_summarizer")
    graph.add_edge("content_summarizer", "report_generator")
    graph.add_edge("report_generator", END)
    
    return graph.compile()

def deduplicate_results(results: List[Dict]) -> List[Dict]:
    """去重搜索结果"""
    seen_urls = set()
    seen_titles = set()
    filtered = []
    
    for result in results:
        # 简化的去重逻辑
        url = result.get("url", "")
        title = result.get("title", "")
        
        if url not in seen_urls and title not in seen_titles:
            seen_urls.add(url)
            seen_titles.add(title)
            filtered.append(result)
    
    return filtered

def calculate_confidence_score(state: ResearchState) -> float:
    """计算置信度分数"""
    search_results = state.get("search_results", [])
    filtered_results = state.get("filtered_results", [])
    
    # 简化的置信度计算
    if not search_results:
        return 0.0
    
    # 基于结果数量、来源多样性等因素计算
    result_count = len(filtered_results)
    source_types = len(set(
        'academic' if 'academic' in r.get('url', '') else
        'news' if 'news' in r.get('url', '') else 'web'
        for r in search_results
    ))
    
    confidence = min(1.0, (source_types * 0.4 + min(result_count / 15, 1.0) * 0.6))
    return round(confidence, 2)
```

#### 3. 演示和测试

```python
def demo_research_assistant():
    """演示研究助手"""
    research_app = create_research_assistant()
    
    print("🔍 智能研究助手启动成功！")
    print("📚 我可以帮您搜索、分析和总结各种主题的信息")
    print("输入 'quit' 退出\n")
    
    # 自动测试
    test_queries = [
        "人工智能在教育领域的应用",
        "区块链技术的发展趋势",
        "可持续发展与环保技术"
    ]
    
    print("🧪 自动测试模式：")
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*60}")
        print(f"📋 研究主题 {i}: {query}")
        print("🔄 开始研究，请稍候...")
        
        try:
            # 初始状态
            initial_state = {
                "messages": [HumanMessage(content=query)],
                "original_query": "",
                "optimized_queries": [],
                "search_results": [],
                "filtered_results": [],
                "summary_content": "",
                "final_report": "",
                "research_depth": 1,
                "sources": [],
                "confidence_score": 0.0
            }
            
            # 执行研究
            result = research_app.invoke(initial_state)
            
            # 显示结果
            print(f"\n📊 研究报告 (置信度: {result.get('confidence_score', 0):.2f})")
            print("=" * 60)
            print(result.get('final_report', '报告生成失败'))
            print("=" * 60)
            
            # 显示统计信息
            print(f"📈 研究统计:")
            print(f"   - 优化查询: {len(result.get('optimized_queries', []))} 个")
            print(f"   - 搜索结果: {len(result.get('search_results', []))} 个")
            print(f"   - 筛选结果: {len(result.get('filtered_results', []))} 个")
            
        except Exception as e:
            print(f"❌ 研究过程中出错: {e}")
    
    # 交互模式
    print(f"\n{'='*60}")
    print("🎯 交互模式（输入quit退出）：")
    
    while True:
        user_query = input("\n📝 请输入研究主题: ").strip()
        if user_query.lower() == 'quit':
            print("📚 研究愉快！感谢使用智能研究助手！")
            break
        
        if not user_query:
            continue
        
        try:
            print("🔄 开始研究，请稍候...")
            
            # 初始状态
            initial_state = {
                "messages": [HumanMessage(content=user_query)],
                "original_query": "",
                "optimized_queries": [],
                "search_results": [],
                "filtered_results": [],
                "summary_content": "",
                "final_report": "",
                "research_depth": 1,
                "sources": [],
                "confidence_score": 0.0
            }
            
            # 执行研究
            result = research_app.invoke(initial_state)
            
            # 显示结果
            print(f"\n📊 研究报告 (置信度: {result.get('confidence_score', 0):.2f})")
            print("=" * 60)
            print(result.get('final_report', '报告生成失败'))
            print("=" * 60)
            
            # 询问是否需要深入研究
            follow_up = input("\n🔍 需要深入研究某个方面吗？(输入具体问题或按回车继续): ").strip()
            if follow_up:
                print(f"🎯 深入研究: {follow_up}")
                # 这里可以实现深入研究的逻辑
                
        except Exception as e:
            print(f"❌ 研究过程中出错: {e}")

if __name__ == "__main__":
    demo_research_assistant()
```

### 项目2总结

通过这个智能研究助手项目，我们实践了：

**✅ 高级技能**
- **工具链协作**：多个搜索工具的协调使用
- **并行处理**：同时执行多个搜索查询
- **数据处理**：搜索结果的去重、筛选和整合
- **内容生成**：结构化报告的自动生成
- **质量评估**：置信度评分和来源追踪
- **流式处理**：分步骤展示研究进展

**🚀 应用价值**
这个项目展示了如何构建复杂的信息处理流水线，适用于：
- 学术研究和论文写作
- 市场调研和竞品分析
- 政策解读和趋势分析
- 技术调研和方案选型

---

## 🔧 环境准备

在运行本章的示例代码之前，请确保已安装必要的依赖：

```bash
# 使用uv安装依赖
uv add langchain-community langgraph langchain-core typing-extensions
```

并设置智谱AI API密钥：

```bash
export ZHIPUAI_API_KEY="your_zhipu_api_key_here"
```

## 🚀 运行示例

```python
def demo_comprehensive_projects():
    """演示综合项目"""
    print("🚀 LangGraph 综合应用项目演示")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置")
    else:
        print("⚠️ 未配置智谱API密钥，将使用模拟模式")
    
    projects = {
        "1": {
            "name": "天气问答小助手",
            "difficulty": "⭐⭐",
            "description": "智能天气查询、意图识别、工具调用",
            "demo": demo_weather_assistant
        },
        "2": {
            "name": "智能搜索-总结-汇报 Agent", 
            "difficulty": "⭐⭐⭐",
            "description": "多源搜索、内容总结、报告生成",
            "demo": demo_research_assistant
        }
    }
    
    print(f"\n📋 可用项目:")
    for key, project in projects.items():
        print(f"   {key}. {project['name']} {project['difficulty']}")
        print(f"      {project['description']}")
    
    print(f"\n🎯 选择项目进行演示:")
    choice = input("请选择项目编号 (1-2): ").strip()
    
    if choice in projects:
        selected_project = projects[choice]
        print(f"\n🚀 启动 {selected_project['name']}")
        print("=" * 60)
        
        try:
            selected_project["demo"]()
        except Exception as e:
            print(f"❌ 项目运行出错: {e}")
    else:
        print("❌ 无效的项目选择")

if __name__ == "__main__":
    demo_comprehensive_projects()
```

---

## 📚 本章小结

在这一章中，我们通过2个完整的综合项目，实践了LangGraph的核心能力：

### 🎯 项目1：天气问答小助手
- **难度等级**：⭐⭐ 入门级
- **核心技能**：状态管理、工具调用、意图识别、条件路由
- **实际价值**：掌握LangGraph基础开发模式

### 🎯 项目2：智能搜索-总结-汇报 Agent  
- **难度等级**：⭐⭐⭐ 中级
- **核心技能**：工具链协作、并行处理、内容生成、质量评估
- **实际价值**：构建复杂信息处理系统

### 📈 技能提升路径

**从项目1到项目2，我们的能力提升包括：**
1. **简单→复杂**：从单一工具到工具链协作
2. **同步→异步**：从顺序执行到并行处理  
3. **静态→动态**：从固定流程到智能路由
4. **基础→高级**：从简单状态到复杂状态管理

### 🚀 下一步建议

1. **深度实践**：完整实现每个项目，理解每个细节
2. **自主扩展**：根据自己的需求添加新功能
3. **生产部署**：将项目部署到实际环境中使用
4. **持续优化**：根据用户反馈不断改进

### 🎯 学习成果

完成这些项目后，你将具备：
- ✅ LangGraph应用开发的完整技能
- ✅ 复杂AI系统的架构设计能力
- ✅ 工具集成和状态管理的实战经验
- ✅ 错误处理和用户体验优化技巧

**恭喜你！现在你已经具备了构建复杂AI应用的核心能力！** 🎉

在第12章中，我们将解决在开发过程中经常遇到的**常见问题和最佳实践**，帮你避免常见的坑，成为真正的LangGraph专家！