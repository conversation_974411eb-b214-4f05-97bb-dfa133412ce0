# 第2章 快速跑起来：第一个聊天机器人

理论再多，不如动手一试。在这一章中，我们将从零开始构建你的第一个 LangGraph 应用——一个简单但功能完整的聊天机器人。通过这个实践，你将真正理解 LangGraph 的工作原理。

## 2.1 安装与环境准备

### 系统要求

在开始之前，确保你的系统满足以下要求：

- **Python 版本**：3.8 或更高版本（推荐 3.10+）
- **uv**：现代 Python 包管理器（[安装方法](https://docs.astral.sh/uv/getting-started/installation/)）
- **操作系统**：Windows、macOS 或 Linux
- **内存**：至少 4GB RAM（推荐 8GB+）
- **网络**：稳定的互联网连接（用于下载依赖和调用 API）

### 安装 uv

如果还没有安装 uv，可以通过以下方式安装：

```bash
# macOS/Linux (推荐)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 或者通过 pip 安装
pip install uv
```

验证 uv 安装：

```bash
uv --version
```

### 创建项目环境

使用 uv 创建一个干净的 Python 项目：

```bash
# 创建并初始化项目
uv init my-first-langgraph-bot
cd my-first-langgraph-bot

# uv 会自动创建 pyproject.toml 和虚拟环境
# 查看项目结构
ls -la
```

**uv 的优势：**
- 🚀 **速度快**：比 pip 快 10-100 倍
- 🔒 **依赖锁定**：自动生成 `uv.lock` 确保可重复构建
- 📦 **统一管理**：项目配置、依赖、虚拟环境一体化
- 🛡️ **安全可靠**：内置依赖解析和冲突检测

### 安装核心依赖

使用 uv 安装项目依赖：

```bash
# 安装 LangGraph 和相关依赖
uv add langgraph

# 安装 LangChain 和智谱AI支持
uv add langchain langchain-openai

# 安装环境变量管理
uv add python-dotenv

# 安装可选依赖（开发和可视化）
uv add --dev matplotlib jupyter

# 查看已安装的依赖
uv tree
```

**uv add 命令说明：**
- `uv add package`：添加生产依赖
- `uv add --dev package`：添加开发依赖
- 依赖会自动写入 `pyproject.toml` 文件
- 同时生成 `uv.lock` 锁定文件确保版本一致性

### 项目结构

uv 创建的项目结构如下：

```
my-first-langgraph-bot/
├── pyproject.toml      # 项目配置和依赖定义
├── uv.lock            # 锁定文件，确保可重现构建
├── .python-version    # Python 版本锁定
├── .env               # 环境变量（需手动创建）
├── src/               # 源代码目录（可选）
├── tests/             # 测试目录（可选）
└── README.md          # 项目说明
```

**uv 常用命令：**
```bash
uv add package         # 添加依赖
uv remove package      # 移除依赖
uv tree               # 查看依赖树
uv run command        # 在虚拟环境中运行命令
uv shell              # 激活虚拟环境shell
uv sync               # 同步依赖（基于uv.lock）
uv python pin 3.11    # 锁定Python版本
```

### 配置 API 密钥

创建 `.env` 文件来管理环境变量（python-dotenv 已经在上一步安装）：

```env
# 智谱AI API 密钥
ZHIPU_API_KEY=your_zhipu_api_key_here
```

**重要提示：**
- 从 [智谱AI开放平台](https://open.bigmodel.cn/) 注册账户并获取 API 密钥
- 确保 `.env` 文件不要提交到版本控制系统
- GLM-4.5 是智谱AI推出的大语言模型，性能优异且价格实惠

**获取智谱AI API密钥步骤：**
1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册并登录账户
3. 进入 API Keys 页面
4. 创建新的 API Key
5. 复制密钥到 `.env` 文件中

### 验证安装

创建一个简单的测试文件 `test_setup.py`：

```python
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langgraph import __version__ as langgraph_version

# 加载环境变量
load_dotenv()

print(f"LangGraph 版本: {langgraph_version}")
print(f"智谱AI API Key 已配置: {'是' if os.getenv('ZHIPU_API_KEY') else '否'}")

# 测试智谱AI GLM-4.5连接
try:
    llm = ChatOpenAI(
        model="glm-4.5",
        api_key=os.getenv("ZHIPU_API_KEY"),
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0
    )
    response = llm.invoke("你好，世界！")
    print(f"智谱AI GLM-4.5 连接测试成功: {response.content[:50]}...")
except Exception as e:
    print(f"智谱AI GLM-4.5 连接测试失败: {e}")
```

运行测试：

```bash
# 使用 uv run 执行脚本（自动激活虚拟环境）
uv run python test_setup.py

# 或者先进入 uv shell 再运行
uv shell
python test_setup.py
```

如果一切正常，你应该看到类似的输出：

```
LangGraph 版本: 0.2.x
智谱AI API Key 已配置: 是
智谱AI GLM-4.5 连接测试成功: 你好！我是智谱AI助手，很高兴为您服务。有什么我可以帮助您的吗？...
```

## 2.2 创建 StateGraph 与节点

现在让我们构建第一个 LangGraph 应用。创建 `chatbot.py` 文件：

### 定义状态结构

```python
import os
from typing import Annotated
from typing_extensions import TypedDict
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, AnyMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

# 加载环境变量
load_dotenv()

# 定义图的状态
class State(TypedDict):
    # 使用 add_messages 作为 reducer，自动处理消息列表的更新
    messages: Annotated[list[AnyMessage], add_messages]
```

**状态解释：**
- `State` 是我们图的核心数据结构
- `messages` 字段存储对话历史
- `Annotated[list[AnyMessage], add_messages]` 告诉 LangGraph 如何更新消息列表
- `add_messages` 是一个内置的 reducer，能智能地处理消息的添加和更新

### 创建聊天节点

```python
def chatbot_node(state: State):
    """
    聊天机器人节点：接收用户消息，生成 AI 回复
    """
    # 初始化智谱AI GLM-4.5模型
    llm = ChatOpenAI(
        model="glm-4.5",
        api_key=os.getenv("ZHIPU_API_KEY"),
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0.7,  # 稍微增加创造性
        max_tokens=1000
    )
    
    # 调用 LLM 生成回复
    response = llm.invoke(state["messages"])
    
    # 返回状态更新
    return {"messages": [response]}
```

**节点解释：**
- 节点是一个普通的 Python 函数
- 接收当前状态作为输入
- 执行具体的业务逻辑（这里是调用 LLM）
- 返回状态更新（这里是新的 AI 消息）

### 构建状态图

```python
def create_chatbot_graph():
    """
    创建聊天机器人的状态图
    """
    # 创建状态图
    graph = StateGraph(State)
    
    # 添加节点
    graph.add_node("chatbot", chatbot_node)
    
    # 设置入口点：从 START 到 chatbot 节点
    graph.add_edge(START, "chatbot")
    
    # 设置出口点：从 chatbot 节点到 END
    graph.add_edge("chatbot", END)
    
    return graph
```

**图结构解释：**
```
START → chatbot → END
```
- `START` 是特殊的起始节点，代表用户输入进入图的地方
- `chatbot` 是我们定义的处理节点
- `END` 是特殊的结束节点，代表图执行完成

## 2.3 编译、可视化与运行

### 编译图

```python
def main():
    # 创建图
    graph = create_chatbot_graph()
    
    # 编译图（必须步骤）
    app = graph.compile()
    
    return app

if __name__ == "__main__":
    app = main()
    print("聊天机器人已准备就绪！")
```

**编译的作用：**
- 验证图的结构是否正确（没有孤立节点等）
- 优化执行路径
- 准备运行时环境
- 添加调试和监控功能

### 可视化图结构

让我们添加可视化功能，看看我们构建的图长什么样：

```python
def visualize_graph(app):
    """
    可视化图结构
    """
    try:
        # 生成图的可视化
        from IPython.display import Image, display
        display(Image(app.get_graph().draw_mermaid_png()))
    except Exception as e:
        print(f"可视化需要额外依赖，跳过: {e}")
        # 打印文本版本的图结构
        print("\n图结构（文本版本）:")
        print("START → chatbot → END")
```

### 运行聊天机器人

```python
def chat_with_bot(app):
    """
    与聊天机器人交互
    """
    print("🤖 聊天机器人已启动！输入 'quit' 退出。\n")
    
    while True:
        # 获取用户输入
        user_input = input("👤 你: ").strip()
        
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
            
        if not user_input:
            continue
            
        try:
            # 构造输入状态
            initial_state = {
                "messages": [HumanMessage(content=user_input)]
            }
            
            # 运行图
            result = app.invoke(initial_state)
            
            # 获取 AI 回复
            ai_message = result["messages"][-1]
            print(f"🤖 机器人: {ai_message.content}\n")
            
        except Exception as e:
            print(f"❌ 出错了: {e}\n")
```

### 完整的运行代码

将所有代码整合到 `chatbot.py` 中：

```python
import os
from typing import Annotated
from typing_extensions import TypedDict
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, AnyMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

# 加载环境变量
load_dotenv()

# 定义图的状态
class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]

def chatbot_node(state: State):
    """聊天机器人节点"""
    llm = ChatOpenAI(
        model="glm-4.5",
        api_key=os.getenv("ZHIPU_API_KEY"),
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0.7,
        max_tokens=1000
    )
    response = llm.invoke(state["messages"])
    return {"messages": [response]}

def create_chatbot_graph():
    """创建聊天机器人图"""
    graph = StateGraph(State)
    graph.add_node("chatbot", chatbot_node)
    graph.add_edge(START, "chatbot")
    graph.add_edge("chatbot", END)
    return graph

def chat_with_bot(app):
    """交互式聊天"""
    print("🤖 聊天机器人已启动！输入 'quit' 退出。\n")
    
    while True:
        user_input = input("👤 你: ").strip()
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("👋 再见！")
            break
        if not user_input:
            continue
            
        try:
            initial_state = {"messages": [HumanMessage(content=user_input)]}
            result = app.invoke(initial_state)
            ai_message = result["messages"][-1]
            print(f"🤖 机器人: {ai_message.content}\n")
        except Exception as e:
            print(f"❌ 出错了: {e}\n")

def main():
    # 创建并编译图
    graph = create_chatbot_graph()
    app = graph.compile()
    
    # 可视化图结构（可选）
    print("📊 图结构: START → chatbot → END")
    
    # 开始聊天
    chat_with_bot(app)

if __name__ == "__main__":
    main()
```

### 运行你的第一个聊天机器人

```bash
# 使用 uv run 运行聊天机器人
uv run python chatbot.py

# 或者在 uv shell 中运行
uv shell
python chatbot.py
```

你应该看到类似的输出：

```
📊 图结构: START → chatbot → END
🤖 聊天机器人已启动！输入 'quit' 退出。

👤 你: 你好！
🤖 机器人: 您好！我是基于智谱AI GLM-4.5的助手，很高兴为您服务。请问有什么可以帮助您的吗？

👤 你: 你能做什么？
🤖 机器人: 我可以为您提供多方面的帮助，包括：
- 回答各类知识性问题
- 协助写作和文本编辑
- 进行创意讨论和头脑风暴
- 提供问题解决建议
- 解释复杂概念和技术原理
- 代码编写和调试协助
- 翻译和语言学习支持

有什么具体需要帮助的吗？

👤 你: quit
👋 再见！
```

## 2.4 逐行代码解读：白话理解

现在让我们深入理解每一行代码的作用，以及为什么要这样写。

### 状态定义的奥秘

```python
class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
```

**为什么这样定义？**

1. **TypedDict**：提供类型提示，让 IDE 和 LangGraph 知道状态的结构
2. **messages 字段**：存储对话历史，这是聊天应用的核心数据
3. **Annotated[list[AnyMessage], add_messages]**：这是关键！
   - `list[AnyMessage]`：消息列表的类型
   - `add_messages`：告诉 LangGraph 如何更新这个列表

**add_messages 的魔法：**

```python
# 如果没有 add_messages，每次更新都会覆盖整个列表
# 有了 add_messages，它会智能地：
# 1. 追加新消息到列表末尾
# 2. 如果消息有相同 ID，则更新而不是重复添加
# 3. 自动处理消息的序列化和反序列化
```

### 节点函数的设计哲学

```python
def chatbot_node(state: State):
    llm = ChatOpenAI(
        model="glm-4.5",
        api_key=os.getenv("ZHIPU_API_KEY"),
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0.7
    )
    response = llm.invoke(state["messages"])
    return {"messages": [response]}
```

**设计原则解析：**

1. **输入参数**：`state: State`
   - 节点函数的第一个参数总是当前状态
   - 通过类型提示，我们知道能访问哪些数据

2. **业务逻辑**：`llm.invoke(state["messages"])`
   - 这里是节点的核心功能
   - 接收所有历史消息，生成新回复

3. **返回值**：`{"messages": [response]}`
   - 返回状态更新，而不是完整状态
   - LangGraph 会自动合并这个更新到当前状态

**为什么不返回完整状态？**

```python
# 不推荐：返回完整状态
def bad_node(state):
    # ... 处理逻辑
    return {
        "messages": state["messages"] + [new_message],  # 需要手动管理所有字段
    }

# 推荐：只返回更新
def good_node(state):
    # ... 处理逻辑
    return {"messages": [new_message]}  # LangGraph 自动合并
```

### 图构建的逻辑

```python
graph = StateGraph(State)
graph.add_node("chatbot", chatbot_node)
graph.add_edge(START, "chatbot")
graph.add_edge("chatbot", END)
```

**构建步骤解析：**

1. **创建图实例**：`StateGraph(State)`
   - 告诉 LangGraph 这个图使用什么状态结构

2. **添加节点**：`add_node("chatbot", chatbot_node)`
   - 第一个参数是节点名称（字符串）
   - 第二个参数是节点函数

3. **连接节点**：`add_edge(START, "chatbot")`
   - 定义执行顺序
   - START → chatbot → END 形成一条简单的执行链

**为什么需要 START 和 END？**
- `START`：标记用户输入进入图的位置
- `END`：标记图执行完成的位置
- 这样设计让图的执行边界非常清晰

### 编译和运行的机制

```python
app = graph.compile()
result = app.invoke(initial_state)
```

**编译过程做了什么？**
1. 验证图结构（检查是否有孤立节点）
2. 优化执行路径
3. 设置状态管理机制
4. 准备调试和监控钩子

**invoke 方法的执行流程：**
```
1. 接收初始状态 {"messages": [HumanMessage("你好")]}
2. 从 START 节点开始
3. 路由到 chatbot 节点
4. 执行 chatbot_node 函数
5. 更新状态：添加 AI 回复
6. 到达 END 节点
7. 返回最终状态
```

### 状态更新的细节

让我们追踪一次完整的状态变化：

```python
# 初始状态
initial_state = {
    "messages": [HumanMessage(content="你好")]
}

# chatbot_node 执行后返回
node_output = {
    "messages": [AIMessage(content="您好！我是智谱AI助手，很高兴为您服务。")]
}

# LangGraph 自动合并状态（使用 add_messages reducer）
final_state = {
    "messages": [
        HumanMessage(content="你好"),                           # 原有消息
        AIMessage(content="您好！我是智谱AI助手，很高兴为您服务。")  # 新增消息
    ]
}
```

**add_messages 的智能之处：**
- 如果是新消息，追加到列表末尾
- 如果消息有相同 ID，更新现有消息
- 自动处理不同类型的消息对象

## 2.5 常见问题排查：环境配置与依赖问题

在运行第一个 LangGraph 应用时，你可能会遇到一些常见问题。让我们逐一解决：

### 问题1：ImportError - 找不到模块

**错误信息：**
```
ImportError: No module named 'langgraph'
```

**解决方案：**
```bash
# 使用 uv 重新安装 LangGraph
uv add langgraph --upgrade

# 或者移除后重新安装
uv remove langgraph
uv add langgraph

# 验证安装
uv run python -c "import importlib.metadata; print(importlib.metadata.version('langgraph'))"
```

### 问题2：智谱AI API 相关错误

**错误信息：**
```
AuthenticationError: Incorrect API key provided
```

**解决方案：**
1. 检查 `.env` 文件是否存在且格式正确
2. 验证智谱AI API 密钥是否有效
3. 确认账户有足够的额度
4. 检查base_url是否正确

```python
# 调试智谱AI API 密钥配置
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('ZHIPU_API_KEY')
print(f"智谱AI API Key 长度: {len(api_key) if api_key else 0}")
print(f"智谱AI API Key 前缀: {api_key[:10] if api_key else 'None'}...")

# 测试完整配置
try:
    from langchain_openai import ChatOpenAI
    llm = ChatOpenAI(
        model="glm-4.5",
        api_key=api_key,
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0
    )
    response = llm.invoke("测试连接")
    print("智谱AI连接成功")
except Exception as e:
    print(f"智谱AI连接失败: {e}")
```

### 问题3：网络连接问题

**错误信息：**
```
requests.exceptions.ConnectionError: Failed to establish a new connection
```

**解决方案：**
```python
# 添加重试机制和超时设置
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(
    model="glm-4.5",
    api_key=os.getenv("ZHIPU_API_KEY"),
    base_url="https://open.bigmodel.cn/api/paas/v4/",
    temperature=0.7,
    request_timeout=30,  # 30秒超时
    max_retries=3        # 最多重试3次
)
```

### 问题4：内存不足

**错误信息：**
```
MemoryError: Unable to allocate memory
```

**解决方案：**
```python
# 限制模型参数
llm = ChatOpenAI(
    model="glm-4.5",
    api_key=os.getenv("ZHIPU_API_KEY"),
    base_url="https://open.bigmodel.cn/api/paas/v4/",
    max_tokens=500,         # 限制输出长度
    temperature=0.7
)
```

### 问题5：图编译失败

**错误信息：**
```
ValueError: Graph has no entry point
```

**解决方案：**
```python
# 确保图有正确的入口和出口
graph = StateGraph(State)
graph.add_node("chatbot", chatbot_node)

# 必须设置入口点
graph.add_edge(START, "chatbot")

# 必须设置出口点
graph.add_edge("chatbot", END)

app = graph.compile()
```

### 问题6：状态更新不生效

**错误信息：**
消息没有正确添加到对话历史中

**解决方案：**
```python
# 确保使用正确的 reducer
from langgraph.graph.message import add_messages
from typing import Annotated

class State(TypedDict):
    # 正确：使用 add_messages reducer
    messages: Annotated[list[AnyMessage], add_messages]
    
    # 错误：没有 reducer，会覆盖整个列表
    # messages: list[AnyMessage]
```

### 调试技巧

**1. 添加日志输出**
```python
def chatbot_node(state: State):
    print(f"节点输入: {len(state['messages'])} 条消息")
    llm = ChatOpenAI(
        model="glm-4.5",
        api_key=os.getenv("ZHIPU_API_KEY"),
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0.7
    )
    response = llm.invoke(state["messages"])
    print(f"GLM-4.5 回复: {response.content[:50]}...")
    return {"messages": [response]}
```

**2. 使用流式输出查看执行过程**
```python
# 使用 stream 方法查看每个步骤
for chunk in app.stream(initial_state):
    print(f"步骤输出: {chunk}")
```

**3. 检查图结构**
```python
# 打印图的节点和边
compiled_graph = app.get_graph()
print(f"节点: {compiled_graph.nodes}")
print(f"边: {compiled_graph.edges}")
```

### 性能优化建议

**1. 模型选择**
```python
# 开发阶段：使用GLM-4.5（性价比高）
llm = ChatOpenAI(
    model="glm-4.5",
    api_key=os.getenv("ZHIPU_API_KEY"),
    base_url="https://open.bigmodel.cn/api/paas/v4/"
)

# 生产阶段：可以调整参数优化性能
llm = ChatOpenAI(
    model="glm-4.5",
    api_key=os.getenv("ZHIPU_API_KEY"),
    base_url="https://open.bigmodel.cn/api/paas/v4/",
    max_tokens=2000,  # 根据需求调整
    temperature=0.3   # 更稳定的输出
)
```

**2. 缓存配置**
```python
# 启用缓存减少重复调用
from langchain_core.caches import InMemoryCache
from langchain_core.globals import set_llm_cache

set_llm_cache(InMemoryCache())
```

**3. 异步处理**
```python
# 对于高并发场景，使用异步版本
async def async_chatbot_node(state: State):
    llm = ChatOpenAI(
        model="glm-4.5",
        api_key=os.getenv("ZHIPU_API_KEY"),
        base_url="https://open.bigmodel.cn/api/paas/v4/"
    )
    response = await llm.ainvoke(state["messages"])
    return {"messages": [response]}
```

---

**🎉 恭喜！** 你已经成功构建并运行了第一个 LangGraph 应用！

在这一章中，我们：
- ✅ 使用 **uv** 搭建了现代化的开发环境
- ✅ 理解了状态图的基本概念
- ✅ 创建了一个功能完整的聊天机器人
- ✅ 学会了调试和排查常见问题
- ✅ 掌握了 uv 的项目管理和依赖处理

虽然这个聊天机器人还很简单，但它已经展示了 LangGraph 的核心特性：状态管理、节点执行、图编排。在下一章中，我们将深入探讨这些核心概念，为构建更复杂的应用打下坚实基础。

**🔮 下一步预告：**
在第3章中，我们将详细解析状态、节点、边等核心概念，并学习如何设计更复杂的对话流程。准备好了吗？