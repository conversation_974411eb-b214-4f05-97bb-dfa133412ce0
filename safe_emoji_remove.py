#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全地从 Markdown 文件中移除 emoji 符号
- 删除所有 emoji 符号，除了 ⭐
- 将 ⭐ 替换为 *
- 保持原有的 markdown 格式不变
"""

import os
import re
import glob
from pathlib import Path

def safe_remove_emojis(text):
    """
    安全地从文本中移除 emoji 符号，保持格式不变
    """
    # 先将 ⭐ 替换为 *
    text = text.replace('⭐', '*')
    
    # 定义具体的 emoji 字符，使用更精确的匹配
    specific_emojis = [
        '🤖', '👤', '👋', '💡', '🔧', '🤔', '📚',
        '🌱', '🚀', '💼', '📈', '📖', '🎉', '❌',
        '✅', '🧮', '🔍', '🌤️', '⏰', '📝', '🤝',
        '⚠️', '⏸️', '📋', '👨‍💼', '🔄', '📤', '📥',
        '👥', '⭐'  # 这个已经在上面处理了，但保险起见
    ]
    
    # 逐个替换具体的emoji
    for emoji in specific_emojis:
        text = text.replace(emoji, '')
    
    # 清理连续的空格（但保留换行符）
    # 将多个连续空格替换为单个空格，但不影响换行
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # 清理每行内的多余空格，但保留行结构
        cleaned_line = re.sub(r'[ \t]+', ' ', line).strip()
        cleaned_lines.append(cleaned_line)
    
    # 重新组合，保持原有的行结构
    result = '\n'.join(cleaned_lines)
    
    # 清理多余的空行（3个以上连续空行改为2个）
    result = re.sub(r'\n\s*\n\s*\n+', '\n\n', result)
    
    return result

def process_markdown_file_safe(file_path):
    """
    安全地处理单个 markdown 文件
    """
    try:
        print(f"处理文件: {file_path}")
        
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件（以防万一）
        backup_path = file_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 安全地移除 emoji
        cleaned_content = safe_remove_emojis(content)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
            
        print(f"✓ 完成: {file_path}")
        print(f"  备份保存在: {backup_path}")
        
        # 如果处理成功，删除备份文件
        # os.remove(backup_path)  # 先保留备份以确保安全
        
    except Exception as e:
        print(f"✗ 错误处理 {file_path}: {e}")
        # 如果出错，从备份恢复
        backup_path = file_path + '.backup'
        if os.path.exists(backup_path):
            with open(backup_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            print(f"  已从备份恢复: {file_path}")

def main():
    """
    主函数：安全地批量处理所有 markdown 文件
    """
    # 获取当前目录下的所有 .md 文件
    md_files = glob.glob("*.md")
    
    print(f"找到 {len(md_files)} 个 markdown 文件")
    print("开始安全处理...\n")
    
    for file_path in md_files:
        process_markdown_file_safe(file_path)
    
    print(f"\n处理完成！共处理了 {len(md_files)} 个文件")
    print("备份文件以 .backup 结尾，处理完成后可以删除")

if __name__ == "__main__":
    main()