# 第5章 记忆与状态管理

想象一下，如果你每次和朋友聊天都要重新自我介绍，每次都忘记之前说过的话，这样的对话会多么令人沮丧。AI 助手也是如此——没有记忆的助手只能进行一次性的问答，无法提供连贯、个性化的服务。在这一章中，我们将深入探讨 LangGraph 的记忆与状态管理机制，让你的 AI 助手拥有"记忆"。

## 5.1 内存中的短期记忆：MemorySaver 的使用

### 什么是短期记忆？

短期记忆就像人类的工作记忆，用于存储当前对话会话中的信息。在 LangGraph 中，`MemorySaver` 提供了最简单的内存存储方案，适合开发和测试阶段。

```python
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict
from typing import Annotated
from langchain_core.messages import HumanMessage, AIMessage

# 定义状态
class ConversationState(TypedDict):
    messages: Annotated[list, add_messages]
    user_name: str
    conversation_count: int
    topics_discussed: list[str]

def create_memory_chatbot():
    """创建带内存的聊天机器人"""
    # 创建内存保存器
    memory = MemorySaver()
    
    def chatbot_node(state: ConversationState):
        """聊天节点，能够记住对话历史"""
        from langchain_openai import ChatOpenAI
        from langchain_core.messages import SystemMessage
        
        # 构造系统提示，包含用户信息
        user_name = state.get("user_name", "朋友")
        conversation_count = state.get("conversation_count", 0)
        topics = state.get("topics_discussed", [])
        
        system_prompt = f"""
你是一个友好的AI助手。你正在与{user_name}对话。
这是你们的第{conversation_count + 1}次交流。
之前讨论过的话题：{', '.join(topics) if topics else '无'}
请保持对话的连贯性，记住之前的交流内容。
"""
        
        messages = [SystemMessage(content=system_prompt)] + state["messages"]
        llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0.7)
        response = llm.invoke(messages)
        
        # 更新对话计数
        new_count = conversation_count + 1
        
        return {
            "messages": [response],
            "conversation_count": new_count
        }
    
    # 构建图
    graph = StateGraph(ConversationState)
    graph.add_node("chat", chatbot_node)
    graph.add_edge(START, "chat")
    graph.add_edge("chat", END)
    
    # 编译时指定内存保存器
    return graph.compile(checkpointer=memory)
```

### MemorySaver 的特点和限制

**优点：**
- 简单易用，无需配置
- 性能优秀，读写速度快
- 适合开发和测试

**限制：**
- 程序重启后数据丢失
- 无法跨进程共享
- 内存使用量随对话增长

## 5.2 长期持久化方案：SQLite, Postgres 实战

### SQLite：轻量级持久化

SQLite 是一个轻量级的文件数据库，适合中小型应用：

```python
from langgraph.checkpoint.sqlite import SqliteSaver
from pathlib import Path
from datetime import datetime

def create_sqlite_chatbot():
    """创建 SQLite 持久化聊天机器人"""
    # 确保数据目录存在
    data_dir = Path("./data")
    data_dir.mkdir(exist_ok=True)
    
    # 创建 SQLite 检查点保存器
    db_path = data_dir / "chatbot_memory.db"
    memory = SqliteSaver.from_conn_string(str(db_path))
    
    def persistent_chatbot_node(state: ConversationState):
        """持久化聊天节点"""
        from langchain_openai import ChatOpenAI
        from langchain_core.messages import SystemMessage
        
        # 获取用户信息（从持久化状态中）
        user_name = state.get("user_name", "用户")
        total_conversations = state.get("conversation_count", 0)
        
        system_prompt = f"""
你是{user_name}的个人AI助手。
你们已经进行了{total_conversations}次对话。
请保持友好和个性化的交流风格。
"""
        
        messages = [SystemMessage(content=system_prompt)] + state["messages"]
        llm = ChatOpenAI(model="gpt-3.5-turbo")
        response = llm.invoke(messages)
        
        return {
            "messages": [response],
            "conversation_count": total_conversations + 1,
            "last_interaction": datetime.now().isoformat()
        }
    
    # 构建图
    graph = StateGraph(ConversationState)
    graph.add_node("chat", persistent_chatbot_node)
    graph.add_edge(START, "chat")
    graph.add_edge("chat", END)
    
    return graph.compile(checkpointer=memory)
```

## 5.3 自定义状态字段：灵活扩展你的数据

### 设计复杂的状态结构

随着应用复杂度的增加，你需要存储更多类型的信息：

```python
from datetime import datetime
from typing import Optional, Dict, List
from enum import Enum

class UserRole(Enum):
    GUEST = "guest"
    MEMBER = "member"
    VIP = "vip"
    ADMIN = "admin"

class AdvancedState(TypedDict):
    # 基础消息
    messages: Annotated[list, add_messages]
    
    # 用户信息
    user_id: str
    user_name: Optional[str]
    user_role: UserRole
    user_preferences: Dict[str, any]
    
    # 对话上下文
    current_topic: Optional[str]
    topics_history: List[str]
    conversation_count: int
    
    # 会话状态
    session_start_time: str
    last_activity_time: str
    session_duration: float  # 秒
```

## 5.4 回溯与复盘：时间旅行调试

### 什么是时间旅行？

时间旅行是 LangGraph 的一个强大特性，允许你：
- 查看对话的历史状态
- 回滚到之前的某个时间点
- 从历史状态创建新的分支
- 分析对话的演进过程

```python
class TimeTravelManager:
    """时间旅行管理器"""
    
    def __init__(self, app):
        self.app = app
    
    def get_conversation_history(self, thread_id: str) -> List[Dict]:
        """获取对话历史"""
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # 获取所有历史状态
            history = list(self.app.get_state_history(config))
            conversation_points = []
            
            for i, checkpoint in enumerate(history):
                state = checkpoint.values
                messages = state.get("messages", [])
                
                if messages:
                    last_message = messages[-1]
                    conversation_points.append({
                        "step": i,
                        "timestamp": state.get("timestamp", "未知"),
                        "message_count": len(messages),
                        "last_message": last_message.content[:50] + "..." 
                                       if len(last_message.content) > 50 
                                       else last_message.content,
                        "message_type": type(last_message).__name__
                    })
            
            return conversation_points
            
        except Exception as e:
            print(f"获取历史失败: {e}")
            return []
    
    def rollback_to_step(self, thread_id: str, step: int) -> bool:
        """回滚到指定步骤"""
        config = {"configurable": {"thread_id": thread_id}}
        
        try:
            # 获取历史状态
            history = list(self.app.get_state_history(config))
            
            if step >= len(history):
                print(f"❌ 步骤 {step} 不存在，总共只有 {len(history)} 个步骤")
                return False
            
            # 获取目标状态
            target_checkpoint = history[step]
            
            # 更新当前状态为目标状态
            self.app.update_state(config, target_checkpoint.values)
            print(f"✅ 已回滚到步骤 {step}")
            return True
            
        except Exception as e:
            print(f"❌ 回滚失败: {e}")
            return False
```

### 完整的时间旅行演示

```python
def demo_time_travel():
    """演示时间旅行功能"""
    from langgraph.checkpoint.sqlite import SqliteSaver
    
    # 创建支持时间旅行的聊天机器人
    memory = SqliteSaver.from_conn_string("./data/time_travel_demo.db")
    
    def chatbot_with_timestamp(state: ConversationState):
        from langchain_openai import ChatOpenAI
        from langchain_core.messages import SystemMessage
        
        timestamp = datetime.now().isoformat()
        system_prompt = f"你是AI助手，当前时间：{timestamp}"
        
        messages = [SystemMessage(content=system_prompt)] + state["messages"]
        llm = ChatOpenAI(model="gpt-3.5-turbo")
        response = llm.invoke(messages)
        
        return {
            "messages": [response],
            "timestamp": timestamp,
            "conversation_count": state.get("conversation_count", 0) + 1
        }
    
    # 构建图
    graph = StateGraph(ConversationState)
    graph.add_node("chat", chatbot_with_timestamp)
    graph.add_edge(START, "chat")
    graph.add_edge("chat", END)
    
    app = graph.compile(checkpointer=memory)
    time_manager = TimeTravelManager(app)
    
    thread_id = "time_travel_demo"
    config = {"configurable": {"thread_id": thread_id}}
    
    print("🚀 时间旅行聊天机器人")
    print("📝 支持历史查看、回滚等功能")
    print("\n🎯 可用命令:")
    print("  history - 查看对话历史")
    print("  rollback <步骤> - 回滚到指定步骤")
    print("  quit - 退出\n")
    
    while True:
        user_input = input("🧑 你: ").strip()
        
        if user_input.lower() == 'quit':
            break
        
        # 处理特殊命令
        if user_input.lower() == 'history':
            history = time_manager.get_conversation_history(thread_id)
            if history:
                print("\n📚 对话历史:")
                for point in history:
                    print(f"  步骤 {point['step']}: [{point['message_type']}] {point['last_message']}")
                print()
            else:
                print("📝 暂无对话历史\n")
            continue
        
        if user_input.lower().startswith('rollback '):
            try:
                step = int(user_input.split()[1])
                time_manager.rollback_to_step(thread_id, step)
            except (IndexError, ValueError):
                print("❌ 用法: rollback <步骤号>")
            continue
        
        if not user_input:
            continue
        
        # 正常对话
        try:
            result = app.invoke({
                "messages": [HumanMessage(content=user_input)],
                "conversation_count": 0,
                "topics_discussed": []
            }, config=config)
            
            ai_message = result["messages"][-1]
            print(f"🤖 机器人: {ai_message.content}")
            print(f"⏰ 时间: {result.get('timestamp', '未知')}\n")
            
        except Exception as e:
            print(f"❌ 出错了: {e}\n")

if __name__ == "__main__":
    demo_time_travel()
```

---

**本章小结**

在这一章中，我们深入探讨了 LangGraph 的记忆与状态管理：

- **短期记忆**：使用 MemorySaver 实现快速的内存存储
- **长期持久化**：通过 SQLite 和 PostgreSQL 实现数据持久化
- **自定义状态**：设计复杂的状态结构满足业务需求
- **时间旅行**：实现对话历史的回溯、分支和分析

通过这些技术，你的 AI 助手不仅能够记住对话历史，还能提供个性化服务，支持复杂的业务场景，并具备强大的调试和分析能力。

**下一步预告：**

在第6章中，我们将学习人机协作，探讨如何在 AI 自动化和人工干预之间找到完美平衡，让你的应用既智能又可控。准备好学习如何让 AI 在关键时刻"举手求助"了吗？