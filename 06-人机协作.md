# 第6章 人机协作

AI 再聪明，也有犯错的时候；人类再能干，也有疲惫的时候。最好的解决方案不是让 AI 完全替代人类，而是让两者优势互补——AI 处理常规任务，人类在关键时刻介入决策。这就是"人机协作"（Human-in-the-Loop）的核心理念。

在这一章中，我们将学习如何在 LangGraph 中实现智能的人机协作机制。

## 🔧 环境准备

在开始之前，请确保已正确配置开发环境：

### 使用 uv 管理依赖

```bash
# 安装必要的依赖包
uv add langgraph langchain-core langchain-community typing-extensions

# 查看已安装的包
uv list
```

### 配置智谱GLM-4.5 API

```bash
# 设置环境变量（推荐在.env文件中配置）
export ZHIPUAI_API_KEY=your_zhipu_api_key

# 或者在.env文件中添加
echo "ZHIPUAI_API_KEY=your_zhipu_api_key" >> .env
```

💡 **提示**: 本章代码支持模拟模式，即使没有API密钥也可以运行和学习核心逻辑。

## 6.1 为什么需要人工干预？

### 人工干预的必要性

想象一下这些场景：

- **高风险决策**：AI 要删除重要文件时，需要人类确认
- **模糊情况**：用户意图不明确，AI 需要人类帮助澄清
- **质量控制**：AI 生成的内容需要人类审核后发布
- **学习改进**：通过人类反馈，AI 可以不断优化

```python
# 传统的全自动化流程
用户请求 → AI处理 → 直接执行 → 结果返回

# 人机协作流程
用户请求 → AI分析 → 风险评估 → 需要人工？
                                ↓
           人工审核 → 决策 → 执行 → 结果返回
```

### 什么时候需要人工干预？

**1. 置信度低的情况**

```python
def need_human_review(confidence_score: float) -> bool:
    """根据置信度判断是否需要人工审核"""
    return confidence_score < 0.8  # 置信度低于80%需要人工确认
```

**2. 高风险操作**

```python
HIGH_RISK_ACTIONS = [
    "delete_file",
    "send_email",
    "make_payment",
    "publish_content",
    "modify_database"
]

def is_high_risk_action(action: str) -> bool:
    """判断是否为高风险操作"""
    return action in HIGH_RISK_ACTIONS
```

**3. 异常情况**

```python
def detect_anomaly(user_request: str, context: dict) -> bool:
    """检测异常情况"""
    # 检查是否与历史行为模式不符
    # 检查是否包含敏感内容
    # 检查是否超出权限范围
    return False  # 简化示例
```

### 人机协作的优势

| 方面 | 纯AI自动化 | 人机协作 | 纯人工处理 |
|------|------------|----------|------------|
| 处理速度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |
| 准确性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 成本效益 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |
| 可控性 | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 扩展性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |

## 6.2 interrupt 与 Command：让机器人"举手"

### interrupt：暂停执行等待人工

`interrupt` 是 LangGraph 提供的核心机制，让 AI 能够在需要时"举手"请求人工帮助：

```python
from langgraph.types import interrupt
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict
from typing import Annotated
from langchain_core.messages import HumanMessage, AIMessage

class ReviewState(TypedDict):
    messages: Annotated[list, add_messages]
    pending_action: str
    confidence_score: float
    requires_approval: bool
    human_feedback: str

def create_human_in_loop_chatbot():
    """创建人机协作聊天机器人"""
    
    def ai_analysis_node(state: ReviewState):
        """AI 分析节点"""
        import os
        last_message = state["messages"][-1].content
        
        # 智谱GLM-4.5配置
        try:
            from langchain_community.chat_models import ChatZhipuAI
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.1,
                api_key=os.getenv("ZHIPUAI_API_KEY")
            )
            
            analysis_prompt = f"""
            分析以下用户请求，评估其风险等级和你的置信度：
            用户请求：{last_message}
            
            请返回：
            1. 建议的操作
            2. 风险等级（低/中/高）
            3. 置信度（0-1之间的数字）
            
            格式：操作|风险等级|置信度
            """
            
            response = llm.invoke([HumanMessage(content=analysis_prompt)])
            analysis = response.content.strip()
            
        except (ImportError, Exception) as e:
            print(f"⚠️ API调用失败，使用模拟分析: {e}")
            # 基于关键词的模拟分析
            if "删除" in last_message:
                analysis = "删除文件操作|高|0.9"
            elif "发送" in last_message:
                analysis = "发送通知|高|0.8"
            elif "复杂" in last_message or "不确定" in last_message:
                analysis = "复杂咨询|中|0.4"
            elif "天气" in last_message:
                analysis = "天气查询|低|0.95"
            else:
                analysis = "一般回复|中|0.7"
        
        try:
            parts = analysis.split('|')
            action = parts[0].strip()
            risk_level = parts[1].strip()
            confidence = float(parts[2].strip())
        except:
            # 解析失败时的默认值
            action = "general_response"
            risk_level = "中"
            confidence = 0.5
        
        # 判断是否需要人工审核
        needs_approval = (
            confidence < 0.7 or  # 置信度低
            risk_level == "高" or  # 高风险
            "删除" in last_message or 
            "发送" in last_message  # 敏感操作
        )
        
        return {
            "pending_action": action,
            "confidence_score": confidence,
            "requires_approval": needs_approval
        }
    
    def human_review_node(state: ReviewState):
        """人工审核节点"""
        if state.get("requires_approval", False):
            # 构造审核信息
            review_info = {
                "user_request": state["messages"][-1].content,
                "ai_suggestion": state.get("pending_action", "未知"),
                "confidence": state.get("confidence_score", 0),
                "reason": "需要人工确认"
            }
            
            # 使用 interrupt 暂停执行，等待人工输入
            return interrupt(review_info)
        
        # 不需要审核，直接通过
        return {"human_feedback": "auto_approved"}
    
    def execution_node(state: ReviewState):
        """执行节点"""
        import os
        human_feedback = state.get("human_feedback", "")
        
        if human_feedback == "rejected":
            return {
                "messages": [AIMessage(content="抱歉，该操作已被拒绝。")]
            }
        elif human_feedback == "auto_approved" or human_feedback == "approved":
            # 执行原始请求
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(
                    model="glm-4.5",
                    temperature=0.7,
                    api_key=os.getenv("ZHIPUAI_API_KEY")
                )
                response = llm.invoke(state["messages"])
                return {"messages": [response]}
            except (ImportError, Exception) as e:
                print(f"⚠️ API调用失败，使用模拟回复: {e}")
                # 模拟响应
                user_content = state["messages"][-1].content
                if "天气" in user_content:
                    mock_response = AIMessage(content="今天天气晴朗，温度适宜，适合外出活动。")
                elif "删除" in user_content:
                    mock_response = AIMessage(content="我不能执行删除文件的操作，这可能会造成数据丢失。")
                elif "发送" in user_content:
                    mock_response = AIMessage(content="邮件发送功能需要相应权限，请联系管理员。")
                else:
                    mock_response = AIMessage(content="我已经处理了您的请求。")
                return {"messages": [mock_response]}
        else:
            # 根据人工反馈调整回复
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(
                    model="glm-4.5",
                    temperature=0.7,
                    api_key=os.getenv("ZHIPUAI_API_KEY")
                )
                adjusted_prompt = f"""
                用户原始请求：{state['messages'][-1].content}
                人工反馈：{human_feedback}
                请根据人工反馈调整你的回复。
                """
                response = llm.invoke([HumanMessage(content=adjusted_prompt)])
                return {"messages": [response]}
            except (ImportError, Exception) as e:
                print(f"⚠️ API调用失败，使用模拟调整回复: {e}")
                mock_response = AIMessage(content=f"根据您的反馈「{human_feedback}」，我已经调整了处理方式。")
                return {"messages": [mock_response]}
    
    # 构建图
    graph = StateGraph(ReviewState)
    
    # 添加节点
    graph.add_node("ai_analysis", ai_analysis_node)
    graph.add_node("human_review", human_review_node)
    graph.add_node("execution", execution_node)
    
    # 设置流程
    graph.add_edge(START, "ai_analysis")
    graph.add_edge("ai_analysis", "human_review")
    graph.add_edge("human_review", "execution")
    graph.add_edge("execution", END)
    
    return graph.compile()

def demo_human_in_loop():
    """演示人机协作"""
    import os
    
    print("🚀 启动人机协作聊天机器人")
    print("🔧 使用智谱GLM-4.5模型")
    print("=" * 60)
    
    # 检查环境配置
    if not os.getenv("ZHIPUAI_API_KEY"):
        print("⚠️ 未设置 ZHIPUAI_API_KEY 环境变量")
        print("💡 将使用模拟模式进行演示")
        print("🔧 如需使用真实API，请设置：export ZHIPUAI_API_KEY=your_key")
        print("-" * 60)
    
    try:
        app = create_human_in_loop_chatbot()
        print("✅ 成功创建人机协作应用\n")
    except Exception as e:
        print(f"❌ 创建应用失败: {e}")
        return
    
    print("🤖 人机协作聊天机器人")
    print("📊 AI会自动评估风险，必要时请求人工确认")
    print("⚠️ 高风险或低置信度的操作需要人工审核\n")
    
    # 测试不同类型的请求
    test_requests = [
        "你好，今天天气怎么样？",  # 低风险
        "帮我删除所有文件",       # 高风险
        "这个复杂的技术问题我不太确定",  # 低置信度
        "发送邮件给所有客户"       # 敏感操作
    ]
    
    for request in test_requests:
        print(f"📝 测试请求: {request}")
        
        try:
            # 初始调用
            result = app.invoke({
                "messages": [HumanMessage(content=request)],
                "pending_action": "",
                "confidence_score": 0.0,
                "requires_approval": False,
                "human_feedback": ""
            })
            print(f"✅ 直接回复: {result['messages'][-1].content}")
            
        except Exception as e:
            # 如果遇到 interrupt，说明需要人工干预
            if "interrupt" in str(e).lower():
                print("⏸️ AI请求人工审核")
                print("📋 审核信息:", str(e))
                
                # 模拟人工决策
                human_decision = input("👤 人工决策 (approved/rejected/修改建议): ").strip()
                
                # 继续执行
                try:
                    final_result = app.invoke(
                        result,  # 使用之前的状态
                        {"human_feedback": human_decision}
                    )
                    print(f"✅ 最终回复: {final_result['messages'][-1].content}")
                except Exception as e2:
                    print(f"❌ 执行失败: {e2}")
            else:
                print(f"❌ 其他错误: {e}")
        
        print("-" * 60)

if __name__ == "__main__":
    demo_human_in_loop()
```

### Command：更灵活的控制流

`Command` 提供了更灵活的方式来结合状态更新和流程控制：

```python
from langgraph.types import Command
from typing import Literal
from datetime import datetime

def smart_routing_node(state: ReviewState) -> Command[Literal["human_review", "auto_execute"]]:
    """智能路由节点，使用 Command 进行动态控制"""
    last_message = state["messages"][-1].content.lower()
    
    # 分析风险因素
    risk_keywords = ["删除", "发送", "支付", "转账", "发布"]
    has_risk = any(keyword in last_message for keyword in risk_keywords)
    
    # 分析复杂度
    complexity_indicators = ["复杂", "不确定", "可能", "也许", "不知道"]
    is_complex = any(indicator in last_message for indicator in complexity_indicators)
    
    if has_risk or is_complex:
        # 需要人工审核
        return Command(
            update={
                "requires_approval": True,
                "pending_action": "需要人工确认"
            },
            goto="human_review"
        )
    else:
        # 可以自动执行
        return Command(
            update={
                "requires_approval": False,
                "pending_action": "自动处理"
            },
            goto="auto_execute"
        )

def create_command_based_chatbot():
    """创建基于 Command 的人机协作机器人"""
    
    def auto_execute_node(state: ReviewState):
        """自动执行节点"""
        import os
        
        try:
            from langchain_community.chat_models import ChatZhipuAI
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.7,
                api_key=os.getenv("ZHIPUAI_API_KEY")
            )
            response = llm.invoke(state["messages"])
            return {
                "messages": [response],
                "human_feedback": "auto_processed"
            }
        except (ImportError, Exception) as e:
            print(f"⚠️ API调用失败，使用模拟回复: {e}")
            # 模拟自动执行响应
            user_content = state["messages"][-1].content
            if "天气" in user_content:
                mock_response = AIMessage(content="今天天气不错，适合外出。")
            else:
                mock_response = AIMessage(content="我已经自动处理了您的请求。")
            
            return {
                "messages": [mock_response],
                "human_feedback": "auto_processed"
            }
    
    def human_review_with_command(state: ReviewState):
        """带 Command 的人工审核节点"""
        review_info = {
            "request": state["messages"][-1].content,
            "action": state.get("pending_action", ""),
            "timestamp": datetime.now().isoformat()
        }
        
        # 使用 interrupt 请求人工输入
        return interrupt(review_info)
    
    # 构建图
    graph = StateGraph(ReviewState)
    
    # 添加节点
    graph.add_node("smart_routing", smart_routing_node)
    graph.add_node("human_review", human_review_with_command)
    graph.add_node("auto_execute", auto_execute_node)
    
    # 设置流程
    graph.add_edge(START, "smart_routing")
    # smart_routing 节点会通过 Command 动态路由
    graph.add_edge("human_review", END)
    graph.add_edge("auto_execute", END)
    
    return graph.compile()
```

## 6.3 恢复执行：人在回路中的工作流

### 处理人工反馈

当 AI 通过 `interrupt` 请求人工干预后，系统需要能够处理各种类型的人工反馈：

```python
class WorkflowState(TypedDict):
    user_text: str

def create_resumable_workflow():
    """创建可恢复的工作流（基于官方示例）"""
    
    def human_node(state: WorkflowState):
        """人工干预节点 - 基于官方interrupt模式"""
        import os
        
        # 使用interrupt请求人工修改
        revised_text = interrupt({
            "text_to_revise": state["user_text"],
            "instruction": "请修改以下文本"
        })
        
        # 可选：使用GLM-4.5处理修改后的文本
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if api_key:
            try:
                from langchain_community.chat_models import ChatZhipuAI
                llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key)
                
                prompt = f"请优化以下文本：{revised_text}"
                response = llm.invoke([HumanMessage(content=prompt)])
                final_text = response.content
            except Exception as e:
                print(f"⚠️ GLM-4.5调用失败: {e}")
                final_text = revised_text
        else:
            final_text = revised_text
        
        return {"user_text": final_text}
    
    # 构建图 - 基于官方示例
    from langgraph.checkpoint.memory import MemorySaver
    
    graph = StateGraph(WorkflowState)
    graph.add_node("human_node", human_node)
    graph.add_edge(START, "human_node")
    
    # checkpointer是interrupt机制的必需组件
    checkpointer = MemorySaver()
    return graph.compile(checkpointer=checkpointer)

def demo_resumable_workflow():
    """演示人工干预工作流（基于官方示例）"""
    import uuid
    from langgraph.types import Command
    
    print("🔄 真正的交互式 Human-in-the-Loop 演示")
    print("📝 演示文本修改和人工干预流程")
    print("⏸️ 程序将在 interrupt 处暂停，等待您的输入\n")
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置")
    else:
        print("⚠️ 未配置智谱API")
    
    # 创建应用
    app = create_resumable_workflow()
    print("✅ 工作流已创建")
    
    # 配置线程ID（interrupt机制必需）
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    # 初始文本
    original_text = "今天是个好天气，适合出去走走。"
    print(f"📝 原始文本: {original_text}")
    
    # 第一步：运行工作流直到interrupt
    print("\n🚀 启动工作流...")
    print("⏸️ 工作流将在 interrupt 处暂停，等待您的输入")
    result = app.invoke({"user_text": original_text}, config=config)
    
    # 检查是否触发了interrupt
    if "__interrupt__" in result:
        print("\n🔔 工作流已暂停！触发了 interrupt")
        interrupt_info = result["__interrupt__"][0]
        
        print("📋 Interrupt 信息:")
        for key, value in interrupt_info.value.items():
            print(f"  • {key}: {value}")
        
        print("\n" + "="*50)
        print("现在需要您亲自输入修改后的文本！")
        print("="*50)
        
        # 等待用户真实输入
        user_input = input("\n✏️ 请输入修改后的文本: ").strip()
        
        if not user_input:
            print("❌ 输入为空，使用原文本")
            user_input = original_text
        
        print(f"📝 您输入的文本: {user_input}")
        
        # 使用用户的真实输入
        human_revision = user_input
        
        # 第二步：使用Command恢复执行
        print("\n🔄 使用您的输入恢复工作流...")
        final_result = app.invoke(Command(resume=human_revision), config=config)
        
        print(f"\n✅ 最终处理结果:")
        print(f"📄 {final_result['user_text']}")
    else:
        print("❌ 没有触发interrupt，这不应该发生！")
    
    print("\n🎉 演示完成！这就是真正的 Human-in-the-Loop！")

if __name__ == "__main__":
    demo_resumable_workflow()
```

---

**本章小结**

在这一章中，我们深入探讨了 LangGraph 的人机协作机制：

- **人工干预的必要性**：理解何时以及为什么需要人工介入
- **interrupt 机制**：学会使用 interrupt 让 AI 在关键时刻"举手"
- **Command 控制**：掌握更灵活的状态更新和流程控制
- **恢复执行**：实现完整的人机协作工作流

通过人机协作，你的 AI 应用既能享受自动化的效率，又能保持人工监督的可靠性。这种平衡是构建可信赖 AI 系统的关键。

## 🚀 运行示例

### 基本运行

```bash
# 使用uv运行代码
uv run python your_script.py

# 或者在虚拟环境中运行
uv shell
python your_script.py
```

### 预期输出示例

```
🚀 启动人机协作聊天机器人
🔧 使用智谱GLM-4.5模型
============================================================
✅ 成功创建人机协作应用

🤖 人机协作聊天机器人
📊 AI会自动评估风险，必要时请求人工确认
⚠️ 高风险或低置信度的操作需要人工审核

📝 测试请求: 你好，今天天气怎么样？
✅ 直接回复: 今天天气晴朗，温度适宜，适合外出活动。

📝 测试请求: 帮我删除所有文件
⏸️ AI请求人工审核
📋 审核信息: {'user_request': '帮我删除所有文件', 'ai_suggestion': '删除文件操作', 'confidence': 0.9, 'reason': '需要人工确认'}
👤 人工决策 (approved/rejected/修改建议): rejected
✅ 最终回复: 抱歉，该操作已被拒绝。
```

## ⚠️ 常见问题和解决方案

### 1. 导入错误

**问题**: `ModuleNotFoundError: No module named 'langchain_community'`

**解决方案**:
```bash
uv add langchain-community
```

### 2. API密钥问题

**问题**: API调用失败或返回错误

**解决方案**:
- 检查API密钥是否正确设置
- 确认账户有足够的额度
- 代码会自动降级到模拟模式

### 3. interrupt机制问题

**问题**: interrupt没有按预期暂停执行

**说明**: 这是LangGraph版本相关的已知问题，不影响核心逻辑学习。在生产环境中可能需要根据具体版本调整。

### 4. 测试建议

```python
# 建议的测试流程
def test_human_loop():
    # 1. 测试低风险请求（应该自动通过）
    # 2. 测试高风险请求（应该需要审核）
    # 3. 测试低置信度请求（应该需要审核）
    # 4. 测试不同的人工反馈类型
    pass
```

## 💡 扩展建议

1. **多级审核**: 可以根据风险等级设置不同的审核权限
2. **审核历史**: 记录所有人工审核决策，用于模型改进
3. **批量处理**: 支持批量审核多个请求
4. **实时通知**: 集成钉钉、企微等通知系统
5. **权限管理**: 不同用户有不同的审核权限

**下一步预告：**
在第7章中，我们将学习如何让对话更聪明，包括系统提示词设计、结构化输出、以及 Token 优化技巧，让你的 AI 助手变得更加智能和高效。准备好提升对话质量了吗？