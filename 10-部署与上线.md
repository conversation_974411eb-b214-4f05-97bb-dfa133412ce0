# 第10章 部署与上线

开发完成的 AI 应用就像是一件精美的艺术品，但只有让更多人能够使用它，才能真正发挥价值。从本地开发环境到生产环境，这中间有很多技术挑战需要解决：如何保证稳定性？如何处理高并发？如何监控运行状态？

在这一章中，我们将学习如何将 LangGraph 应用部署到生产环境。

## 10.1 本地 Docker 部署

### 为什么选择 Docker？

Docker 就像是给你的应用打包了一个"便携式房间"，无论在哪里都能保持一致的运行环境：

- **环境一致性**：开发、测试、生产环境完全相同
- **依赖隔离**：不会与其他应用产生冲突
- **快速部署**：一键启动整个应用栈
- **易于扩展**：可以轻松创建多个实例

### 创建 Dockerfile

```dockerfile
# 使用官方 Python 基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非 root 用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 应用容器化

```python
# main.py - FastAPI 应用入口
import os
import time
from datetime import datetime
from typing import List, Optional

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from langgraph_app import create_production_chatbot

app = FastAPI(
    title="LangGraph 聊天机器人 API",
    description="基于 LangGraph 的生产级聊天机器人",
    version="1.0.0"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求/响应模型
class ChatRequest(BaseModel):
    message: str
    user_id: Optional[str] = "anonymous"
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    timestamp: str
    processing_time: float

# 全局变量
chatbot_app = None

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global chatbot_app
    print("🚀 正在启动 LangGraph 聊天机器人...")
    
    try:
        chatbot_app = create_production_chatbot()
        print("✅ LangGraph 应用初始化成功")
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        raise

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "chatbot_ready": chatbot_app is not None
    }

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """聊天端点"""
    if not chatbot_app:
        raise HTTPException(status_code=503, detail="聊天机器人未初始化")
    
    start_time = datetime.now()
    
    try:
        # 生成会话 ID
        session_id = request.session_id or f"{request.user_id}_{int(start_time.timestamp())}"
        
        # 调用 LangGraph 应用
        from langchain_core.messages import HumanMessage
        result = chatbot_app.invoke(
            {"messages": [HumanMessage(content=request.message)]},
            config={"configurable": {"thread_id": session_id}}
        )
        
        # 计算处理时间
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 提取 AI 回复
        ai_response = result["messages"][-1].content
        
        return ChatResponse(
            response=ai_response,
            session_id=session_id,
            timestamp=start_time.isoformat(),
            processing_time=processing_time
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理请求时出错: {str(e)}")

@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """获取会话历史"""
    if not chatbot_app:
        raise HTTPException(status_code=503, detail="聊天机器人未初始化")
    
    try:
        config = {"configurable": {"thread_id": session_id}}
        history = list(chatbot_app.get_state_history(config))
        
        return {
            "session_id": session_id,
            "message_count": len(history),
            "history": [
                {
                    "step": i,
                    "timestamp": checkpoint.config.get("timestamp", "unknown"),
                    "message_count": len(checkpoint.values.get("messages", []))
                }
                for i, checkpoint in enumerate(history)
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史失败: {str(e)}")

@app.get("/metrics")
async def get_metrics():
    """获取应用指标"""
    return {
        "timestamp": datetime.now().isoformat(),
        "uptime_seconds": time.time() - startup_time,
        "chatbot_status": "ready" if chatbot_app else "not_ready",
        "memory_usage": "实际部署时可添加具体指标"
    }

# 记录启动时间
startup_time = time.time()

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=False,  # 生产环境中关闭热重载
        workers=1  # 单进程，因为 LangGraph 应用有状态
    )
```

### Docker Compose 配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # LangGraph 应用
  langgraph-app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ZHIPUAI_API_KEY=${ZHIPUAI_API_KEY}
      - DATABASE_URL=********************************************/langgraph_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL 数据库
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=langgraph_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - langgraph-app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 生产级 LangGraph 应用

```python
# langgraph_app.py
import os
import time
from typing import Annotated
from typing_extensions import TypedDict

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

class ProductionState(TypedDict):
    messages: Annotated[list, add_messages]
    user_id: str
    session_metadata: dict

def create_production_chatbot():
    """创建生产级聊天机器人"""
    
    # 配置数据库连接
    database_url = os.getenv("DATABASE_URL", "sqlite:///./data/chatbot.db")
    
    if database_url.startswith("postgresql"):
        # 使用 PostgreSQL
        from langgraph.checkpoint.postgres import PostgresSaver
        checkpointer = PostgresSaver.from_conn_string(database_url)
    else:
        # 回退到 SQLite
        from langgraph.checkpoint.sqlite import SqliteSaver
        checkpointer = SqliteSaver.from_conn_string(database_url)
    
    def production_chat_node(state: ProductionState):
        """生产级聊天节点"""
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if not api_key:
            return {
                "messages": [AIMessage(content="⚠️ 系统配置错误，请联系管理员")]
            }
        
        try:
            from langchain_community.chat_models import ChatZhipuAI
            
            # 配置 LLM
            llm = ChatZhipuAI(
                model=os.getenv("ZHIPU_MODEL", "glm-4.5"),
                temperature=float(os.getenv("ZHIPU_TEMPERATURE", "0.7")),
                api_key=api_key,
                timeout=35,  # 35秒超时
                max_retries=2  # 最多重试2次
            )
            
            # 系统提示词
            system_prompt = os.getenv("SYSTEM_PROMPT", """
你是一个友好、专业的AI助手。请遵循以下原则：
1. 提供准确、有用的信息
2. 保持礼貌和专业的语调
3. 如果不确定，请诚实说明
4. 保护用户隐私，不记录敏感信息
""")
            
            messages = [SystemMessage(content=system_prompt)] + state["messages"]
            response = llm.invoke(messages)
            
            return {"messages": [response]}
            
        except Exception as e:
            # 错误处理
            error_message = AIMessage(
                content=f"抱歉，我遇到了一些技术问题。请稍后重试。\n错误信息：{str(e)[:100]}..."
            )
            return {"messages": [error_message]}
    
    def call_with_retry(state: ProductionState):
        """带重试机制的调用"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                return production_chat_node(state)
            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(1)  # 等待1秒后重试
                    continue
                else:
                    # 最后一次尝试失败，返回错误信息
                    error_message = AIMessage(
                        content="抱歉，系统暂时不可用，请稍后重试。"
                    )
                    return {"messages": [error_message]}
    
    # 构建图
    graph = StateGraph(ProductionState)
    graph.add_node("chat", call_with_retry)
    graph.add_edge(START, "chat")
    graph.add_edge("chat", END)
    
    # 编译时添加检查点
    return graph.compile(checkpointer=checkpointer)
```

### 部署脚本

```bash
#!/bin/bash
# deploy.sh - 部署脚本

set -e  # 遇到错误立即退出

echo "🚀 开始部署 LangGraph 应用..."

# 检查环境变量
if [ -z "$ZHIPUAI_API_KEY" ]; then
    echo "❌ 错误: ZHIPUAI_API_KEY 环境变量未设置"
    exit 1
fi

# 创建必要的目录
mkdir -p data logs ssl

# 构建 Docker 镜像
echo "🔨 构建 Docker 镜像..."
docker-compose build

# 启动数据库并等待就绪
echo "🗄️ 启动数据库..."
docker-compose up -d postgres redis

# 等待数据库启动
echo "⏳ 等待数据库就绪..."
sleep 10

# 运行数据库迁移（如果需要）
echo "📋 运行数据库迁移..."
# docker-compose exec postgres psql -U postgres -d langgraph_db -f /docker-entrypoint-initdb.d/init.sql

# 启动应用
echo "🎯 启动应用..."
docker-compose up -d

# 检查服务状态
echo "📊 检查服务状态..."
sleep 5
docker-compose ps

# 健康检查
echo "🔍 执行健康检查..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ 应用启动成功！"
        echo "🌐 访问地址: http://localhost:8000"
        echo "📚 API 文档: http://localhost:8000/docs"
        echo "📊 健康检查: http://localhost:8000/health"
        exit 0
    fi
    echo "⏳ 等待应用启动... ($i/10)"
    sleep 3
done

echo "❌ 应用启动失败，请检查日志:"
docker-compose logs langgraph-app
exit 1
```

## 10.2 LangGraph Platform (SaaS) 部署

### LangGraph Cloud 简介

LangGraph Cloud 是官方提供的托管服务，让你可以专注于应用逻辑而不用担心基础设施：

- **零运维**：无需管理服务器和数据库
- **自动扩展**：根据负载自动调整资源
- **内置监控**：提供详细的性能指标
- **安全保障**：企业级安全和合规性

### 准备部署文件

```python
# langgraph.json - LangGraph Cloud 配置文件
{
    "dependencies": [
        "langchain",
        "langchain-community", 
        "langgraph",
        "fastapi",
        "uvicorn"
    ],
    "graphs": {
        "chatbot": "./chatbot_graph.py:graph"
    },
    "env": [
        "ZHIPUAI_API_KEY"
    ]
}
```

```python
# chatbot_graph.py - 云端图定义
import os
from typing import Annotated
from typing_extensions import TypedDict

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages

class CloudState(TypedDict):
    messages: Annotated[list, add_messages]

def cloud_chat_node(state: CloudState):
    """云端聊天节点"""
    api_key = os.getenv("ZHIPUAI_API_KEY")
    
    if not api_key:
        return {
            "messages": [AIMessage(content="⚠️ API密钥未配置")]
        }
    
    try:
        from langchain_community.chat_models import ChatZhipuAI
        
        llm = ChatZhipuAI(
            model="glm-4.5",
            temperature=0.7,
            api_key=api_key,
            timeout=35
        )
        
        system_message = SystemMessage(content="""
你是一个专业的AI助手，运行在 LangGraph Cloud 上。
请提供准确、有用的回答。
""")
        
        messages = [system_message] + state["messages"]
        response = llm.invoke(messages)
        
        return {"messages": [response]}
        
    except Exception as e:
        error_message = AIMessage(content=f"处理请求时出错: {str(e)[:100]}...")
        return {"messages": [error_message]}

# 构建图
builder = StateGraph(CloudState)
builder.add_node("chat", cloud_chat_node)
builder.add_edge(START, "chat")
builder.add_edge("chat", END)

# 导出图
graph = builder.compile()
```

### 使用 LangGraph CLI 部署

```bash
# 安装 LangGraph CLI
pip install langgraph-cli

# 登录 LangGraph Cloud
langgraph auth login

# 创建新项目
langgraph new my-chatbot-project
cd my-chatbot-project

# 部署到云端
langgraph deploy

# 查看部署状态
langgraph status

# 查看日志
langgraph logs

# 测试部署的应用
langgraph test --input '{"messages": [{"role": "user", "content": "Hello!"}]}'
```

### 云端配置管理

```python
# config.py - 云端配置
import os
from typing import Dict, Any

class CloudConfig:
    """云端配置管理"""
    
    def __init__(self):
        self.zhipuai_api_key = os.getenv("ZHIPUAI_API_KEY")
        self.model_name = os.getenv("MODEL_NAME", "glm-4.5")
        self.temperature = float(os.getenv("TEMPERATURE", "0.7"))
        self.timeout = int(os.getenv("TIMEOUT", "35"))
        
        # 验证必需的配置
        if not self.zhipuai_api_key:
            raise ValueError("ZHIPUAI_API_KEY 环境变量未设置")
    
    def get_llm_config(self) -> Dict[str, Any]:
        """获取 LLM 配置"""
        return {
            "model": self.model_name,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "api_key": self.zhipuai_api_key
        }
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return os.getenv("SYSTEM_PROMPT", """
你是一个运行在 LangGraph Cloud 上的AI助手。
请提供专业、准确的回答。
""")

# 全局配置实例
config = CloudConfig()
```

## 10.3 混合部署：LangCloud 控制面 + 自托管数据面

### 混合架构的优势

混合部署结合了云端和本地部署的优势：

- **数据安全**：敏感数据保留在本地
- **管理便利**：使用云端控制面进行管理
- **成本优化**：根据需求灵活配置资源
- **合规要求**：满足数据本地化要求

### 架构设计

```
┌─────────────────┐    ┌─────────────────┐
│   LangGraph     │    │   自托管数据面   │
│   Cloud 控制面  │◄──►│                 │
│                 │    │  ┌─────────────┐ │
│ • 图管理        │    │  │ LangGraph   │ │
│ • 监控面板      │    │  │ Runtime     │ │
│ • 配置管理      │    │  └─────────────┘ │
│ • 日志聚合      │    │  ┌─────────────┐ │
└─────────────────┘    │  │ 本地数据库   │ │
                       │  └─────────────┘ │
                       └─────────────────┘
```

### 本地数据面配置

```python
# hybrid_deployment.py
import os
import time
import requests
from typing import Dict, Any
from langgraph.graph import StateGraph

class HybridDataPlane:
    """混合部署数据面"""
    
    def __init__(self):
        self.local_db_url = os.getenv("LOCAL_DATABASE_URL")
        self.cloud_control_url = os.getenv("LANGGRAPH_CLOUD_URL")
        self.api_key = os.getenv("LANGGRAPH_API_KEY")
        
        if not all([self.local_db_url, self.cloud_control_url, self.api_key]):
            raise ValueError("混合部署配置不完整")
    
    def setup_local_storage(self):
        """设置本地存储"""
        if self.local_db_url.startswith("postgresql"):
            from langgraph.checkpoint.postgres import PostgresSaver
            return PostgresSaver.from_conn_string(self.local_db_url)
        else:
            from langgraph.checkpoint.sqlite import SqliteSaver
            return SqliteSaver.from_conn_string(self.local_db_url)
    
    def register_with_control_plane(self):
        """向控制面注册"""
        registration_data = {
            "data_plane_id": os.getenv("DATA_PLANE_ID", "local-dp-1"),
            "endpoint": os.getenv("LOCAL_ENDPOINT", "http://localhost:8000"),
            "capabilities": ["graph_execution", "state_persistence"],
            "status": "healthy"
        }
        
        try:
            response = requests.post(
                f"{self.cloud_control_url}/api/v1/data-planes/register",
                json=registration_data,
                headers={"Authorization": f"Bearer {self.api_key}"},
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ 成功注册到控制面")
            else:
                print(f"❌ 注册失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 注册失败: {e}")
    
    def sync_configurations(self):
        """同步配置"""
        try:
            response = requests.get(
                f"{self.cloud_control_url}/api/v1/configurations",
                headers={"Authorization": f"Bearer {self.api_key}"},
                timeout=10
            )
            
            if response.status_code == 200:
                configs = response.json()
                # 应用配置到本地环境
                for key, value in configs.items():
                    os.environ[key] = str(value)
                print("✅ 配置同步完成")
            else:
                print(f"❌ 配置同步失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 配置同步失败: {e}")
    
    def heartbeat(self):
        """发送心跳"""
        heartbeat_data = {
            "data_plane_id": os.getenv("DATA_PLANE_ID", "local-dp-1"),
            "status": "healthy",
            "timestamp": time.time(),
            "metrics": {
                "cpu_usage": "模拟数据",
                "memory_usage": "模拟数据"
            }
        }
        
        try:
            response = requests.post(
                f"{self.cloud_control_url}/api/v1/data-planes/heartbeat",
                json=heartbeat_data,
                headers={"Authorization": f"Bearer {self.api_key}"},
                timeout=5
            )
            
            if response.status_code == 200:
                print("💓 心跳发送成功")
            else:
                print(f"❌ 心跳发送失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 心跳发送失败: {e}")

def create_hybrid_application():
    """创建混合部署应用"""
    # 初始化混合数据面
    data_plane = HybridDataPlane()
    
    # 设置本地存储
    checkpointer = data_plane.setup_local_storage()
    
    # 注册到控制面
    data_plane.register_with_control_plane()
    
    # 同步配置
    data_plane.sync_configurations()
    
    def hybrid_chat_node(state):
        """混合部署聊天节点"""
        api_key = os.getenv("ZHIPUAI_API_KEY")
        
        if not api_key:
            return {
                "messages": [AIMessage(content="⚠️ API密钥未配置")]
            }
        
        try:
            from langchain_community.chat_models import ChatZhipuAI
            
            llm = ChatZhipuAI(
                model=os.getenv("MODEL_NAME", "glm-4.5"),
                temperature=float(os.getenv("TEMPERATURE", "0.7")),
                api_key=api_key,
                timeout=35
            )
            
            response = llm.invoke(state["messages"])
            return {"messages": [response]}
            
        except Exception as e:
            error_message = AIMessage(content=f"处理失败: {str(e)[:100]}...")
            return {"messages": [error_message]}
    
    # 构建图
    from typing_extensions import TypedDict
    from typing import Annotated
    from langgraph.graph.message import add_messages
    
    class HybridState(TypedDict):
        messages: Annotated[list, add_messages]
    
    graph = StateGraph(HybridState)
    graph.add_node("chat", hybrid_chat_node)
    graph.add_edge(START, "chat")
    graph.add_edge("chat", END)
    
    return graph.compile(checkpointer=checkpointer)
```

## 10.4 完全自建：Kubernetes + Postgres 方案

### Kubernetes 部署配置

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: langgraph-app
  labels:
    app: langgraph-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: langgraph-app
  template:
    metadata:
      labels:
        app: langgraph-app
    spec:
      containers:
      - name: langgraph-app
        image: your-registry/langgraph-app:latest
        ports:
        - containerPort: 8000
        env:
        - name: ZHIPUAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: langgraph-secrets
              key: zhipuai-api-key
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: langgraph-secrets
              key: database-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: langgraph-service
spec:
  selector:
    app: langgraph-app
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer

---
apiVersion: v1
kind: Secret
metadata:
  name: langgraph-secrets
type: Opaque
data:
  zhipuai-api-key: <base64-encoded-api-key>
  database-url: <base64-encoded-database-url>
```

### PostgreSQL 集群配置

```yaml
# postgres-cluster.yaml
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: postgres-cluster
spec:
  instances: 3
  
  postgresql:
    parameters:
      max_connections: "200"
      shared_buffers: "256MB"
      effective_cache_size: "1GB"
  
  bootstrap:
    initdb:
      database: langgraph_db
      owner: langgraph_user
      secret:
        name: postgres-credentials
  
  storage:
    size: 100Gi
    storageClass: fast-ssd
  
  monitoring:
    enabled: true
  
  backup:
    retentionPolicy: "30d"
    barmanObjectStore:
      destinationPath: "s3://your-backup-bucket/postgres"
      s3Credentials:
        accessKeyId:
          name: backup-credentials
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: backup-credentials
          key: SECRET_ACCESS_KEY

---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-credentials
type: kubernetes.io/basic-auth
data:
  username: <base64-encoded-username>
  password: <base64-encoded-password>
```

### 自动扩展配置

```yaml
# hpa.yaml - 水平自动扩展
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: langgraph-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: langgraph-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
```

## 10.5 监控与日志：深入集成 LangSmith

### LangSmith 集成

```python
# monitoring.py
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# 模拟 LangSmith 客户端（实际使用时需要安装 langsmith）
class MockLangSmithClient:
    """模拟 LangSmith 客户端"""
    
    def __init__(self, api_url: str, api_key: str):
        self.api_url = api_url
        self.api_key = api_key
        print(f"🔗 LangSmith 客户端初始化: {api_url}")
    
    def create_run(self, **kwargs):
        """创建运行记录"""
        print(f"📝 记录对话: {kwargs.get('name', 'unknown')}")
        return {"run_id": f"run_{datetime.now().timestamp()}"}
    
    def create_dataset(self, dataset_name: str, description: str):
        """创建数据集"""
        print(f"📊 创建数据集: {dataset_name}")
        return {"dataset_id": f"dataset_{datetime.now().timestamp()}"}
    
    def create_example(self, dataset_id: str, inputs: Dict, outputs: Dict):
        """创建示例"""
        print(f"➕ 添加示例到数据集: {dataset_id}")

class LangSmithMonitoring:
    """LangSmith 监控集成"""
    
    def __init__(self):
        # 在实际环境中，这里会导入真正的 LangSmith 客户端
        self.client = MockLangSmithClient(
            api_url=os.getenv("LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com"),
            api_key=os.getenv("LANGCHAIN_API_KEY", "mock_key")
        )
        self.project_name = os.getenv("LANGCHAIN_PROJECT", "langgraph-production")
    
    def setup_tracing(self):
        """设置链路追踪"""
        os.environ["LANGCHAIN_TRACING_V2"] = "true"
        os.environ["LANGCHAIN_PROJECT"] = self.project_name
        print(f"🔍 启用链路追踪: {self.project_name}")
    
    def log_conversation(self, 
                        session_id: str, 
                        user_message: str, 
                        ai_response: str, 
                        processing_time: float, 
                        metadata: Optional[Dict] = None):
        """记录对话"""
        run_data = {
            "name": "chat_interaction",
            "run_type": "chain",
            "inputs": {"user_message": user_message},
            "outputs": {"ai_response": ai_response},
            "session_id": session_id,
            "extra": {
                "processing_time": processing_time,
                "timestamp": datetime.now().isoformat(),
                **(metadata or {})
            }
        }
        
        try:
            result = self.client.create_run(**run_data)
            print(f"✅ 对话记录成功: {result}")
        except Exception as e:
            logging.error(f"❌ 记录对话到 LangSmith 失败: {e}")
    
    def log_error(self, session_id: str, error: str, context: Dict):
        """记录错误"""
        error_data = {
            "name": "error_event",
            "run_type": "tool",
            "inputs": context,
            "outputs": {"error": error},
            "session_id": session_id,
            "extra": {
                "timestamp": datetime.now().isoformat(),
                "severity": "error"
            }
        }
        
        try:
            self.client.create_run(**error_data)
            print(f"⚠️ 错误记录成功")
        except Exception as e:
            logging.error(f"❌ 记录错误失败: {e}")
    
    def create_evaluation_dataset(self, name: str, examples: list):
        """创建评估数据集"""
        try:
            dataset = self.client.create_dataset(
                dataset_name=name,
                description=f"评估数据集: {name}"
            )
            
            for example in examples:
                self.client.create_example(
                    dataset_id=dataset["dataset_id"],
                    inputs=example["inputs"],
                    outputs=example["outputs"]
                )
            
            print(f"📊 评估数据集创建完成: {name}")
            return dataset
            
        except Exception as e:
            logging.error(f"❌ 创建评估数据集失败: {e}")
            return None

# 集成到生产应用
def create_monitored_chatbot():
    """创建带监控的聊天机器人"""
    monitoring = LangSmithMonitoring()
    monitoring.setup_tracing()
    
    def monitored_chat_node(state):
        """带监控的聊天节点"""
        start_time = datetime.now()
        session_id = state.get("session_id", "unknown")
        user_message = state["messages"][-1].content
        
        try:
            # 调用智谱AI
            api_key = os.getenv("ZHIPUAI_API_KEY")
            if not api_key:
                raise ValueError("API密钥未配置")
            
            from langchain_community.chat_models import ChatZhipuAI
            llm = ChatZhipuAI(
                model="glm-4.5",
                temperature=0.7,
                api_key=api_key,
                timeout=35
            )
            
            response = llm.invoke(state["messages"])
            
            # 记录成功的交互
            processing_time = (datetime.now() - start_time).total_seconds()
            monitoring.log_conversation(
                session_id=session_id,
                user_message=user_message,
                ai_response=response.content,
                processing_time=processing_time,
                metadata={"status": "success", "model": "glm-4.5"}
            )
            
            return {"messages": [response]}
            
        except Exception as e:
            # 记录错误
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = f"处理失败: {str(e)[:100]}..."
            
            monitoring.log_error(
                session_id=session_id,
                error=str(e),
                context={
                    "user_message": user_message,
                    "processing_time": processing_time
                }
            )
            
            # 返回错误响应
            from langchain_core.messages import AIMessage
            return {"messages": [AIMessage(content=error_msg)]}
    
    # 构建图
    from langgraph.graph import StateGraph, START, END
    from typing_extensions import TypedDict
    from typing import Annotated
    from langgraph.graph.message import add_messages
    
    class MonitoredState(TypedDict):
        messages: Annotated[list, add_messages]
        session_id: str
    
    graph = StateGraph(MonitoredState)
    graph.add_node("chat", monitored_chat_node)
    graph.add_edge(START, "chat")
    graph.add_edge("chat", END)
    
    return graph.compile()
```

### 自定义监控指标

```python
# metrics.py
import time
import functools
from collections import defaultdict
from datetime import datetime

# 简化的指标收集器（生产环境可使用 Prometheus）
class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self.request_count = defaultdict(int)
        self.request_duration = []
        self.active_sessions = set()
        self.llm_token_usage = defaultdict(int)
        self.error_count = defaultdict(int)
    
    def track_request(self, method: str, endpoint: str):
        """请求追踪装饰器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                status = "success"
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    status = "error"
                    self.error_count[f"{method}_{endpoint}"] += 1
                    raise
                finally:
                    duration = time.time() - start_time
                    self.request_count[f"{method}_{endpoint}_{status}"] += 1
                    self.request_duration.append(duration)
                    
                    # 保持最近1000个请求的记录
                    if len(self.request_duration) > 1000:
                        self.request_duration = self.request_duration[-1000:]
                        
            return wrapper
        return decorator
    
    def track_session(self, session_id: str):
        """会话追踪"""
        self.active_sessions.add(session_id)
    
    def end_session(self, session_id: str):
        """结束会话追踪"""
        self.active_sessions.discard(session_id)
    
    def track_llm_usage(self, model: str, tokens: int):
        """LLM 使用量追踪"""
        self.llm_token_usage[model] += tokens
    
    def get_metrics(self) -> dict:
        """获取当前指标"""
        avg_duration = sum(self.request_duration) / len(self.request_duration) if self.request_duration else 0
        
        return {
            "timestamp": datetime.now().isoformat(),
            "request_count": dict(self.request_count),
            "error_count": dict(self.error_count),
            "average_response_time": avg_duration,
            "active_sessions": len(self.active_sessions),
            "llm_token_usage": dict(self.llm_token_usage),
            "total_requests": sum(self.request_count.values())
        }
    
    def reset_metrics(self):
        """重置指标"""
        self.request_count.clear()
        self.request_duration.clear()
        self.llm_token_usage.clear()
        self.error_count.clear()

# 全局指标收集器
metrics = MetricsCollector()

def create_metrics_endpoint():
    """创建指标端点"""
    from fastapi import FastAPI
    
    metrics_app = FastAPI(title="LangGraph 监控指标")
    
    @metrics_app.get("/metrics")
    async def get_metrics():
        """获取应用指标"""
        return metrics.get_metrics()
    
    @metrics_app.post("/metrics/reset")
    async def reset_metrics():
        """重置指标"""
        metrics.reset_metrics()
        return {"status": "metrics reset"}
    
    return metrics_app
```

### 完整的生产部署脚本

```bash
#!/bin/bash
# production-deploy.sh

set -e
echo "🚀 开始生产环境部署..."

# 检查必需的环境变量
required_vars=("ZHIPUAI_API_KEY" "DATABASE_URL" "DOCKER_REGISTRY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ 错误: $var 环境变量未设置"
        exit 1
    fi
done

# 获取Git版本
GIT_COMMIT=$(git rev-parse --short HEAD)
IMAGE_TAG="${DOCKER_REGISTRY}/langgraph-app:${GIT_COMMIT}"

echo "📦 构建 Docker 镜像..."
docker build -t "$IMAGE_TAG" .
docker tag "$IMAGE_TAG" "${DOCKER_REGISTRY}/langgraph-app:latest"

echo "📤 推送镜像到仓库..."
docker push "$IMAGE_TAG"
docker push "${DOCKER_REGISTRY}/langgraph-app:latest"

# 如果是 Kubernetes 部署
if command -v kubectl &> /dev/null; then
    echo "🚢 部署到 Kubernetes..."
    
    # 更新部署配置中的镜像标签
    sed -i "s|your-registry/langgraph-app:latest|$IMAGE_TAG|g" k8s-deployment.yaml
    
    # 应用配置
    kubectl apply -f k8s/
    
    # 等待部署完成
    echo "⏳ 等待部署完成..."
    kubectl rollout status deployment/langgraph-app --timeout=300s
    
    # 获取服务地址
    SERVICE_IP=$(kubectl get service langgraph-service -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
    
    echo "✅ Kubernetes 部署成功！"
    echo "🌐 服务地址: http://$SERVICE_IP"
    
# 如果是 Docker Compose 部署
elif command -v docker-compose &> /dev/null; then
    echo "🐳 使用 Docker Compose 部署..."
    
    # 更新 docker-compose.yml 中的镜像
    sed -i "s|build: \.|image: $IMAGE_TAG|g" docker-compose.yml
    
    # 启动服务
    docker-compose up -d
    
    echo "✅ Docker Compose 部署成功！"
    echo "🌐 服务地址: http://localhost:8000"
fi

# 运行健康检查
echo "🔍 执行健康检查..."
sleep 10

if command -v kubectl &> /dev/null; then
    # Kubernetes 健康检查
    kubectl wait --for=condition=ready pod -l app=langgraph-app --timeout=300s
    
    # 通过端口转发测试
    kubectl port-forward service/langgraph-service 8080:80 &
    PORT_FORWARD_PID=$!
    sleep 5
    
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ 健康检查通过"
    else
        echo "❌ 健康检查失败"
        kubectl logs -l app=langgraph-app --tail=50
        kill $PORT_FORWARD_PID
        exit 1
    fi
    
    kill $PORT_FORWARD_PID
    
else
    # Docker Compose 健康检查
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ 健康检查通过"
    else
        echo "❌ 健康检查失败"
        docker-compose logs langgraph-app --tail=50
        exit 1
    fi
fi

echo "🎉 生产部署完成！"
echo "📊 监控指标: /metrics 端点"
echo "📚 API 文档: /docs 端点"
```

## 🔧 环境准备

在运行本章的示例代码之前，请确保已安装必要的依赖：

```bash
# 使用uv安装依赖
uv add fastapi uvicorn langchain-community langgraph langchain-core
uv add --dev docker-compose kubernetes
```

并设置相关环境变量：

```bash
export ZHIPUAI_API_KEY="your_zhipu_api_key_here"
export DATABASE_URL="postgresql://user:pass@localhost/db"
export DOCKER_REGISTRY="your-registry.com"
```

## 🚀 运行示例

```python
def demo_deployment_strategies():
    """演示部署策略"""
    print("🚀 LangGraph 部署策略演示")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("ZHIPUAI_API_KEY")
    if api_key:
        print(f"✅ 智谱GLM-4.5已配置")
    else:
        print("⚠️ 未配置智谱API密钥")
    
    print("\n📋 部署方案对比:")
    deployment_strategies = {
        "本地Docker": {
            "优势": ["快速启动", "完全控制", "成本低"],
            "适用": "开发测试、小规模部署",
            "复杂度": "⭐⭐"
        },
        "LangGraph Cloud": {
            "优势": ["零运维", "自动扩展", "企业级安全"],
            "适用": "快速上线、中小型项目",
            "复杂度": "⭐"
        },
        "混合部署": {
            "优势": ["数据安全", "灵活配置", "成本可控"],
            "适用": "合规要求、敏感数据",
            "复杂度": "⭐⭐⭐"
        },
        "Kubernetes": {
            "优势": ["高可用", "自动扩展", "企业级"],
            "适用": "大规模生产、高并发",
            "复杂度": "⭐⭐⭐⭐⭐"
        }
    }
    
    for strategy, details in deployment_strategies.items():
        print(f"\n🎯 {strategy}")
        print(f"   💡 优势: {', '.join(details['优势'])}")
        print(f"   🎯 适用: {details['适用']}")
        print(f"   📊 复杂度: {details['复杂度']}")
    
    print(f"\n{'='*60}")
    print("📊 监控集成示例:")
    
    # 演示监控集成
    try:
        monitoring = LangSmithMonitoring()
        monitoring.setup_tracing()
        
        # 模拟对话记录
        monitoring.log_conversation(
            session_id="demo_session",
            user_message="测试消息",
            ai_response="测试回复",
            processing_time=1.5,
            metadata={"model": "glm-4.5", "env": "demo"}
        )
        
        print("✅ 监控集成演示成功")
        
    except Exception as e:
        print(f"⚠️ 监控演示失败: {e}")
    
    print(f"\n{'='*60}")
    print("📈 指标收集示例:")
    
    # 演示指标收集
    metrics_collector = MetricsCollector()
    metrics_collector.track_session("demo_session_1")
    metrics_collector.track_session("demo_session_2")
    metrics_collector.track_llm_usage("glm-4.5", 150)
    
    current_metrics = metrics_collector.get_metrics()
    print(f"📊 当前指标: {json.dumps(current_metrics, indent=2, ensure_ascii=False)}")
    
    print(f"\n{'='*60}")
    print("🎉 部署策略演示完成！")

if __name__ == "__main__":
    demo_deployment_strategies()
```

---

## 📚 本章小结

在这一章中，我们学习了 LangGraph 应用的完整部署方案：

1. **本地 Docker 部署**：使用 Docker Compose 快速搭建完整的应用栈
2. **LangGraph Platform 部署**：利用官方 SaaS 服务实现零运维部署
3. **混合部署架构**：结合云端管理和本地数据的最佳实践
4. **Kubernetes 生产部署**：企业级的容器化部署方案
5. **监控与日志**：集成 LangSmith 和自定义指标实现全面监控

## 🎯 关键特性

- **多种部署方案** - 从本地到云端的完整解决方案
- **生产就绪** - 包含健康检查、自动扩展、监控集成
- **智谱GLM-4.5集成** - 完整适配智谱AI接口
- **监控完备** - LangSmith集成和自定义指标收集

从开发到生产，每个阶段都有对应的部署策略。选择合适的部署方案，能够让你的 AI 应用稳定、高效地为用户提供服务。

## 🎯 下一步预告

在第11章中，我们将通过**4个综合项目**来实践所学知识，从简单的天气助手到复杂的多智能体系统，让你真正掌握 LangGraph 的实战应用。准备好挑战综合项目了吗？