#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全地从文本中移除特定的emoji符号
只替换已知的emoji，不破坏文件格式
"""

import os

def safe_replace_emojis(text):
    """
    安全地替换emoji，逐个处理，不破坏格式
    """
    # 首先将⭐替换为*
    text = text.replace('⭐', '*')
    
    # 定义要移除的具体emoji列表
    emojis_to_remove = [
        '🤖', '👤', '👋', '💡', '🔧', '🤔', '📚', '🌱', '🚀', 
        '💼', '📈', '📖', '🎉', '❌', '✅', '🧮', '🔍', '🌤️', 
        '⏰', '📝', '🤝', '⚠️', '⏸️', '📋', '👨‍💼', '🔄', 
        '📤', '📥', '👥'
    ]
    
    # 逐个移除emoji
    for emoji in emojis_to_remove:
        text = text.replace(emoji, '')
    
    return text

def process_file(file_path):
    """
    处理单个文件
    """
    try:
        # 读取文件内容（保持原有编码）
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 处理emoji
        processed_content = safe_replace_emojis(content)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(processed_content)
        
        print(f"✓ 已处理: {file_path}")
        
    except Exception as e:
        print(f"✗ 处理失败 {file_path}: {e}")

def main():
    """
    主函数：处理当前目录下的所有md文件
    """
    print("开始安全地移除emoji符号...")
    
    # 查找所有md文件
    md_files = [f for f in os.listdir('.') if f.endswith('.md')]
    
    if not md_files:
        print("未找到.md文件")
        return
    
    print(f"找到 {len(md_files)} 个markdown文件")
    
    for file_path in md_files:
        process_file(file_path)
    
    print("处理完成！")

if __name__ == "__main__":
    main()