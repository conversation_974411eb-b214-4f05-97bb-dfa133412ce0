# 附录 ## 附录A：API参考手册 ### A.1 核心类和函数 #### StateGraph 类 ```python class StateGraph: """状态图的核心类""" def __init__(self, state_schema: Type[TypedDict]): """ 初始化状态图 Args: state_schema: 状态模式定义 """ pass def add_node(self, name: str, func: Callable) -> None: """ 添加节点 Args: name: 节点名称 func: 节点函数 """ pass def add_edge(self, start: str, end: str) -> None: """ 添加边 Args: start: 起始节点 end: 结束节点 """ pass def add_conditional_edges( self, start: str, condition: Callable, condition_map: Dict[str, str] ) -> None: """ 添加条件边 Args: start: 起始节点 condition: 条件函数 condition_map: 条件映射 """ pass def compile(self, checkpointer=None) -> CompiledGraph: """ 编译图 Args: checkpointer: 检查点保存器 Returns: 编译后的图 """ pass ``` #### CompiledGraph 类 ```python class CompiledGraph: """编译后的图""" def invoke(self, input_data: dict, config: dict = None) -> dict: """ 同步执行图 Args: input_data: 输入数据 config: 配置信息 Returns: 执行结果 """ pass async def ainvoke(self, input_data: dict, config: dict = None) -> dict: """ 异步执行图 Args: input_data: 输入数据 config: 配置信息 Returns: 执行结果 """ pass def stream(self, input_data: dict, config: dict = None): """ 流式执行图 Args: input_data: 输入数据 config: 配置信息 Yields: 执行过程中的状态更新 """ pass async def astream(self, input_data: dict, config: dict = None): """ 异步流式执行图 Args: input_data: 输入数据 config: 配置信息 Yields: 执行过程中的状态更新 """ pass ``` ### A.2 预构建组件 #### ToolNode 类 ```python class ToolNode: """工具节点""" def __init__(self, tools: List[BaseTool]): """ 初始化工具节点 Args: tools: 工具列表 """ pass ``` #### tools_condition 函数 ```python def tools_condition(state: dict) -> str: """ 工具调用条件函数 Args: state: 当前状态 Returns: 下一个节点名称 """ pass ``` ### A.3 检查点相关 #### SqliteSaver 类 ```python class SqliteSaver: """SQLite检查点保存器""" @classmethod def from_conn_string(cls, conn_string: str) -> 'SqliteSaver': """ 从连接字符串创建保存器 Args: conn_string: 数据库连接字符串 Returns: SqliteSaver实例 """ pass ``` #### MemorySaver 类 ```python class MemorySaver: """内存检查点保存器""" def __init__(self): """初始化内存保存器""" pass ``` ### A.4 消息类型 #### 基础消息类 ```python from langchain_core.messages import ( BaseMessage, HumanMessage, AIMessage, SystemMessage, ToolMessage ) class HumanMessage(BaseMessage): """人类消息""" def __init__(self, content: str, **kwargs): super().__init__(content=content, **kwargs) class AIMessage(BaseMessage): """AI消息""" def __init__(self, content: str, **kwargs): super().__init__(content=content, **kwargs) class SystemMessage(BaseMessage): """系统消息""" def __init__(self, content: str, **kwargs): super().__init__(content=content, **kwargs) ``` ### A.5 工具装饰器 ```python from langchain_core.tools import tool @tool def example_tool(input_text: str) -> str: """ 工具装饰器示例 Args: input_text: 输入文本 Returns: 处理结果 """ return f"Processed: {input_text}" ``` ## 附录B：配置参考 ### B.1 环境变量配置 ```bash # OpenAI配置 export OPENAI_API_KEY="your-openai-api-key" export OPENAI_API_BASE="https://api.openai.com/v1" # 可选 export OPENAI_ORGANIZATION="your-org-id" # 可选 # Anthropic配置 export ANTHROPIC_API_KEY="your-anthropic-api-key" # Google配置 export GOOGLE_API_KEY="your-google-api-key" # Azure OpenAI配置 export AZURE_OPENAI_API_KEY="your-azure-key" export AZURE_OPENAI_ENDPOINT="your-azure-endpoint" export AZURE_OPENAI_API_VERSION="2023-12-01-preview" # LangSmith配置（可选） export LANGCHAIN_TRACING_V2="true" export LANGCHAIN_API_KEY="your-langsmith-api-key" export LANGCHAIN_PROJECT="your-project-name" # 其他配置 export LANGCHAIN_VERBOSE="true" # 启用详细日志 export LANGCHAIN_DEBUG="true" # 启用调试模式 ``` ### B.2 配置文件示例 #### config.yaml ```yaml # LangGraph应用配置 app: name: "My LangGraph App" version: "1.0.0" debug: false # LLM配置 llm: provider: "openai" model: "gpt-3.5-turbo" temperature: 0.7 max_tokens: 1000 timeout: 30 # 检查点配置 checkpoint: enabled: true type: "sqlite" connection_string: "sqlite:///checkpoints.db" # 工具配置 tools: web_search: enabled: true max_results: 10 calculator: enabled: true precision: 2 # 日志配置 logging: level: "INFO" format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s" file: "app.log" ``` #### config.json ```json { "app": { "name": "My LangGraph App", "version": "1.0.0", "debug": false }, "llm": { "provider": "openai", "model": "gpt-3.5-turbo", "temperature": 0.7, "max_tokens": 1000, "timeout": 30 }, "checkpoint": { "enabled": true, "type": "sqlite", "connection_string": "sqlite:///checkpoints.db" }, "tools": { "web_search": { "enabled": true, "max_results": 10 }, "calculator": { "enabled": true, "precision": 2 } }, "logging": { "level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "app.log" } } ``` ### B.3 配置加载工具 ```python import yaml import json import os from typing import Dict, Any class ConfigLoader: """配置加载器""" @staticmethod def load_yaml(file_path: str) -> Dict[str, Any]: """加载YAML配置文件""" with open(file_path, 'r', encoding='utf-8') as f: return yaml.safe_load(f) @staticmethod def load_json(file_path: str) -> Dict[str, Any]: """加载JSON配置文件""" with open(file_path, 'r', encoding='utf-8') as f: return json.load(f) @staticmethod def load_env_vars(prefix: str = "LANGGRAPH_") -> Dict[str, str]: """加载环境变量""" return { key[len(prefix):].lower(): value for key, value in os.environ.items() if key.startswith(prefix) } @classmethod def load_config(cls, config_path: str = None) -> Dict[str, Any]: """加载配置""" config = {} # 加载文件配置 if config_path: if config_path.endswith('.yaml') or config_path.endswith('.yml'): config.update(cls.load_yaml(config_path)) elif config_path.endswith('.json'): config.update(cls.load_json(config_path)) # 加载环境变量（优先级更高） env_config = cls.load_env_vars() config.update(env_config) return config ``` ## 附录C：部署指南 ### C.1 Docker部署 #### Dockerfile ```dockerfile FROM python:3.9-slim # 设置工作目录 WORKDIR /app # 安装系统依赖 RUN apt-get update && apt-get install -y \ gcc \ && rm -rf /var/lib/apt/lists/* # 复制依赖文件 COPY requirements.txt . # 安装Python依赖 RUN pip install --no-cache-dir -r requirements.txt # 复制应用代码 COPY . . # 暴露端口 EXPOSE 8000 # 启动命令 CMD ["python", "app.py"] ``` #### docker-compose.yml ```yaml version: '3.8' services: langgraph-app: build: . ports: - "8000:8000" environment: - OPENAI_API_KEY=${OPENAI_API_KEY} - LANGCHAIN_TRACING_V2=true - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY} volumes: - ./data:/app/data - ./logs:/app/logs depends_on: - postgres restart: unless-stopped postgres: image: postgres:13 environment: - POSTGRES_DB=langgraph - POSTGRES_USER=langgraph - POSTGRES_PASSWORD=password volumes: - postgres_data:/var/lib/postgresql/data ports: - "5432:5432" redis: image: redis:6-alpine ports: - "6379:6379" volumes: - redis_data:/data volumes: postgres_data: redis_data: ``` ### C.2 云平台部署 #### AWS Lambda部署 ```python # lambda_handler.py import json from your_langgraph_app import create_app def lambda_handler(event, context): """AWS Lambda处理函数""" # 创建LangGraph应用 app = create_app() # 解析输入 body = json.loads(event.get('body', '{}')) try: # 执行图 result = app.invoke(body) return { 'statusCode': 200, 'headers': { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }, 'body': json.dumps(result) } except Exception as e: return { 'statusCode': 500, 'headers': { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }, 'body': json.dumps({'error': str(e)}) } ``` #### requirements.txt for Lambda ```txt langgraph langchain-openai langchain-community boto3 ``` #### serverless.yml (Serverless Framework) ```yaml service: langgraph-app provider: name: aws runtime: python3.9 region: us-east-1 environment: OPENAI_API_KEY: ${env:OPENAI_API_KEY} functions: langgraph: handler: lambda_handler.lambda_handler timeout: 30 memorySize: 512 events: - http: path: /invoke method: post cors: true plugins: - serverless-python-requirements custom: pythonRequirements: dockerizePip: true ``` ### C.3 Kubernetes部署 #### deployment.yaml ```yaml apiVersion: apps/v1 kind: Deployment metadata: name: langgraph-app labels: app: langgraph-app spec: replicas: 3 selector: matchLabels: app: langgraph-app template: metadata: labels: app: langgraph-app spec: containers: - name: langgraph-app image: your-registry/langgraph-app:latest ports: - containerPort: 8000 env: - name: OPENAI_API_KEY valueFrom: secretKeyRef: name: langgraph-secrets key: openai-api-key - name: DATABASE_URL valueFrom: configMapKeyRef: name: langgraph-config key: database-url resources: requests: memory: "256Mi" cpu: "250m" limits: memory: "512Mi" cpu: "500m" livenessProbe: httpGet: path: /health port: 8000 initialDelaySeconds: 30 periodSeconds: 10 readinessProbe: httpGet: path: /ready port: 8000 initialDelaySeconds: 5 periodSeconds: 5 --- apiVersion: v1 kind: Service metadata: name: langgraph-service spec: selector: app: langgraph-app ports: - protocol: TCP port: 80 targetPort: 8000 type: LoadBalancer --- apiVersion: v1 kind: Secret metadata: name: langgraph-secrets type: Opaque data: openai-api-key: <base64-encoded-api-key> --- apiVersion: v1 kind: ConfigMap metadata: name: langgraph-config data: database-url: "****************************************/langgraph" ``` ## 附录D：性能优化指南 ### D.1 性能监控 ```python import time import psutil import logging from functools import wraps from typing import Dict, Any class PerformanceMonitor: """性能监控器""" def __init__(self): self.metrics = {} self.logger = logging.getLogger(__name__) def measure_execution_time(self, func_name: str = None): """测量执行时间装饰器""" def decorator(func): @wraps(func) def wrapper(*args, **kwargs): start_time = time.time() try: result = func(*args, **kwargs) return result finally: end_time = time.time() execution_time = end_time - start_time name = func_name or func.__name__ self.record_metric(f"{name}_execution_time", execution_time) self.logger.info(f"{name} 执行时间: {execution_time:.2f}秒") return wrapper return decorator def measure_memory_usage(self): """测量内存使用""" process = psutil.Process() memory_info = process.memory_info() return { "rss": memory_info.rss / 1024 / 1024, # MB "vms": memory_info.vms / 1024 / 1024, # MB "percent": process.memory_percent() } def record_metric(self, name: str, value: float): """记录指标""" if name not in self.metrics: self.metrics[name] = [] self.metrics[name].append({ "value": value, "timestamp": time.time() }) def get_metrics_summary(self) -> Dict[str, Any]: """获取指标摘要""" summary = {} for name, values in self.metrics.items(): if values: summary[name] = { "count": len(values), "avg": sum(v["value"] for v in values) / len(values), "min": min(v["value"] for v in values), "max": max(v["value"] for v in values) } return summary # 使用示例 monitor = PerformanceMonitor() @monitor.measure_execution_time("graph_execution") def execute_graph(app, input_data): return app.invoke(input_data) ``` ### D.2 缓存策略 ```python import hashlib import pickle from functools import wraps from typing import Any, Optional import redis class CacheManager: """缓存管理器""" def __init__(self, redis_url: str = "redis://localhost:6379"): self.redis_client = redis.from_url(redis_url) def cache_result(self, ttl: int = 3600, key_prefix: str = ""): """结果缓存装饰器""" def decorator(func): @wraps(func) def wrapper(*args, **kwargs): # 生成缓存键 cache_key = self._generate_cache_key( key_prefix + func.__name__, args, kwargs ) # 尝试从缓存获取 cached_result = self._get_from_cache(cache_key) if cached_result is not None: return cached_result # 执行函数并缓存结果 result = func(*args, **kwargs) self._set_to_cache(cache_key, result, ttl) return result return wrapper return decorator def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str: """生成缓存键""" key_data = { "func": func_name, "args": args, "kwargs": kwargs } key_str = pickle.dumps(key_data) return hashlib.md5(key_str).hexdigest() def _get_from_cache(self, key: str) -> Optional[Any]: """从缓存获取数据""" try: cached_data = self.redis_client.get(key) if cached_data: return pickle.loads(cached_data) except Exception as e: logging.warning(f"缓存读取失败: {e}") return None def _set_to_cache(self, key: str, value: Any, ttl: int): """设置缓存数据""" try: serialized_data = pickle.dumps(value) self.redis_client.setex(key, ttl, serialized_data) except Exception as e: logging.warning(f"缓存写入失败: {e}") # 使用示例 cache_manager = CacheManager() @cache_manager.cache_result(ttl=1800, key_prefix="llm_") def call_llm(prompt: str, model: str = "gpt-3.5-turbo"): # 昂贵的LLM调用 return llm.invoke(prompt) ``` ### D.3 异步优化 ```python import asyncio from concurrent.futures import ThreadPoolExecutor from typing import List, Callable, Any class AsyncOptimizer: """异步优化器""" def __init__(self, max_workers: int = 4): self.executor = ThreadPoolExecutor(max_workers=max_workers) async def run_parallel_tasks(self, tasks: List[Callable], *args, **kwargs): """并行运行任务""" loop = asyncio.get_event_loop() # 创建任务 futures = [ loop.run_in_executor(self.executor, task, *args, **kwargs) for task in tasks ] # 等待所有任务完成 results = await asyncio.gather(*futures, return_exceptions=True) return results async def run_with_timeout(self, coro, timeout: float): """带超时的异步执行""" try: return await asyncio.wait_for(coro, timeout=timeout) except asyncio.TimeoutError: raise TimeoutError(f"操作超时 ({timeout}秒)") # 异步节点示例 async def async_llm_node(state: dict) -> dict: """异步LLM节点""" optimizer = AsyncOptimizer() # 并行处理多个LLM调用 tasks = [ lambda: call_llm(prompt1), lambda: call_llm(prompt2), lambda: call_llm(prompt3) ] results = await optimizer.run_parallel_tasks(tasks) return {"llm_results": results} ``` ## 附录E：扩展资源 ### E.1 官方资源 - **官方文档**: https://langchain-ai.github.io/langgraph/ - **GitHub仓库**: https://github.com/langchain-ai/langgraph - **示例代码**: https://github.com/langchain-ai/langgraph/tree/main/examples - **API参考**: https://langchain-ai.github.io/langgraph/reference/ - **发布说明**: https://github.com/langchain-ai/langgraph/releases ### E.2 社区资源 - **Discord社区**: https://discord.gg/langchain - **论坛讨论**: https://github.com/langchain-ai/langgraph/discussions - **Stack Overflow**: 标签 `langgraph` - **Reddit**: r/LangChain - **Twitter**: @LangChainAI ### E.3 学习资源 #### 在线教程 - LangChain官方教程 - YouTube视频教程 - Medium技术博客 - Dev.to开发者文章 #### 书籍推荐 - "Building LLM Applications with LangChain" - "Generative AI with Python and TensorFlow" - "Natural Language Processing with Python" #### 课程推荐 - Coursera: "Natural Language Processing Specialization" - edX: "Introduction to Artificial Intelligence" - Udemy: "LangChain & Vector Databases in Production" ### E.4 相关工具和库 #### 向量数据库 - **Chroma**: 轻量级向量数据库 - **Pinecone**: 托管向量数据库服务 - **Weaviate**: 开源向量搜索引擎 - **Qdrant**: 高性能向量数据库 #### LLM提供商 - **OpenAI**: GPT系列模型 - **Anthropic**: Claude系列模型 - **Google**: PaLM和Gemini模型 - **Cohere**: 企业级NLP API - **Hugging Face**: 开源模型平台 #### 监控和观测 - **LangSmith**: LangChain官方监控平台 - **Weights & Biases**: 机器学习实验跟踪 - **MLflow**: 开源ML生命周期管理 - **Prometheus**: 监控和告警系统 #### 部署平台 - **Vercel**: 前端和全栈应用部署 - **Railway**: 简化的云部署平台 - **Render**: 现代云平台 - **Fly.io**: 全球应用部署 ### E.5 开发工具 #### IDE和编辑器 - **VS Code**: 推荐插件 - Python - Jupyter - GitLens - Docker - **PyCharm**: 专业Python IDE - **Jupyter Lab**: 交互式开发环境 #### 调试工具 - **pdb**: Python内置调试器 - **ipdb**: 增强版调试器 - **Sentry**: 错误监控和性能监控 - **New Relic**: 应用性能监控 #### 测试工具 - **pytest**: Python测试框架 - **unittest**: Python标准测试库 - **coverage**: 代码覆盖率工具 - **tox**: 多环境测试工具 --- ## 结语 这份附录提供了LangGraph开发所需的全面参考信息。随着技术的不断发展，建议定期查看官方文档获取最新信息。 如果你在使用过程中发现任何问题或有改进建议，欢迎通过以下方式联系： - 提交GitHub Issue - 参与社区讨论 - 贡献代码和文档 祝你在LangGraph的开发之路上一切顺利！