# 第4章 工具：让机器人“动手”

到目前为止，我们的聊天机器人只能“动嘴”——接收消息、思考、回复。但真正实用的 AI 助手需要能够“动手”：搜索信息、调用 API、执行计算、操作文件等。在这一章中，我们将学习如何为 LangGraph 应用集成各种工具，让你的机器人变得真正有用。

## 环境准备

在开始之前，我们需要安装一些额外的依赖库：

```bash
# 安装 Tavily 搜索 API
uv add tavily-python

# 安装其他必要的工具库
uv add requests
uv add python-dotenv
```

**API 密钥配置：**

```bash
# 创建 .env 文件
touch .env
```

在 `.env` 文件中添加：

```env
# Tavily 搜索 API 密钥（从 https://tavily.com 获取）
TAVILY_API_KEY=your_tavily_api_key_here

# OpenAI API 密钥
OPENAI_API_KEY=your_openai_api_key_here
```

## 4.1 定义工具：搜索、计算、API 调用

### 什么是工具？

在 LangGraph 中，工具（Tool）是机器人与外部世界交互的桥梁。就像人类使用计算器、搜索引擎、电话一样，AI 助手也需要工具来完成各种任务。

```python
# 工具的本质：一个有明确输入输出的函数
def calculator_tool(expression: str) -> str:
    """
    计算器工具
    输入：数学表达式（字符串）
    输出：计算结果（字符串）
    """
    try:
        result = eval(expression)  # 注意：生产环境中不要直接使用 eval
        return f"计算结果：{result}"
    except Exception as e:
        return f"计算错误：{str(e)}"
```

### 工具的基本结构

一个标准的 LangGraph 工具需要包含以下要素：

```python
from langchain_core.tools import tool

@tool
def weather_tool(city: str) -> str:
    """
    查询天气工具
    
    Args:
        city: 城市名称，例如 "北京" 或 "上海"
    
    Returns:
        该城市的天气信息
    """
    # 这里是工具的具体实现
    # 实际应用中会调用天气 API
    weather_data = {
        "北京": "晴天，25°C",
        "上海": "多云，22°C",
        "广州": "小雨，28°C"
    }
    return weather_data.get(city, f"抱歉，暂时无法获取{city}的天气信息")
```

**工具定义的关键点：**
- **@tool 装饰器**：使用 `@tool` 装饰器将普通函数转换为工具
- **清晰的文档字符串**：描述工具的功能、参数和返回值
- **类型提示**：明确的参数类型帮助 LLM 理解如何调用
- **错误处理**：包含完善的异常处理机制
- **API密钥管理**：安全地从环境变量获取API密钥
- **安全检查**：文件操作工具必须包含路径遍历防护

**工具调用方式：**
- **在 LangGraph 内部**：框架自动处理参数传递，无需手动调用
- **在外部测试时**：使用 `tool.invoke({"param": value})` 方式调用
- **参数格式**：必须是字典格式，键名对应函数参数名

**Tavily vs 传统搜索的优势：**
- **更智能的结果**：Tavily提供AI增强的搜索摘要
- **实时性更强**：专为AI代理优化的实时搜索
- **结构化输出**：返回格式化的答案，便于LLM处理
- **更高的可靠性**：专业API服务，稳定性更好

### 常见工具类型

**1. 搜索工具**

```python
from langchain_core.tools import tool
from tavily import TavilyClient
import os

@tool
def web_search_tool(query: str) -> str:
    """
    使用Tavily搜索引擎获取最新的网络信息
    
    Args:
        query: 要搜索的查询内容，例如 "最新AI新闻" 或 "Python教程"
    
    Returns:
        格式化的搜索结果字符串
    """
    try:
        # 从环境变量获取API密钥
        api_key = os.getenv("TAVILY_API_KEY")
        if not api_key:
            return "错误：未设置 TAVILY_API_KEY 环境变量"
        
        # 创建Tavily客户端
        client = TavilyClient(api_key=api_key)
        
        # 执行搜索
        response = client.search(
            query=query,
            search_depth="basic",
            max_results=3,
            include_answer=True,
            include_raw_content=False
        )
        
        # 处理搜索结果
        if response.get("answer"):
            return f"搜索结果: {response['answer']}"
        elif response.get("results"):
            # 如果没有答案摘要，返回前几个结果
            results = response["results"][:2]
            summary = "搜索结果:\n"
            for i, result in enumerate(results, 1):
                title = result.get("title", "无标题")
                content = result.get("content", "无内容")[:200]
                summary += f"{i}. {title}: {content}...\n"
            return summary.strip()
        else:
            return "未找到相关搜索结果"
            
    except Exception as e:
        return f"搜索出错: {str(e)}"

# 使用示例
# 直接调用（在LangGraph外部测试时）
result = web_search_tool.invoke({"query": "LangGraph 教程"})
print(result)

# 在LangGraph应用中，工具会自动处理参数格式
# 无需手动调用 invoke 方法
```

**2. 计算工具**

```python
import math

@tool
def math_calculator(expression: str) -> str:
    """
    数学计算器
    
    Args:
        expression: 数学表达式，支持基本运算和数学函数
                   例如：'2 + 3 * 4', 'math.sqrt(16)', 'math.sin(math.pi/2)'
    
    Returns:
        计算结果
    """
    try:
        # 安全的数学计算环境
        allowed_names = {
            k: v for k, v in math.__dict__.items() if not k.startswith("__")
        }
        # 添加内置函数
        allowed_names.update({"abs": abs, "round": round})
        # 添加 math 模块本身，以支持 math.xxx 的调用方式
        allowed_names["math"] = math
        
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return f"计算结果：{result}"
    except Exception as e:
        return f"计算错误：{str(e)}"

# 使用示例
# 直接调用（在LangGraph外部测试时）
print(math_calculator.invoke({"expression": "2 + 3 * 4"}))      # 计算结果：14
print(math_calculator.invoke({"expression": "math.sqrt(16)"}))  # 计算结果：4.0

# 在LangGraph应用中，工具会自动处理参数格式
```

**3. API 调用工具**

```python
import requests
from typing import Optional

@tool
def api_call_tool(url: str, method: str = "GET", data: Optional[str] = None, headers: Optional[str] = None) -> str:
    """
    通用 API 调用工具
    
    Args:
        url: API 端点 URL
        method: HTTP 方法（GET, POST, PUT, DELETE, PATCH）
        data: 请求数据（JSON 字符串）
        headers: 请求头（JSON 字符串）
    
    Returns:
        API 响应结果
    """
    try:
        import json
        
        # 解析请求头
        headers_dict = {}
        if headers:
            try:
                headers_dict = json.loads(headers)
            except json.JSONDecodeError:
                return "错误：请求头格式无效，必须是有效的JSON字符串"
        
        # 解析请求数据
        json_data = {}
        if data:
            try:
                json_data = json.loads(data)
            except json.JSONDecodeError:
                return "错误：请求数据格式无效，必须是有效的JSON字符串"
        
        method = method.upper()
        
        # 根据HTTP方法执行请求
        if method == "GET":
            response = requests.get(url, headers=headers_dict, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=json_data, headers=headers_dict, timeout=10)
        elif method == "PUT":
            response = requests.put(url, json=json_data, headers=headers_dict, timeout=10)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers_dict, timeout=10)
        elif method == "PATCH":
            response = requests.patch(url, json=json_data, headers=headers_dict, timeout=10)
        else:
            return f"不支持的 HTTP 方法：{method}。支持的方法：GET, POST, PUT, DELETE, PATCH"
        
        response.raise_for_status()
        
        # 尝试解析JSON响应
        try:
            return f"API 调用成功：{response.json()}"
        except json.JSONDecodeError:
            # 如果不是JSON响应，返回文本内容
            return f"API 调用成功：{response.text[:500]}..."
        
    except requests.exceptions.Timeout:
        return "错误：请求超时，请检查网络连接或增加超时时间"
    except requests.exceptions.ConnectionError:
        return "错误：无法连接到目标服务器，请检查URL和网络"
    except requests.exceptions.HTTPError as e:
        return f"错误：HTTP错误 {e.response.status_code} - {e.response.reason}"
    except requests.exceptions.RequestException as e:
        return f"API 调用失败：{str(e)}"
    except Exception as e:
        return f"处理错误：{str(e)}"
```

**4. 文件操作工具**

```python
import os
from pathlib import Path

@tool
def file_reader_tool(file_path: str) -> str:
    """
    文件读取工具
    
    Args:
        file_path: 文件路径
    
    Returns:
        文件内容或错误信息
    """
    try:
        # 安全的路径检查
        def is_safe_path(file_path: str, allowed_dir: str = "./data") -> tuple[bool, Path]:
            """
            安全的路径检查，防止路径遍历攻击
            
            Returns:
                (is_safe, resolved_path)
            """
            try:
                allowed_dir = Path(allowed_dir).resolve()
                requested_path = Path(file_path).resolve()
                
                # 检查是否在允许的目录下
                if not requested_path.is_relative_to(allowed_dir):
                    return False, None
                    
                return True, requested_path
            except (ValueError, OSError, RuntimeError):
                return False, None
        
        # 执行安全检查
        is_safe, resolved_path = is_safe_path(file_path)
        if not is_safe:
            return "错误：不允许访问该路径"
        
        if not resolved_path.exists():
            return f"错误：文件 {file_path} 不存在"
        
        with open(resolved_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 限制返回内容长度
        if len(content) > 1000:
            content = content[:1000] + "...(内容过长，已截断)"
        
        return f"文件内容：\n{content}"
    except Exception as e:
        return f"读取文件失败：{str(e)}"

@tool
def file_writer_tool(file_path: str, content: str) -> str:
    """
    文件写入工具
    
    Args:
        file_path: 文件路径
        content: 要写入的内容
    
    Returns:
        操作结果
    """
    try:
        # 使用与读取工具相同的安全检查
        def is_safe_path(file_path: str, allowed_dir: str = "./data") -> tuple[bool, Path]:
            """
            安全的路径检查，防止路径遍历攻击
            
            Returns:
                (is_safe, resolved_path)
            """
            try:
                allowed_dir = Path(allowed_dir).resolve()
                requested_path = Path(file_path).resolve()
                
                # 检查是否在允许的目录下
                if not requested_path.is_relative_to(allowed_dir):
                    return False, None
                    
                return True, requested_path
            except (ValueError, OSError, RuntimeError):
                return False, None
        
        # 执行安全检查
        is_safe, resolved_path = is_safe_path(file_path)
        if not is_safe:
            return "错误：不允许写入该路径"
        
        # 确保目录存在
        resolved_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(resolved_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return f"成功写入文件：{file_path}"
    except Exception as e:
        return f"写入文件失败：{str(e)}"
```

### 工具设计的最佳实践

**1. 清晰的文档和类型提示**

```python
@tool
def good_tool_example(
    query: str, 
    limit: int = 10, 
    include_metadata: bool = False
) -> str:
    """
    优秀的工具示例
    
    这个工具展示了如何编写清晰、易用的工具函数。
    
    Args:
        query: 搜索查询字符串，不能为空
        limit: 返回结果的最大数量，默认为 10，范围 1-100
        include_metadata: 是否包含元数据信息，默认为 False
    
    Returns:
        格式化的搜索结果字符串
    
    Examples:
        >>> good_tool_example("Python 教程")
        "找到 10 个相关结果：..."
        
        >>> good_tool_example("AI", limit=5, include_metadata=True)
        "找到 5 个相关结果（含元数据）：..."
    """
    # 参数验证
    if not query.strip():
        return "错误：查询字符串不能为空"
    
    if not 1 <= limit <= 100:
        return "错误：limit 必须在 1-100 之间"
    
    # 工具逻辑实现
    # ...
    
    return f"找到 {limit} 个相关结果：..."
```

**2. 错误处理和边界情况**

```python
@tool
def robust_tool(input_data: str) -> str:
    """
    健壮的工具实现
    """
    def process_data(data: str) -> str:
        """
        示例数据处理函数
        在实际应用中，这里会是具体的业务逻辑
        """
        # 模拟数据处理：简单的字符串处理示例
        if not data.strip():
            return ""
        
        # 示例处理：去除多余空格，首字母大写
        processed = " ".join(data.split()).title()
        return processed
    
    try:
        # 输入验证
        if not input_data:
            return "错误：输入数据为空"
        
        if len(input_data) > 10000:
            return "错误：输入数据过长（最大 10000 字符）"
        
        # 主要逻辑
        result = process_data(input_data)
        
        # 输出验证
        if not result:
            return "警告：处理结果为空"
        
        return f"处理成功：{result}"
    
    except ValueError as e:
        return f"输入格式错误：{str(e)}"
    except TimeoutError:
        return "错误：处理超时，请稍后重试"
    except Exception as e:
        return f"处理失败：{str(e)}"
```

**3. 性能优化**

```python
from functools import lru_cache
import time

@tool
def cached_expensive_tool(query: str) -> str:
    """
    带缓存的昂贵操作工具
    """
    return _expensive_operation(query)

@lru_cache(maxsize=100)  # 缓存最近 100 个结果
def _expensive_operation(query: str) -> str:
    """实际的昂贵操作（带缓存）"""
    # 模拟耗时操作
    time.sleep(2)
    return f"昂贵操作的结果：{query}"

@tool
def timeout_tool(data: str, timeout: int = 30) -> str:
    """
    带超时控制的工具
    """
    import signal
    import time
    
    def slow_operation(data: str) -> str:
        """
        模拟耗时操作
        在实际应用中，这里会是具体的耗时任务
        """
        # 模拟处理时间
        time.sleep(2)  # 模拟2秒的处理时间
        return f"处理完成：{data.upper()}"
    
    def timeout_handler(signum, frame):
        raise TimeoutError("操作超时")
    
    try:
        # 设置超时
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout)
        
        # 执行操作
        result = slow_operation(data)
        
        # 取消超时
        signal.alarm(0)
        return result
    
    except TimeoutError:
        return f"操作超时（{timeout}秒）"
    finally:
        signal.alarm(0)  # 确保清理
```

## 4.2 ToolNode 与 tools_condition

### ToolNode：工具执行的核心

`ToolNode` 是 LangGraph 提供的预构建节点，专门用于执行工具调用。它能够：

- 自动解析 LLM 的工具调用请求
- 执行相应的工具函数
- 将结果格式化后返回给 LLM

```python
from langgraph.prebuilt import ToolNode
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage

# 定义工具列表
tools = [
    web_search_tool,
    math_calculator,
    weather_tool
]

# 创建 ToolNode
tool_node = ToolNode(tools)

# ToolNode 的工作流程示例
def demonstrate_tool_node():
    """演示 ToolNode 的工作原理"""
    
    # 模拟 LLM 生成的工具调用消息
    ai_message_with_tool_call = AIMessage(
        content="我需要搜索一些信息",
        tool_calls=[
            {
                "name": "web_search_tool",
                "args": {"query": "LangGraph 教程"},
                "id": "call_1"
            }
        ]
    )
    
    # 构造状态
    state = {
        "messages": [
            HumanMessage(content="请帮我搜索 LangGraph 教程"),
            ai_message_with_tool_call
        ]
    }
    
    # ToolNode 执行工具调用
    result = tool_node.invoke(state)
    
    # 结果包含工具执行的消息
    print("工具执行结果：")
    for message in result["messages"]:
        if isinstance(message, ToolMessage):
            print(f"工具 {message.name} 的结果：{message.content}")
```

### tools_condition：智能工具路由

`tools_condition` 是一个预构建的条件函数，用于判断是否需要调用工具：

```python
from langgraph.prebuilt import tools_condition

def create_tool_enabled_graph():
    """创建支持工具调用的图"""
    from langgraph.graph import StateGraph, START, END
    from langgraph.graph.message import add_messages
    from typing_extensions import TypedDict
    from typing import Annotated
    
    # 定义状态
    class State(TypedDict):
        messages: Annotated[list, add_messages]
    
    # 创建图
    graph = StateGraph(State)
    
    # LLM 节点：决定是否需要调用工具
    def llm_node(state: State):
        from langchain_openai import ChatOpenAI
        
        # 绑定工具到 LLM
        llm = ChatOpenAI(model="gpt-3.5-turbo")
        llm_with_tools = llm.bind_tools(tools)
        
        response = llm_with_tools.invoke(state["messages"])
        return {"messages": [response]}
    
    # 添加节点
    graph.add_node("llm", llm_node)
    graph.add_node("tools", tool_node)
    
    # 设置边
    graph.add_edge(START, "llm")
    
    # 条件边：根据 LLM 的输出决定是否调用工具
    graph.add_conditional_edges(
        "llm",
        tools_condition,  # 预构建的条件函数
        {
            "tools": "tools",    # 如果需要工具，跳转到 tools 节点
            "__end__": END       # 如果不需要工具，直接结束
        }
    )
    
    # 工具执行后回到 LLM
    graph.add_edge("tools", "llm")
    
    return graph.compile()
```

### 完整的工具集成示例

让我们构建一个完整的多功能助手：

```python
import os
from typing import Annotated
from typing_extensions import TypedDict
from dotenv import load_dotenv

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, tools_condition

# 加载环境变量
load_dotenv()

# 确保设置了必要的API密钥
os.environ.setdefault("TAVILY_API_KEY", "your_tavily_api_key")
os.environ.setdefault("OPENAI_API_KEY", "your_openai_api_key")

# 定义状态
class AssistantState(TypedDict):
    messages: Annotated[list, add_messages]

# 定义所有工具
@tool
def calculator(expression: str) -> str:
    """数学计算器，支持基本运算"""
    try:
        import math
        allowed_names = {
            k: v for k, v in math.__dict__.items() if not k.startswith("__")
        }
        # 添加内置函数
        allowed_names.update({"abs": abs, "round": round})
        # 添加 math 模块本身，以支持 math.xxx 的调用方式
        allowed_names["math"] = math
        
        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return f"计算结果：{result}"
    except Exception as e:
        return f"计算错误：{str(e)}"

@tool
def web_search(query: str) -> str:
    """
    使用Tavily进行网络搜索
    
    Args:
        query: 搜索查询内容
    
    Returns:
        搜索结果字符串
    """
    try:
        # 这里使用Tavily搜索
        from tavily import TavilyClient
        import os
        
        api_key = os.getenv("TAVILY_API_KEY")
        if not api_key:
            return "错误：未设置 TAVILY_API_KEY 环境变量"
            
        client = TavilyClient(api_key=api_key)
        response = client.search(
            query=query,
            search_depth="basic",
            max_results=3,
            include_answer=True
        )
        
        if response.get("answer"):
            return f"搜索结果: {response['answer']}"
        elif response.get("results"):
            results = response["results"][:2]
            summary = "搜索结果:\n"
            for i, result in enumerate(results, 1):
                title = result.get("title", "无标题")
                content = result.get("content", "无内容")[:150]
                summary += f"{i}. {title}: {content}...\n"
            return summary.strip()
        else:
            return "未找到相关搜索结果"
    except Exception as e:
        return f"搜索失败：{str(e)}"

@tool
def get_weather(city: str) -> str:
    """获取天气信息"""
    # 模拟天气数据
    weather_data = {
        "北京": "晴天，气温 25°C，湿度 45%",
        "上海": "多云，气温 22°C，湿度 60%",
        "广州": "小雨，气温 28°C，湿度 80%",
        "深圳": "晴天，气温 30°C，湿度 55%"
    }
    return weather_data.get(city, f"抱歉，暂时无法获取{city}的天气信息")

@tool
def current_time() -> str:
    """获取当前时间"""
    from datetime import datetime
    now = datetime.now()
    return f"当前时间：{now.strftime('%Y-%m-%d %H:%M:%S')}"

# 工具列表
tools = [calculator, web_search, get_weather, current_time]

def create_multi_tool_assistant():
    """创建多工具助手"""
    
    # 创建 ToolNode
    tool_node = ToolNode(tools)
    
    # LLM 节点
    def llm_node(state: AssistantState):
        # 系统提示
        system_message = SystemMessage(content="""
你是一个有用的AI助手，可以使用多种工具来帮助用户：

1. calculator - 进行数学计算
2. web_search - 使用Tavily搜索网络信息
3. get_weather - 查询天气信息
4. current_time - 获取当前时间

请根据用户的需求选择合适的工具，并提供有用的回答。
        """)
        
        # 准备消息
        messages = [system_message] + state["messages"]
        
        # 创建带工具的 LLM
        llm = ChatOpenAI(model="gpt-3.5-turbo", temperature=0.7)
        llm_with_tools = llm.bind_tools(tools)
        
        response = llm_with_tools.invoke(messages)
        return {"messages": [response]}
    
    # 构建图
    graph = StateGraph(AssistantState)
    
    # 添加节点
    graph.add_node("llm", llm_node)
    graph.add_node("tools", tool_node)
    
    # 设置边
    graph.add_edge(START, "llm")
    
    # 条件边：智能路由
    graph.add_conditional_edges(
        "llm",
        tools_condition,
        {
            "tools": "tools",
            "__end__": END
        }
    )
    
    # 工具执行后回到 LLM 进行总结
    graph.add_edge("tools", "llm")
    
    return graph.compile()

def demo_multi_tool_assistant():
    """演示多工具助手"""
    app = create_multi_tool_assistant()
    
    test_queries = [
        "现在几点了？",
        "帮我计算 15 * 23 + 45",
        "北京今天天气怎么样？",
        "搜索一下最新的 AI 新闻",
        "计算圆周率的前5位小数，然后告诉我现在的时间"
    ]
    
    for query in test_queries:
        print(f"\n🤔 用户：{query}")
        result = app.invoke({
            "messages": [HumanMessage(content=query)]
        })
        
        # 获取最后的 AI 回复
        last_message = result["messages"][-1]
        print(f"🤖 助手：{last_message.content}")
        print("-" * 50)

if __name__ == "__main__":
    # 运行前请确保设置环境变量：
    # export TAVILY_API_KEY="your_tavily_api_key"
    # export OPENAI_API_KEY="your_openai_api_key"
    demo_multi_tool_assistant()
```

## 4.3 条件执行：何时调用工具？

### 工具调用的决策逻辑

并不是每个用户请求都需要调用工具。一个智能的助手需要能够判断：

- 什么时候需要工具？
- 需要哪个工具？
- 如何组合多个工具？

```python
def smart_tool_routing(state: AssistantState) -> str:
    """智能工具路由决策"""
    last_message = state["messages"][-1]
    
    # 如果是 AI 消息且包含工具调用
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        return "execute_tools"
    
    # 分析用户意图
    user_content = ""
    for msg in reversed(state["messages"]):
        if isinstance(msg, HumanMessage):
            user_content = msg.content.lower()
            break
    
    # 基于关键词的简单路由
    if any(keyword in user_content for keyword in ["计算", "算", "数学"]):
        return "suggest_calculator"
    elif any(keyword in user_content for keyword in ["搜索", "查找", "搜"]):
        return "suggest_search"
    elif any(keyword in user_content for keyword in ["天气", "气温", "下雨"]):
        return "suggest_weather"
    elif any(keyword in user_content for keyword in ["时间", "几点", "现在"]):
        return "suggest_time"
    else:
        return "general_response"
```

### 工具调用的错误处理

```python
def robust_tool_node(state: AssistantState):
    """带错误处理的工具节点"""
    messages = state["messages"]
    last_message = messages[-1]
    
    if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
        return {"messages": []}
    
    tool_results = []
    
    for tool_call in last_message.tool_calls:
        try:
            # 查找对应的工具
            tool_func = None
            for tool in tools:
                if tool.name == tool_call["name"]:
                    tool_func = tool
                    break
            
            if tool_func is None:
                result = f"错误：未找到工具 {tool_call['name']}"
            else:
                # 获取工具调用参数并进行验证
                tool_args = tool_call.get("args", {})
                
                # 参数验证和智能修复
                if not tool_args or tool_args == {}:
                    # 针对计算器工具的智能参数修复
                    if tool_call['name'] == 'calculator':
                        # 尝试从AI消息内容中提取数学表达式
                        ai_content = last_message.content.lower()
                        if any(op in ai_content for op in ['计算', '算', '+', '-', '*', '/', '除以', '乘以']):
                            # 提取可能的数学表达式
                            import re
                            # 查找数字和运算符的组合
                            math_patterns = [
                                r'(\d+)\s*除以\s*(\d+)',  # "100除以7"
                                r'(\d+)\s*/\s*(\d+)',     # "100/7"
                                r'(\d+)\s*\*\s*(\d+)',    # "2*3"
                                r'(\d+)\s*\+\s*(\d+)',    # "2+3"
                                r'(\d+)\s*-\s*(\d+)',     # "5-2"
                            ]
                            
                            expression = None
                            for pattern in math_patterns:
                                match = re.search(pattern, ai_content)
                                if match:
                                    if '除以' in pattern:
                                        expression = f"{match.group(1)} / {match.group(2)}"
                                    elif '/' in pattern:
                                        expression = f"{match.group(1)} / {match.group(2)}"
                                    elif '*' in pattern:
                                        expression = f"{match.group(1)} * {match.group(2)}"
                                    elif '+' in pattern:
                                        expression = f"{match.group(1)} + {match.group(2)}"
                                    elif '-' in pattern:
                                        expression = f"{match.group(1)} - {match.group(2)}"
                                    break
                            
                            if expression:
                                tool_args = {"expression": expression}
                                result = tool_func.invoke(tool_args)
                            else:
                                result = "错误：无法识别要计算的数学表达式，请明确指定计算内容"
                        else:
                            result = "错误：calculator工具缺少必要的expression参数"
                    else:
                        result = f"错误：工具 {tool_call['name']} 缺少必要参数"
                else:
                    # 正常执行工具
                    result = tool_func.invoke(tool_args)
            
            # 创建工具消息
            tool_message = ToolMessage(
                content=result,
                tool_call_id=tool_call["id"],
                name=tool_call["name"]
            )
            tool_results.append(tool_message)
            
        except Exception as e:
            # 错误处理
            error_message = ToolMessage(
                content=f"工具执行失败：{str(e)}",
                tool_call_id=tool_call["id"],
                name=tool_call["name"]
            )
            tool_results.append(error_message)
    
    return {"messages": tool_results}
```

### 工具链：组合多个工具

有时候，完成一个任务需要调用多个工具：

```python
@tool
def research_and_summarize(topic: str) -> str:
    """研究主题并总结（组合工具示例）"""
    
    # 步骤1：搜索信息
    search_result = web_search.invoke({"query": topic})
    
    # 步骤2：如果涉及数字，进行计算
    if any(char.isdigit() for char in search_result):
        # 这里可以提取数字并进行相关计算
        pass
    
    # 步骤3：获取当前时间作为报告时间戳
    timestamp = current_time.invoke({})
    
    # 步骤4：组合结果
    summary = f"""
研究报告：{topic}

{timestamp}

搜索结果摘要：
{search_result[:200]}...

注：这是一个自动生成的研究摘要。
    """
    
    return summary

# 将组合工具添加到工具列表
advanced_tools = tools + [research_and_summarize]
```

## 4.4 把工具结果写回状态

### 状态更新策略

工具执行的结果需要正确地写回到状态中，以便后续节点能够使用：

```python
class EnhancedState(TypedDict):
    messages: Annotated[list, add_messages]
    tool_results: dict  # 存储工具执行结果
    context: dict  # 存储上下文信息
    user_preferences: dict  # 用户偏好

def enhanced_tool_node(state: EnhancedState):
    """增强的工具节点，更新多个状态字段"""
    
    # 使用改进的工具执行逻辑
    tool_messages = robust_tool_node(state)["messages"]
    
    # 提取工具结果到专门的字段
    tool_results = {}
    context_updates = {}
    
    for msg in tool_messages:
        if isinstance(msg, ToolMessage):
            tool_results[msg.name] = msg.content
            
            # 根据工具类型更新上下文
            if msg.name == "get_weather":
                context_updates["last_weather_query"] = msg.content
            elif msg.name == "calculator":
                context_updates["last_calculation"] = msg.content
    
    return {
        "messages": tool_messages,
        "tool_results": tool_results,
        "context": context_updates
    }
```

### 工具结果的格式化

```python
def format_tool_results(tool_results: dict) -> str:
    """格式化工具结果为用户友好的文本"""
    if not tool_results:
        return ""
    
    formatted_parts = []
    for tool_name, result in tool_results.items():
        if tool_name == "calculator":
            formatted_parts.append(f"🧮 计算结果：{result}")
        elif tool_name == "web_search":
            formatted_parts.append(f"🔍 Tavily搜索发现：{result}")
        elif tool_name == "get_weather":
            formatted_parts.append(f"🌤️ 天气信息：{result}")
        elif tool_name == "current_time":
            formatted_parts.append(f"⏰ {result}")
        else:
            formatted_parts.append(f"🔧 {tool_name}：{result}")
    
    return "\n\n".join(formatted_parts)

def summary_node(state: EnhancedState):
    """总结节点：整合工具结果并生成最终回复"""
    
    # 获取工具结果
    tool_results = state.get("tool_results", {})
    formatted_results = format_tool_results(tool_results)
    
    if formatted_results:
        # 如果有工具结果，生成包含结果的回复
        llm = ChatOpenAI(model="gpt-3.5-turbo")
        summary_prompt = f"""
基于以下工具执行结果，为用户生成一个友好、有用的回复：

{formatted_results}

请用自然的语言总结这些信息，并回答用户的问题。
        """
        
        response = llm.invoke([HumanMessage(content=summary_prompt)])
        return {"messages": [response]}
    else:
        # 没有工具结果，直接进行对话
        return {"messages": []}
```

### 完整的工具集成工作流

```python
def create_complete_tool_workflow():
    """创建完整的工具集成工作流"""
    
    graph = StateGraph(EnhancedState)
    
    # 意图分析节点
    def intent_analysis_node(state: EnhancedState):
        """分析用户意图"""
        last_user_message = ""
        for msg in reversed(state["messages"]):
            if isinstance(msg, HumanMessage):
                last_user_message = msg.content
                break
        
        # 简单的意图分析
        intent = "general"
        confidence = 0.5
        
        if any(kw in last_user_message.lower() for kw in ["计算", "算"]):
            intent = "calculation"
            confidence = 0.9
        elif any(kw in last_user_message.lower() for kw in ["搜索", "查"]):
            intent = "search"
            confidence = 0.8
        elif any(kw in last_user_message.lower() for kw in ["天气"]):
            intent = "weather"
            confidence = 0.9
        
        return {
            "context": {
                "intent": intent,
                "confidence": confidence,
                "analyzed_message": last_user_message
            }
        }
    
    # LLM 决策节点
    def llm_decision_node(state: EnhancedState):
        """LLM 决策是否需要工具"""
        llm = ChatOpenAI(model="gpt-3.5-turbo")
        llm_with_tools = llm.bind_tools(tools)
        
        # 构造消息，包含上下文信息
        context = state.get("context", {})
        system_prompt = f"""
你是一个智能助手，可以使用工具来帮助用户。

当前分析的用户意图：{context.get('intent', 'unknown')}
置信度：{context.get('confidence', 0)}

根据用户的需求，决定是否需要使用工具，以及使用哪些工具。
        """
        
        messages = [SystemMessage(content=system_prompt)] + state["messages"]
        response = llm_with_tools.invoke(messages)
        return {"messages": [response]}
    
    # 路由函数
    def route_after_llm(state: EnhancedState) -> str:
        """LLM 决策后的路由"""
        last_message = state["messages"][-1]
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "execute_tools"
        else:
            return "final_response"
    
    # 最终回复节点
    def final_response_node(state: EnhancedState):
        """生成最终回复"""
        # 如果没有工具调用，直接返回 LLM 的回复
        return {}
    
    # 构建图
    graph.add_node("intent_analysis", intent_analysis_node)
    graph.add_node("llm_decision", llm_decision_node)
    graph.add_node("execute_tools", enhanced_tool_node)
    graph.add_node("summary", summary_node)
    graph.add_node("final_response", final_response_node)
    
    # 设置边
    graph.add_edge(START, "intent_analysis")
    graph.add_edge("intent_analysis", "llm_decision")
    
    # 条件边：根据 LLM 决策路由
    graph.add_conditional_edges(
        "llm_decision",
        route_after_llm,
        {
            "execute_tools": "execute_tools",
            "final_response": "final_response"
        }
    )
    
    # 工具执行后进行总结
    graph.add_edge("execute_tools", "summary")
    
    # 结束边
    graph.add_edge("summary", END)
    graph.add_edge("final_response", END)
    
    return graph.compile()

# 测试完整工作流
def test_complete_workflow():
    """测试完整的工具工作流"""
    app = create_complete_tool_workflow()
    
    test_cases = [
        "帮我计算 123 * 456",
        "搜索最新的人工智能新闻",
        "北京今天天气如何？",
        "你好，很高兴认识你",
        "现在几点了？然后帮我算一下 100 除以 7"
    ]
    
    for query in test_cases:
        print(f"\n{'='*60}")
        print(f"🤔 用户：{query}")
        
        result = app.invoke({
            "messages": [HumanMessage(content=query)],
            "tool_results": {},
            "context": {},
            "user_preferences": {}
        })
        
        # 显示最终结果
        if result["messages"]:
            last_message = result["messages"][-1]
            print(f"🤖 助手：{last_message.content}")
        
        # 显示工具使用情况
        if result.get("tool_results"):
            print(f"🔧 使用的工具：{list(result['tool_results'].keys())}")
        
        # 显示上下文信息
        if result.get("context"):
            print(f"📝 上下文：{result['context']}")

if __name__ == "__main__":
    # 运行前请确保设置环境变量：
    # export TAVILY_API_KEY="your_tavily_api_key"
    # export OPENAI_API_KEY="your_openai_api_key"
    # 或者在 .env 文件中配置
    test_complete_workflow()
```

---

## 本章小结

在这一章中，我们学习了如何为 LangGraph 应用集成各种工具，让 AI 助手能够真正“动手”：

- **现代化工具定义**：学会了如何使用 Tavily 搜索、计算、API 调用等各种工具
- **Tavily 搜索集成**：掌握了如何集成先进的 AI 增强搜索工具
- **ToolNode 和 tools_condition**：掌握了 LangGraph 的预构建工具组件
- **条件执行**：理解了何时以及如何智能地调用工具
- **状态管理**：学会了如何将工具结果正确地写回状态

**章节亮点：**
-  使用 Tavily 实现更智能的实时搜索
-  提供了完整的工具类实现示例
-  包含了完善的错误处理机制
-  展示了复杂的工具组合使用

通过工具集成，你的 AI 助手从只能“聊天”升级为能够执行实际任务的智能代理。这为构建真正实用的 AI 应用奠定了基础。

**工具参数验证与智能修复：**

在实际使用中，LLM 可能会出现参数传递不完整的情况。我们的 `robust_tool_node` 实现了智能参数验证和修复机制：

-  **自动参数检测**：检查工具调用是否包含必要参数
-  **智能表达式提取**：从对话内容中自动识别数学表达式
-  **多格式支持**：支持"100除以7"、"2+3"等自然语言表达
-  **错误恢复**：当参数缺失时提供清晰的错误信息

这种方法确保了即使 LLM 在复杂查询中传递空参数，系统也能智能识别用户意图并正确执行工具。

**重要提示：**
- 记得在 [Tavily 官网](https://tavily.com) 注册并获取 API 密钥
- 在生产环境中使用环境变量管理 API 密钥
- Tavily 搜索对于 AI 代理优化，提供更好的搜索体验

**安全警告：**
-  **文件操作安全**：本章的文件工具已包含路径遍历防护，但在实际使用时仍需谨慎
-  **API 调用安全**：调用外部 API 时注意验证响应内容，防止恶意数据
-  **计算器安全**：虽然限制了 `__builtins__`，但在处理用户输入时仍需额外验证
-  **生产环境**：在生产环境中部署前，建议进行全面的安全审计

**下一步预告：**

在第5章中，我们将深入探讨记忆与状态管理，学习如何让你的 AI 助手拥有长期记忆，能够跨会话保持上下文，并支持复杂的状态回溯功能。准备好让你的助手变得更加智能了吗？