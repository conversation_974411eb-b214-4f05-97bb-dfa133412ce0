# 第7章 让对话更聪明

有了基础的对话能力和工具集成，你的 AI 助手已经能够处理很多任务了。但是，如何让它的回答更准确、更有用、更符合你的期望呢？这就需要一些"调教"技巧。

在这一章中，我们将学习如何通过系统提示词、结构化输出和优化技巧，让你的 AI 助手变得更加聪明。

## 7.1 系统提示词与角色扮演

### 系统提示词的威力

系统提示词（System Prompt）就像是给 AI 的"工作说明书"，它定义了 AI 的角色、行为准则和回答风格。一个好的系统提示词能够让 AI 的表现发生质的飞跃。

```python
# 普通的系统提示词
basic_prompt = "你是一个AI助手。"

# 专业的系统提示词
professional_prompt = """
你是一位经验丰富的技术顾问，专门帮助开发者解决编程问题。

你的特点：
- 回答准确、简洁，直击要点
- 提供可执行的代码示例
- 解释技术概念时深入浅出
- 主动指出潜在的问题和最佳实践
- 保持友好和耐心的语调

回答格式：
1. 先简要回答问题
2. 提供代码示例（如适用）
3. 解释关键概念
4. 给出相关建议

请始终保持专业和有用。
"""
```

### 角色扮演的艺术

不同的角色会带来不同的对话风格和专业知识：

```python
import os
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_community.chat_models import ChatZhipuAI
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict
from typing import Annotated

class SmartChatState(TypedDict):
    messages: Annotated[list, add_messages]
    role: str
    context: dict
    response_format: str

# 定义不同的角色
ROLES = {
    "技术专家": {
        "system_prompt": """
你是一位资深的技术专家，拥有15年的软件开发经验。

专业领域：
- Python、JavaScript、Go等编程语言
- 系统架构设计
- 数据库优化
- 云服务部署

回答风格：
- 技术准确，逻辑清晰
- 提供最佳实践建议
- 包含具体的代码示例
- 考虑性能和安全因素

当遇到技术问题时，你会：
1. 分析问题的根本原因
2. 提供多种解决方案
3. 推荐最优方案并说明理由
4. 提醒可能的风险点
""",
        "response_format": "technical"
    },
    
    "产品经理": {
        "system_prompt": """
你是一位经验丰富的产品经理，擅长将技术转化为商业价值。

核心能力：
- 用户需求分析
- 产品规划和路线图
- 跨团队协调
- 数据驱动决策

沟通风格：
- 以用户价值为中心
- 用商业语言解释技术概念
- 关注投入产出比
- 提供可执行的行动计划

回答问题时，你会：
1. 从用户角度分析需求
2. 评估技术方案的商业价值
3. 考虑实施的优先级
4. 提出具体的执行建议
""",
        "response_format": "business"
    },
    
    "教学助手": {
        "system_prompt": """
你是一位耐心的编程教学助手，专门帮助初学者学习编程。

教学理念：
- 循序渐进，由浅入深
- 用生活化的比喻解释抽象概念
- 鼓励学生思考和实践
- 及时给予正面反馈

教学方法：
- 先解释概念，再给出示例
- 将复杂问题分解为简单步骤
- 提供练习题和思考题
- 预防常见错误

回答学生问题时：
1. 确认学生的理解水平
2. 用简单的语言解释概念
3. 提供循序渐进的示例
4. 给出练习建议
""",
        "response_format": "educational"
    }
}

def create_role_based_chatbot():
    """创建基于角色的聊天机器人"""
    
    def role_selection_node(state: SmartChatState):
        """角色选择节点"""
        last_message = state["messages"][-1].content.lower()
        
        # 根据用户问题自动选择角色
        if any(keyword in last_message for keyword in ["代码", "编程", "bug", "算法", "架构"]):
            selected_role = "技术专家"
        elif any(keyword in last_message for keyword in ["产品", "用户", "需求", "商业", "市场"]):
            selected_role = "产品经理"
        elif any(keyword in last_message for keyword in ["学习", "教", "不懂", "初学", "怎么开始"]):
            selected_role = "教学助手"
        else:
            selected_role = "技术专家"  # 默认角色
            
        return {
            "role": selected_role,
            "response_format": ROLES[selected_role]["response_format"]
        }
    
    def smart_response_node(state: SmartChatState):
        """智能回复节点"""
        role = state.get("role", "技术专家")
        role_config = ROLES[role]
        
        # 构造角色化的系统提示
        system_message = SystemMessage(content=role_config["system_prompt"])
        messages = [system_message] + state["messages"]
        
        # 根据角色选择合适的模型参数
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"messages": [AIMessage(content="⚠️ 未配置智谱API密钥，请设置ZHIPUAI_API_KEY环境变量")]}
        
        def call_with_retry(messages, role, max_retries=2):
            """带重试的GLM调用"""
            for attempt in range(max_retries):
                try:
                    if role == "技术专家":
                        llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=35)
                    elif role == "产品经理":
                        llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key, timeout=35)
                    else:  # 教学助手
                        llm = ChatZhipuAI(model="glm-4.5", temperature=0.5, api_key=api_key, timeout=35)
                    
                    response = llm.invoke(messages)
                    
                    # 添加角色标识
                    role_tagged_response = f"[{role}] {response.content}"
                    response.content = role_tagged_response
                    
                    return {"messages": [response]}
                    
                except Exception as e:
                    if attempt < max_retries - 1:
                        # 重试前短暂等待
                        import time
                        time.sleep(1)
                        continue
                    else:
                        # 最后一次失败，返回友好的错误信息
                        return {"messages": [AIMessage(content=f"[{role}] ⚠️ 网络繁忙，请稍后重试。（{str(e)[:50]}...）")]}
        
        return call_with_retry(messages, role)
    
    # 构建图
    graph = StateGraph(SmartChatState)
    graph.add_node("role_selection", role_selection_node)
    graph.add_node("smart_response", smart_response_node)
    
    graph.add_edge(START, "role_selection")
    graph.add_edge("role_selection", "smart_response")
    graph.add_edge("smart_response", END)
    
    return graph.compile()
```

### 动态提示词生成

根据上下文动态生成系统提示词，让 AI 更好地适应不同场景：

```python
def create_dynamic_prompt_system():
    """创建动态提示词系统"""
    
    def context_analyzer_node(state: SmartChatState):
        """上下文分析节点"""
        messages = state["messages"]
        
        # 分析对话历史
        conversation_length = len(messages)
        topics = []
        complexity_level = "basic"
        
        for msg in messages:
            content = msg.content.lower()
            
            # 主题检测
            if "python" in content or "代码" in content:
                topics.append("programming")
            if "数据库" in content or "sql" in content:
                topics.append("database")
            if "部署" in content or "服务器" in content:
                topics.append("deployment")
            
            # 复杂度评估
            if any(word in content for word in ["高级", "复杂", "架构", "优化"]):
                complexity_level = "advanced"
            elif any(word in content for word in ["中级", "进阶", "原理"]):
                complexity_level = "intermediate"
        
        return {
            "context": {
                "conversation_length": conversation_length,
                "topics": list(set(topics)),
                "complexity_level": complexity_level,
                "is_follow_up": conversation_length > 2
            }
        }
    
    def dynamic_prompt_generator_node(state: SmartChatState):
        """动态提示词生成节点"""
        context = state.get("context", {})
        role = state.get("role", "技术专家")
        
        # 基础提示词
        base_prompt = ROLES[role]["system_prompt"]
        
        # 根据上下文添加动态内容
        dynamic_additions = []
        
        # 对话长度相关
        if context.get("conversation_length", 0) > 5:
            dynamic_additions.append("""
注意：这是一个较长的对话，请保持上下文的连贯性，
可以适当引用之前讨论的内容。
""")
        
        # 主题相关
        topics = context.get("topics", [])
        if "programming" in topics:
            dynamic_additions.append("""
当前讨论涉及编程话题，请：
- 提供可运行的代码示例
- 解释代码的工作原理
- 指出潜在的问题和改进点
""")
        
        if "database" in topics:
            dynamic_additions.append("""
当前讨论涉及数据库话题，请：
- 考虑数据安全和性能
- 提供SQL示例时注意最佳实践
- 解释索引和查询优化
""")
        
        # 复杂度相关
        complexity = context.get("complexity_level", "basic")
        if complexity == "advanced":
            dynamic_additions.append("""
用户提出了高级问题，请：
- 深入技术细节
- 讨论架构和设计模式
- 考虑扩展性和维护性
""")
        elif complexity == "basic":
            dynamic_additions.append("""
用户可能是初学者，请：
- 使用简单易懂的语言
- 提供基础概念解释
- 给出学习建议和资源
""")
        
        # 组合最终提示词
        final_prompt = base_prompt + "\n\n" + "\n".join(dynamic_additions)
        
        return {"context": {**context, "dynamic_prompt": final_prompt}}
    
    def contextual_response_node(state: SmartChatState):
        """上下文化回复节点"""
        context = state.get("context", {})
        dynamic_prompt = context.get("dynamic_prompt", ROLES["技术专家"]["system_prompt"])
        
        system_message = SystemMessage(content=dynamic_prompt)
        messages = [system_message] + state["messages"]
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"messages": [AIMessage(content="⚠️ 未配置智谱API密钥")]}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.6, api_key=api_key, timeout=20)
            response = llm.invoke(messages)
            return {"messages": [response]}
        except Exception as e:
            return {"messages": [AIMessage(content=f"❌ 调用失败: {e}")]}
    
    # 构建图
    graph = StateGraph(SmartChatState)
    graph.add_node("context_analyzer", context_analyzer_node)
    graph.add_node("prompt_generator", dynamic_prompt_generator_node)
    graph.add_node("contextual_response", contextual_response_node)
    
    graph.add_edge(START, "context_analyzer")
    graph.add_edge("context_analyzer", "prompt_generator")
    graph.add_edge("prompt_generator", "contextual_response")
    graph.add_edge("contextual_response", END)
    
    return graph.compile()
```

## 7.2 结构化输出：JSON, Pydantic

### 为什么需要结构化输出？

有时候，我们需要 AI 返回的不是自然语言，而是结构化的数据，比如：
- JSON 格式的配置文件
- 表格数据
- 代码片段
- 分类结果

```python
from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum

class Priority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class TaskItem(BaseModel):
    """任务项目模型"""
    title: str = Field(description="任务标题")
    description: str = Field(description="任务描述")
    priority: Priority = Field(description="优先级")
    estimated_hours: Optional[float] = Field(description="预估工时", ge=0)
    tags: List[str] = Field(default=[], description="标签列表")

class TaskList(BaseModel):
    """任务列表模型"""
    project_name: str = Field(description="项目名称")
    tasks: List[TaskItem] = Field(description="任务列表")
    total_estimated_hours: float = Field(description="总预估工时")

def create_structured_output_chatbot():
    """创建结构化输出聊天机器人"""
    
    def task_extraction_node(state: SmartChatState):
        """任务提取节点"""
        from langchain_core.output_parsers import PydanticOutputParser
        
        # 创建输出解析器
        parser = PydanticOutputParser(pydantic_object=TaskList)
        
        # 构造结构化提示
        format_instructions = parser.get_format_instructions()
        system_prompt = f"""
你是一个项目管理助手，专门帮助用户将需求转换为结构化的任务列表。

请根据用户的描述，提取出具体的任务项目，并按照以下格式返回：

{format_instructions}

注意事项：
- 将大的需求分解为具体的小任务
- 合理评估每个任务的优先级
- 预估工时要现实可行
- 添加有用的标签便于分类
"""
        
        user_message = state["messages"][-1].content
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"messages": [AIMessage(content="⚠️ 未配置智谱API密钥")]}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=20)
            
            # 构造完整的提示
            full_prompt = f"{system_prompt}\n\n用户需求：{user_message}"
            
            response = llm.invoke([SystemMessage(content=full_prompt)])
            
            # 清理响应内容中的markdown格式
            cleaned_content = response.content.strip()
            if cleaned_content.startswith("```json"):
                cleaned_content = cleaned_content.replace("```json", "").replace("```", "").strip()
            elif cleaned_content.startswith("```"):
                cleaned_content = cleaned_content.replace("```", "").strip()
            
            # 解析结构化输出
            parsed_result = parser.parse(cleaned_content)
            
            # 转换为友好的显示格式
            formatted_output = format_task_list(parsed_result)
            
            return {
                "messages": [AIMessage(content=formatted_output)],
                "structured_data": parsed_result.dict()
            }
            
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"解析失败：{str(e)}，请重新描述您的需求。")]
            }
    
    def format_task_list(task_list: TaskList) -> str:
        """格式化任务列表为可读文本"""
        output = f"📋 项目：{task_list.project_name}\n"
        output += f"⏱️ 总预估工时：{task_list.total_estimated_hours} 小时\n\n"
        
        for i, task in enumerate(task_list.tasks, 1):
            priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}
            emoji = priority_emoji.get(task.priority.value, "")
            
            output += f"{i}. {emoji} {task.title}\n"
            output += f"   📝 {task.description}\n"
            output += f"   ⏱️ 预估：{task.estimated_hours or 0} 小时\n"
            
            if task.tags:
                output += f"   🏷️ 标签：{', '.join(task.tags)}\n"
            output += "\n"
        
        return output
    
    # 构建图
    graph = StateGraph(SmartChatState)
    graph.add_node("task_extraction", task_extraction_node)
    
    graph.add_edge(START, "task_extraction")
    graph.add_edge("task_extraction", END)
    
    return graph.compile()
```

### 多种输出格式支持

```python
class CodeAnalysis(BaseModel):
    """代码分析结果"""
    language: str = Field(description="编程语言")
    complexity_score: float = Field(description="复杂度评分 (1-10)", ge=1, le=10)
    issues: List[str] = Field(description="发现的问题")
    suggestions: List[str] = Field(description="改进建议")
    code_quality: str = Field(description="代码质量评级")

class DataSummary(BaseModel):
    """数据摘要"""
    total_records: int = Field(description="总记录数")
    key_metrics: dict = Field(description="关键指标")
    insights: List[str] = Field(description="数据洞察")
    recommendations: List[str] = Field(description="建议")

def create_multi_format_analyzer():
    """创建多格式分析器"""
    
    def format_detector_node(state: SmartChatState):
        """格式检测节点"""
        last_message = state["messages"][-1].content.lower()
        
        if "代码" in last_message or "code" in last_message:
            return {"response_format": "code_analysis"}
        elif "数据" in last_message or "分析" in last_message:
            return {"response_format": "data_summary"}
        else:
            return {"response_format": "general"}
    
    def structured_analyzer_node(state: SmartChatState):
        """结构化分析节点"""
        response_format = state.get("response_format", "general")
        user_message = state["messages"][-1].content
        
        if response_format == "code_analysis":
            return analyze_code_structure(user_message)
        elif response_format == "data_summary":
            return analyze_data_structure(user_message)
        else:
            return {"messages": [AIMessage(content="请提供代码或数据进行分析。")]}
    
    def analyze_code_structure(user_message: str):
        """分析代码结构"""
        from langchain_core.output_parsers import PydanticOutputParser
        
        parser = PydanticOutputParser(pydantic_object=CodeAnalysis)
        format_instructions = parser.get_format_instructions()
        
        system_prompt = f"""
你是一个代码审查专家，请分析用户提供的代码。

{format_instructions}

分析要点：
- 评估代码复杂度（考虑嵌套层级、函数长度等）
- 识别潜在问题（性能、安全、可读性）
- 提供具体的改进建议
- 给出整体质量评级（优秀/良好/一般/需改进）
"""
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"messages": [AIMessage(content="⚠️ 未配置智谱API密钥")]}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.2, api_key=api_key, timeout=20)
            
            response = llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"请分析以下代码：\n\n{user_message}")
            ])
            
            parsed_result = parser.parse(response.content)
            formatted_output = format_code_analysis(parsed_result)
            
            return {
                "messages": [AIMessage(content=formatted_output)],
                "structured_data": parsed_result.dict()
            }
            
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"代码分析失败：{str(e)}")]
            }
    
    def analyze_data_structure(user_message: str):
        """分析数据结构（占位函数）"""
        return {"messages": [AIMessage(content="数据分析功能开发中...")]}
    
    def format_code_analysis(analysis: CodeAnalysis) -> str:
        """格式化代码分析结果"""
        output = f"📊 代码分析报告\n\n"
        output += f"💻 语言：{analysis.language}\n"
        output += f"📈 复杂度：{analysis.complexity_score}/10\n"
        output += f"⭐ 质量评级：{analysis.code_quality}\n\n"
        
        if analysis.issues:
            output += "⚠️ 发现的问题：\n"
            for issue in analysis.issues:
                output += f"  • {issue}\n"
            output += "\n"
        
        if analysis.suggestions:
            output += "💡 改进建议：\n"
            for suggestion in analysis.suggestions:
                output += f"  • {suggestion}\n"
        
        return output
    
    # 构建图
    graph = StateGraph(SmartChatState)
    graph.add_node("format_detector", format_detector_node)
    graph.add_node("structured_analyzer", structured_analyzer_node)
    
    graph.add_edge(START, "format_detector")
    graph.add_edge("format_detector", "structured_analyzer")
    graph.add_edge("structured_analyzer", END)
    
    return graph.compile()
```

## 7.3 降低Token消耗：多轮上下文压缩技巧

### Token 消耗的问题

随着对话的进行，上下文会越来越长，导致：
- API 调用成本增加
- 响应速度变慢
- 可能超出模型的上下文限制

### 智能上下文压缩

```python
from datetime import datetime, timedelta

class ContextManager:
    """上下文管理器"""
    
    def __init__(self, max_tokens=4000, compression_ratio=0.6):
        self.max_tokens = max_tokens
        self.compression_ratio = compression_ratio
    
    def estimate_tokens(self, text: str) -> int:
        """估算 token 数量（简化版本）"""
        # 简化估算：1个token约等于4个字符
        return len(text) // 4
    
    def compress_messages(self, messages: list) -> list:
        """压缩消息列表"""
        total_tokens = sum(self.estimate_tokens(msg.content) for msg in messages)
        
        if total_tokens <= self.max_tokens:
            return messages  # 不需要压缩
        
        # 保留系统消息和最近的消息
        system_messages = [msg for msg in messages if isinstance(msg, SystemMessage)]
        other_messages = [msg for msg in messages if not isinstance(msg, SystemMessage)]
        
        # 计算需要保留的消息数量
        target_tokens = int(self.max_tokens * self.compression_ratio)
        
        # 从最新消息开始保留
        compressed_messages = []
        current_tokens = 0
        
        for msg in reversed(other_messages):
            msg_tokens = self.estimate_tokens(msg.content)
            if current_tokens + msg_tokens <= target_tokens:
                compressed_messages.insert(0, msg)
                current_tokens += msg_tokens
            else:
                break
        
        # 如果压缩后消息太少，添加摘要
        if len(compressed_messages) < len(other_messages) // 2:
            summary = self.create_conversation_summary(other_messages[:-len(compressed_messages)])
            summary_message = AIMessage(content=f"[对话摘要] {summary}")
            compressed_messages.insert(0, summary_message)
        
        return system_messages + compressed_messages
    
    def create_conversation_summary(self, messages: list) -> str:
        """创建对话摘要"""
        # 简化版本：提取关键信息
        topics = set()
        key_points = []
        
        for msg in messages:
            content = msg.content.lower()
            
            # 提取主题
            if "python" in content:
                topics.add("Python编程")
            if "数据库" in content:
                topics.add("数据库")
            if "部署" in content:
                topics.add("部署")
            
            # 提取关键点（简化）
            if len(msg.content) > 100:  # 较长的消息可能包含重要信息
                key_points.append(msg.content[:50] + "...")
        
        summary_parts = []
        if topics:
            summary_parts.append(f"讨论话题：{', '.join(topics)}")
        if key_points:
            summary_parts.append(f"关键内容：{'; '.join(key_points[:3])}")
        
        return " | ".join(summary_parts) if summary_parts else "一般性对话"

def create_token_optimized_chatbot():
    """创建 Token 优化的聊天机器人"""
    context_manager = ContextManager(max_tokens=3000)
    
    def context_compression_node(state: SmartChatState):
        """上下文压缩节点"""
        messages = state["messages"]
        
        # 压缩消息历史
        compressed_messages = context_manager.compress_messages(messages)
        
        # 记录压缩信息
        original_count = len(messages)
        compressed_count = len(compressed_messages)
        compression_info = {
            "original_messages": original_count,
            "compressed_messages": compressed_count,
            "compression_applied": original_count != compressed_count
        }
        
        return {
            "messages": compressed_messages,
            "context": {**state.get("context", {}), "compression_info": compression_info}
        }
    
    def optimized_response_node(state: SmartChatState):
        """优化的回复节点"""
        messages = state["messages"]
        compression_info = state.get("context", {}).get("compression_info", {})
        
        # 如果进行了压缩，在系统提示中说明
        if compression_info.get("compression_applied", False):
            context_note = f"""
注意：为了优化性能，对话历史已被压缩。
原始消息数：{compression_info['original_messages']}
当前消息数：{compression_info['compressed_messages']}
请基于当前上下文回答问题。
"""
            # 添加上下文说明到系统消息
            if messages and isinstance(messages[0], SystemMessage):
                messages[0].content += context_note
            else:
                messages.insert(0, SystemMessage(content=context_note))
        
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return {"messages": [AIMessage(content="⚠️ 未配置智谱API密钥")]}
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.7, api_key=api_key, timeout=20)
            response = llm.invoke(messages)
            return {"messages": [response]}
        except Exception as e:
            return {"messages": [AIMessage(content=f"❌ 调用失败: {e}")]}
    
    # 构建图
    graph = StateGraph(SmartChatState)
    graph.add_node("context_compression", context_compression_node)
    graph.add_node("optimized_response", optimized_response_node)
    
    graph.add_edge(START, "context_compression")
    graph.add_edge("context_compression", "optimized_response")
    graph.add_edge("optimized_response", END)
    
    return graph.compile()
```

### 高级压缩策略

```python
class AdvancedContextManager:
    """高级上下文管理器"""
    
    def __init__(self):
        self.importance_keywords = {
            "high": ["错误", "问题", "重要", "关键", "必须", "紧急"],
            "medium": ["建议", "推荐", "考虑", "可能", "应该"],
            "low": ["顺便", "另外", "补充", "例如"]
        }
    
    def calculate_message_importance(self, message) -> float:
        """计算消息重要性"""
        content = message.content.lower()
        importance_score = 0.5  # 基础分数
        
        # 基于关键词调整重要性
        for level, keywords in self.importance_keywords.items():
            for keyword in keywords:
                if keyword in content:
                    if level == "high":
                        importance_score += 0.3
                    elif level == "medium":
                        importance_score += 0.1
                    else:
                        importance_score -= 0.1
        
        # 基于消息长度调整（较长的消息可能包含更多信息）
        if len(content) > 200:
            importance_score += 0.2
        elif len(content) < 50:
            importance_score -= 0.1
        
        # 基于消息类型调整
        if isinstance(message, HumanMessage):
            importance_score += 0.1  # 用户消息稍微重要一些
        
        return max(0.0, min(1.0, importance_score))  # 限制在 0-1 范围内
    
    def smart_compress_messages(self, messages: list, target_count: int) -> list:
        """智能压缩消息"""
        if len(messages) <= target_count:
            return messages
        
        # 计算每条消息的重要性
        message_importance = [
            (msg, self.calculate_message_importance(msg), i)
            for i, msg in enumerate(messages)
        ]
        
        # 按重要性排序
        message_importance.sort(key=lambda x: x[1], reverse=True)
        
        # 选择最重要的消息
        selected_messages = message_importance[:target_count]
        
        # 按原始顺序重新排列
        selected_messages.sort(key=lambda x: x[2])
        
        return [msg for msg, _, _ in selected_messages]
    
    def create_intelligent_summary(self, messages: list) -> str:
        """创建智能摘要"""
        api_key = os.getenv("ZHIPUAI_API_KEY")
        if not api_key:
            return "对话摘要：需要API密钥"
        
        # 提取对话内容
        conversation_text = "\n".join([
            f"{'用户' if isinstance(msg, HumanMessage) else 'AI'}: {msg.content}"
            for msg in messages[-10:]  # 只摘要最近10条消息
        ])
        
        summary_prompt = f"""
请为以下对话创建一个简洁的摘要，保留关键信息：

{conversation_text}

摘要要求：
- 不超过100字
- 保留主要话题和关键结论
- 突出重要的技术细节或决策
"""
        
        try:
            llm = ChatZhipuAI(model="glm-4.5", temperature=0.3, api_key=api_key, timeout=20)
            response = llm.invoke([HumanMessage(content=summary_prompt)])
            return response.content
        except:
            return "对话摘要生成失败"
```

## 🔧 环境准备

在运行本章的示例代码之前，请确保已安装必要的依赖：

```bash
# 使用uv安装依赖
uv add langchain-community langgraph langchain-core pydantic typing-extensions
```

并设置智谱AI API密钥：

```bash
export ZHIPUAI_API_KEY="your_zhipu_api_key_here"
```

## 🚀 运行示例

```python
def demo_smart_conversation():
    """演示智能对话系统"""
    # 创建不同类型的聊天机器人
    role_based_bot = create_role_based_chatbot()
    structured_bot = create_structured_output_chatbot()
    optimized_bot = create_token_optimized_chatbot()
    
    print("🤖 智能对话系统演示")
    print("=" * 50)
    
    # 测试角色扮演
    print("\n1. 角色扮演测试")
    test_questions = [
        "我是编程新手，如何开始学习Python？",
        "我们的产品需要添加用户认证功能，有什么建议？",
        "这段代码的时间复杂度是多少？def find_max(arr): return max(arr)"
    ]
    
    for question in test_questions:
        print(f"\n❓ 问题: {question}")
        try:
            result = role_based_bot.invoke({
                "messages": [HumanMessage(content=question)],
                "role": "",
                "context": {},
                "response_format": ""
            })
            print(f"💬 回答: {result['messages'][-1].content[:100]}...")
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    # 测试结构化输出
    print("\n\n2. 结构化输出测试")
    task_request = "我需要开发一个在线商城，包括用户注册、商品展示、购物车和支付功能"
    print(f"📋 需求: {task_request}")
    
    try:
        result = structured_bot.invoke({
            "messages": [HumanMessage(content=task_request)],
            "role": "",
            "context": {},
            "response_format": ""
        })
        print(f"📝 任务分解:\n{result['messages'][-1].content}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print("\n" + "=" * 50)
    print("✅ 演示完成！")

if __name__ == "__main__":
    demo_smart_conversation()
```

---

## 📚 本章小结

在这一章中，我们学习了让对话更聪明的三大技巧：

1. **系统提示词与角色扮演**：通过精心设计的提示词让 AI 扮演不同角色，提供专业化服务
2. **结构化输出**：使用 Pydantic 模型让 AI 返回格式化的数据，便于后续处理
3. **Token 优化**：通过智能压缩和上下文管理，降低 API 成本并提升响应速度

这些技巧让你的 AI 助手不仅能够提供更准确、更专业的回答，还能以结构化的方式输出结果，同时保持良好的性能和成本效益。

## 🎯 下一步预告

在第8章中，我们将探索**多智能体协作**，学习如何让多个 AI 角色协同工作，构建更复杂、更强大的智能系统。准备好进入多智能体的世界了吗？