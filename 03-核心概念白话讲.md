# 第3章 核心概念白话讲

在第二章中，我们成功构建了第一个聊天机器人，但可能还有很多疑问：为什么要这样设计状态？节点是如何工作的？边又是什么作用？在这一章中，我们将深入浅出地解析 LangGraph 的核心概念，让你真正理解其设计哲学。

## 3.1 状态（State）：对话的"记忆"载体

### 什么是状态？

想象一下你和朋友的对话。你们能够连贯地交流，是因为你们都记得之前说过什么。LangGraph 中的状态（State）就是这样的"记忆"——它存储着应用程序在任何时刻的完整信息。

```python
# 首先导入必要的类型和工具
from typing_extensions import TypedDict
from typing import Annotated
from langchain_core.messages import AnyMessage
from langgraph.graph.message import add_messages

# 状态就像一个共享的记事本
class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]  # 对话历史
    user_info: dict  # 用户信息
    context: str  # 上下文信息
    step_count: int  # 执行步数
```

### 状态的生命周期

让我们通过一个具体例子来理解状态是如何演变的：

```python
from langchain_core.messages import HumanMessage, AIMessage

# 初始状态（用户刚开始对话）
initial_state = {
    "messages": [],
    "user_info": {},
    "context": "",
    "step_count": 0
}

# 用户发送第一条消息后
after_user_input = {
    "messages": [HumanMessage(content="你好，我想了解天气")],
    "user_info": {},
    "context": "",
    "step_count": 1
}

# AI 处理后
after_ai_response = {
    "messages": [
        HumanMessage(content="你好，我想了解天气"),
        AIMessage(content="你好！我可以帮你查询天气。请告诉我你想了解哪个城市的天气？")
    ],
    "user_info": {"intent": "weather_query"},
    "context": "waiting_for_city",
    "step_count": 2
}
```

### 状态设计的最佳实践

**1. 使用 TypedDict 定义结构**

```python
from typing_extensions import TypedDict
from typing import Annotated, Optional

class ChatState(TypedDict):
    # 必需字段
    messages: Annotated[list[AnyMessage], add_messages]
    
    # 可选字段
    user_name: Optional[str]
    session_id: Optional[str]
    
    # 业务字段
    current_topic: str
    confidence_score: float
```

**为什么用 TypedDict？**
- 提供类型提示，IDE 能够自动补全
- LangGraph 能够验证状态结构
- 代码更易读和维护

**2. 合理使用 Reducer 函数**

Reducer 决定了状态如何更新。让我们看看不同的 reducer 效果：

```python
from operator import add
from langgraph.graph.message import add_messages

class State(TypedDict):
    # 默认 reducer：覆盖更新
    current_user: str  # 新值直接替换旧值
    
    # add reducer：累加更新
    scores: Annotated[list[int], add]  # 新列表与旧列表合并
    
    # add_messages reducer：智能消息管理
    messages: Annotated[list[AnyMessage], add_messages]  # 智能处理消息更新
    
    # 自定义 reducer
    metadata: Annotated[dict, lambda old, new: {**old, **new}]  # 字典合并
```

## 3.2 节点（Node）：每一步要做什么

### 节点的本质

节点是 LangGraph 中的"工作单元"。如果把状态比作数据，那么节点就是处理数据的函数。每个节点都有一个简单的职责：接收当前状态，执行某些操作，返回状态更新。

```python
def process_user_input(message: str) -> str:
    """处理用户输入的示例函数"""
    return f"已处理消息: {message}"

def my_node(state: State) -> dict:
    """
    节点的基本结构：
    1. 接收状态
    2. 执行逻辑
    3. 返回更新
    """
    # 1. 从状态中读取需要的信息
    user_message = state["messages"][-1].content
    
    # 2. 执行具体的业务逻辑
    processed_result = process_user_input(user_message)
    
    # 3. 返回状态更新
    return {"processed_data": processed_result}
```

### 节点的类型和用途

**1. LLM 调用节点**

```python
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage

def llm_node(state: State):
    """调用大语言模型生成回复"""
    llm = ChatOpenAI(
        model="glm-4.5",
        api_key="your-zhipu-api-key",
        base_url="https://open.bigmodel.cn/api/paas/v4/",
        temperature=0.1
    )
    
    # 构造 prompt
    system_message = SystemMessage(content="你是一个有用的助手")
    messages = [system_message] + state["messages"]
    
    # 调用 LLM
    response = llm.invoke(messages)
    return {"messages": [response]}
```

**2. 工具调用节点**

```python
def search_node(state: State):
    """搜索相关信息"""
    from tavily import TavilyClient
    
    try:
        tavily_client = TavilyClient(api_key="your-tavily-api-key")
        query = extract_search_query(state["messages"][-1].content)
        
        if query:
            response = tavily_client.search(
                query=query,
                search_depth="basic",
                max_results=3,
                include_answer=True
            )
            
            if response.get("answer"):
                results = f"搜索摘要: {response['answer']}"
            elif response.get("results"):
                results_list = response["results"][:2]
                results = "搜索结果:\n"
                for i, result in enumerate(results_list, 1):
                    title = result.get("title", "无标题")
                    content = result.get("content", "无内容")[:200]
                    results += f"{i}. {title}: {content}...\n"
            else:
                results = "未找到相关信息"
            
            return {
                "search_results": results,
                "messages": [AIMessage(content=f"我找到了相关信息：{results[:200]}...")]
            }
        
        return {"messages": [AIMessage(content="抱歉，我没有理解你的搜索需求")]}
        
    except Exception as e:
        return {"messages": [AIMessage(content=f"搜索出错: {str(e)}")]}

def extract_search_query(message: str) -> str:
    """从消息中提取搜索查询"""
    # 简单实现：寻找关键词
    search_keywords = ["搜索", "查询", "找", "天气", "新闻", "信息"]
    for keyword in search_keywords:
        if keyword in message:
            return message
    return ""
```

## 3.3 边（Edge）：顺序、分支与循环

### 边的作用

如果说节点是"做什么"，那么边就是"接下来做什么"。边定义了图中的执行流程，决定了状态如何在不同节点间流转。

### 边的类型

**1. 普通边（Normal Edge）**

普通边表示无条件的转换，总是从一个节点跳转到另一个节点。

```python
from langgraph.graph import StateGraph, END

# 假设已经创建了状态图
graph = StateGraph(State)

# 创建普通边
graph.add_edge("node_a", "node_b")  # 从 node_a 总是跳转到 node_b
graph.add_edge("node_b", END)  # 从 node_b 总是结束

# 执行流程：node_a → node_b → END
```

**2. 条件边（Conditional Edge）**

条件边根据当前状态动态决定下一个节点，实现分支逻辑。

```python
def routing_function(state: State) -> str:
    """路由函数：根据状态决定下一个节点"""
    last_message = state["messages"][-1].content.lower()
    
    if "天气" in last_message:
        return "weather_node"
    elif "新闻" in last_message:
        return "news_node"
    else:
        return "chat_node"

# 添加条件边
graph.add_conditional_edges(
    "classifier",  # 从哪个节点开始
    routing_function,  # 路由函数
    {  # 路由映射（可选）
        "weather_node": "weather_handler",
        "news_node": "news_handler",
        "chat_node": "chat_handler"
    }
)
```

## 3.4 条件边（Conditional Edge）：让机器人自己决定下一步

条件边是 LangGraph 最强大的特性之一，它让你的应用能够根据当前情况智能地选择执行路径。

### 条件边的工作原理

```python
def my_routing_function(state: State) -> str:
    """
    路由函数的基本结构：
    1. 分析当前状态
    2. 应用决策逻辑
    3. 返回下一个节点的名称
    """
    # 1. 获取状态信息
    last_message = state["messages"][-1].content
    user_intent = state.get("intent", "unknown")
    
    # 2. 决策逻辑
    if "紧急" in last_message:
        return "urgent_handler"
    elif user_intent == "question":
        return "qa_handler"
    else:
        return "general_handler"

# 使用条件边
graph.add_conditional_edges(
    "classifier",  # 源节点
    my_routing_function,  # 路由函数
    # 可选：路由映射
    {
        "urgent_handler": "urgent_response",
        "qa_handler": "question_answering",
        "general_handler": "general_chat"
    }
)
```

### 高级路由策略

**1. 基于置信度的路由**

```python
def confidence_based_routing(state: State) -> str:
    """基于置信度的智能路由"""
    confidence = state.get("confidence", 0.0)
    intent = state.get("intent", "unknown")
    
    # 高置信度：直接处理
    if confidence > 0.8:
        return f"{intent}_handler"
    # 中等置信度：需要确认
    elif confidence > 0.5:
        return "confirmation_node"
    # 低置信度：需要澄清
    else:
        return "clarification_node"
```

## 3.5 持久化初探（Checkpointer）：状态保留的基石

### 为什么需要持久化？

想象一下，你和朋友聊天聊到一半，突然断网了。重新连接后，你们还能接着之前的话题继续聊，这就是"持久化"的作用。在 LangGraph 中，Checkpointer 就是这样的机制，它确保你的应用状态不会因为程序重启或异常而丢失。

```python
# 没有持久化：每次都是全新开始
app = graph.compile()
result1 = app.invoke({"messages": [HumanMessage("你好")]})
result2 = app.invoke({"messages": [HumanMessage("我刚才说了什么？")]})
# result2 不知道 result1 的内容

# 有持久化：能够记住历史
from langgraph.checkpoint.memory import MemorySaver

memory = MemorySaver()
app = graph.compile(checkpointer=memory)

config = {"configurable": {"thread_id": "conversation_1"}}
result1 = app.invoke({"messages": [HumanMessage("你好")]}, config=config)
result2 = app.invoke({"messages": [HumanMessage("我刚才说了什么？")]}, config=config)
# result2 能够访问 result1 的对话历史
```

### 内存持久化：快速开始

最简单的持久化方式是使用内存存储：

```python
from langgraph.checkpoint.memory import MemorySaver

def create_memory_chatbot():
    """创建带内存的聊天机器人"""
    # 创建内存检查点保存器
    memory = MemorySaver()
    
    # 编译图时指定检查点保存器
    app = graph.compile(checkpointer=memory)
    return app

def chat_with_memory():
    """带记忆的聊天"""
    app = create_memory_chatbot()
    
    # 使用线程ID来区分不同的对话
    config = {"configurable": {"thread_id": "my_conversation"}}
    
    print("开始对话（输入 'quit' 退出）:")
    while True:
        user_input = input("你: ").strip()
        if user_input.lower() == 'quit':
            break
        
        # 每次调用都使用相同的 thread_id
        result = app.invoke(
            {"messages": [HumanMessage(content=user_input)]},
            config=config
        )
        print(f"机器人: {result['messages'][-1].content}")
```

**内存持久化的特点：**
- 简单易用，无需额外配置
- 性能好，读写速度快
- 程序重启后数据丢失
- 不适合生产环境

### 文件持久化：简单可靠

对于需要跨程序运行保持状态的场景，可以使用 SQLite 文件存储：

```python
# 对于需要跨程序运行保持状态的场景，可以考虑使用 SQLite 文件存储
# 需要先安装：uv add langgraph-checkpoint-sqlite

from langgraph_checkpoint_sqlite import SqliteSaver

def create_persistent_chatbot():
    """创建持久化聊天机器人"""
    # 使用 SQLite 文件存储
    memory = SqliteSaver.from_conn_string("chatbot_memory.db")
    app = graph.compile(checkpointer=memory)
    return app

# 简化版本：使用内存存储
def create_simple_chatbot():
    """创建简单的内存持久化聊天机器人"""
    from langgraph.checkpoint.memory import MemorySaver
    memory = MemorySaver()
    app = graph.compile(checkpointer=memory)
    return app
```

---

**本章小结**

在这一章中，我们深入探讨了 LangGraph 的四个核心概念：

- **状态（State）**：应用的"记忆"，存储所有重要信息
- **节点（Node）**：执行具体任务的"工作单元"
- **边（Edge）**：控制执行流程的"路由器"
- **持久化（Checkpointer）**：保证状态不丢失的"保险箱"

这些概念相互配合，构成了 LangGraph 强大而灵活的架构。理解了这些基础概念，你就掌握了构建复杂 AI 应用的核心技能。

**下一步预告：**

在第4章中，我们将学习如何让机器人"动手"——集成各种工具，让你的 AI 助手能够搜索信息、调用 API、执行计算等实际操作。准备好让你的机器人变得更加实用了吗？